PODS:
  - AMap3DMap (8.1.0):
    - AMapFoundation (~> 1.6.9)
  - amap_flutter_map (0.0.1):
    - AMap3DMap
    - Flutter
  - AMapFoundation (1.6.9)
  - Flutter (1.0.0)
  - "permission_handler (5.1.0+2)":
    - Flutter

DEPENDENCIES:
  - amap_flutter_map (from `.symlinks/plugins/amap_flutter_map/ios`)
  - Flutter (from `Flutter`)
  - permission_handler (from `.symlinks/plugins/permission_handler/ios`)

SPEC REPOS:
  trunk:
    - AMap3DMap
    - AMapFoundation

EXTERNAL SOURCES:
  amap_flutter_map:
    :path: ".symlinks/plugins/amap_flutter_map/ios"
  Flutter:
    :path: Flutter
  permission_handler:
    :path: ".symlinks/plugins/permission_handler/ios"

SPEC CHECKSUMS:
  AMap3DMap: 135eaaf64a29aeb2f0bca07670db26fbf3271245
  amap_flutter_map: 979e54d227cedac6c7504a2151bfbf3bcf96760a
  AMapFoundation: 8d8ecbb0b2e9ce5487995360d26c885d94642bfd
  Flutter: 434fef37c0980e73bb6479ef766c45957d4b510c
  permission_handler: ccb20a9fad0ee9b1314a52b70b76b473c5f8dab0

PODFILE CHECKSUM: 8e679eca47255a8ca8067c4c67aab20e64cb974d

COCOAPODS: 1.10.0
