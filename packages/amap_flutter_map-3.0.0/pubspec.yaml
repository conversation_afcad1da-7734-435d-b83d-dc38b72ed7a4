name: amap_flutter_map
description: 高德地图SDK Flutter插件
version: 3.0.0
homepage: https://lbs.amap.com/

environment:
  sdk: ">=2.12.0 <3.0.0"
  flutter: ">=1.20.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_plugin_android_lifecycle: ^2.0.1
  meta: ^1.3.0
  plugin_platform_interface: ^2.0.0
  stream_transform: ^2.0.0

  amap_flutter_base: ^3.0.0
#  amap_flutter_base:
#    path: ../amap_flutter_base
dev_dependencies:
  flutter_test:
    sdk: flutter
  # TODO(iskakaushik): The following dependencies can be removed once
  # https://github.com/dart-lang/pub/issues/2101 is resolved.
  flutter_driver:
    sdk: flutter
  test: ^1.16.5
  pedantic: ^1.11.0
  mockito: ^5.0.0-nullsafety.7

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The 'pluginClass' and Android 'package' identifiers should not ordinarily
  # be modified. They are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    platforms:
      android:
        package: com.amap.flutter.map
        pluginClass: AMapFlutterMapPlugin
      ios:
        pluginClass: AMapFlutterMapPlugin

  # To add assets to your plugin package, add an assets section, like this:
  assets:
    - res/
    # - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
