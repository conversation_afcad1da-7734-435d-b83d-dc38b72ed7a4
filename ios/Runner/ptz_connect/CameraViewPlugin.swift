import Flutter
import UIKit
import AVFoundation
import SwiftUI

public class CameraViewPlugin: NSObject, FlutterPlugin {
    private var methodChannel: FlutterMethodChannel?
    private var cameraManager: CameraManager?
    private var trackManager: TrackManager?
    
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "camera_view", binaryMessenger: registrar.messenger())
        let instance = CameraViewPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
        instance.methodChannel = channel
        
        // 注册原生视图工厂
        let factory = CameraViewFactory(messenger: registrar.messenger())
        registrar.register(factory, withId: "camera_view")
    }
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "initialize":
            initializeCamera(result: result)
        case "startRecording":
            guard let arguments = call.arguments as? [String: Any] else {
                result(FlutterError(code: "INVALID_ARGUMENTS", message: "参数格式错误", details: nil))
                return
            }
            cameraManager?.folderName = arguments["folderName"] as? String ?? "unknown"
            startRecording(result: result)
        case "stopRecording":
            stopRecording(result: result)
        case "pauseRecording":
            pausedRecording(result: result)
        case "continueRecording":
            continueRecording(result: result)
        case "takePhoto":
            takePhoto(result: result)
        case "setResolution":
            setResolution(call: call, result: result)
        case "setFrameRate":
            setFrameRate(call: call, result: result)
        case "setPortraitMode":
            setPortraitMode(call: call, result: result)
        case "startTracking":
            startTracking(result: result)
        case "stopTracking":
            stopTracking(result: result)
        case "clearScores":
            clearScores(result: result)
        case "onBackPressed":
            onBackPressed(result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func initializeCamera(result: @escaping FlutterResult) {
        if cameraManager == nil {
            cameraManager = CameraManager()
            trackManager = TrackManager.shared
        }
        result(nil)
    }
    
    private func startRecording(result: @escaping FlutterResult) {
        // 开始录制
        cameraManager?.toggleRecording()
        if !(trackManager?.isTracking ?? false) {
            trackManager?.startTracking()
        }
        result(nil)
    }
    
    private func stopRecording(result: @escaping FlutterResult) {
        if (trackManager?.isTracking ?? false) {
            trackManager?.stopTracking()
        }
        DispatchQueue.global(qos: .userInitiated).async {
            self.cameraManager?.stopRecording()
        }
        
        result(nil)
    }
    private func pausedRecording(result: @escaping FlutterResult) {
//        cameraManager?.togglePaused()
        result(nil)
    }
    private func continueRecording(result: @escaping FlutterResult) {
//        cameraManager?.togglePaused()
        result(nil)
    }
    private func takePhoto(result: @escaping FlutterResult) {
        cameraManager?.capturePhoto()
        result(nil)
    }
    
    private func setResolution(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let resolution = args["resolution"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
            return
        }
        
        let preset: AVCaptureSession.Preset
        switch resolution {
        case "hd720":
            preset = .hd1280x720
        case "hd1080":
            preset = .hd1920x1080
        case "hd4k":
            preset = .hd4K3840x2160
        default:
            preset = .hd1920x1080
        }
        
        cameraManager?.currentResolution = preset
        result(nil)
    }
    
    private func setFrameRate(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let frameRate = args["frameRate"] as? Int else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
            return
        }
        
        cameraManager?.frameRate = frameRate
        result(nil)
    }
    
    private func setPortraitMode(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let isPortrait = args["isPortrait"] as? Bool else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
            return
        }
        
        cameraManager?.isPortraitMode = isPortrait
        result(nil)
    }
    
    private func startTracking(result: @escaping FlutterResult) {
        trackManager?.startTracking()
        result(nil)
    }
    
    private func stopTracking(result: @escaping FlutterResult) {
        trackManager?.stopTracking()
        result(nil)
    }
    
    private func clearScores(result: @escaping FlutterResult) {
        trackManager?.clearScore()
        result(nil)
    }
    
    private func onBackPressed(result: @escaping FlutterResult) {
        // 发送返回事件给Flutter
        methodChannel?.invokeMethod("onBackPressed", arguments: nil)
        result(nil)
    }
}

// MARK: - CameraViewFactory
class CameraViewFactory: NSObject, FlutterPlatformViewFactory {
    private var messenger: FlutterBinaryMessenger
    
    init(messenger: FlutterBinaryMessenger) {
        self.messenger = messenger
        super.init()
    }
    
    func create(withFrame frame: CGRect, viewIdentifier viewId: Int64, arguments args: Any?) -> FlutterPlatformView {
        return CameraViewNative(frame: frame, viewIdentifier: viewId, arguments: args, binaryMessenger: messenger)
    }
    
    func createArgsCodec() -> FlutterMessageCodec & NSObjectProtocol {
        return FlutterStandardMessageCodec()
    }
}

// MARK: - CameraViewNative
class CameraViewNative: NSObject, FlutterPlatformView {
    private var _view: UIView
    private var methodChannel: FlutterMethodChannel?
    
    init(frame: CGRect, viewIdentifier viewId: Int64, arguments args: Any?, binaryMessenger messenger: FlutterBinaryMessenger?) {
        _view = UIView()
        super.init()
        
        // 创建方法通道
        if let messenger = messenger {
            methodChannel = FlutterMethodChannel(name: "camera_view", binaryMessenger: messenger)
        }
        
        setupNativeCameraView()
    }
    
    func view() -> UIView {
        return _view
    }
    
    private func setupNativeCameraView() {
        _view.backgroundColor = .black
        
        // 立即创建SwiftUI视图，因为预览层已经预初始化
        let nativeCameraView = NativeCameraView(
            gimbalManager: GimbalManager.shared,
            trackManager: TrackManager.shared,
            onBack: { [weak self] in
                // 调用Flutter方法通知返回事件
                if let methodChannel = self?.methodChannel {
                    methodChannel.invokeMethod("onBackPressed", arguments: nil)
                }
            }
        )
        let hostingController = UIHostingController(rootView: nativeCameraView)
        hostingController.view.backgroundColor = UIColor.clear
        
        // 添加为子视图控制器
        if let parentVC = UIApplication.shared.windows.first?.rootViewController {
            parentVC.addChild(hostingController)
            hostingController.didMove(toParent: parentVC)
        }
        
        // 设置约束
        hostingController.view.translatesAutoresizingMaskIntoConstraints = false
        _view.addSubview(hostingController.view)
        
        NSLayoutConstraint.activate([
            hostingController.view.topAnchor.constraint(equalTo: _view.topAnchor),
            hostingController.view.leadingAnchor.constraint(equalTo: _view.leadingAnchor),
            hostingController.view.trailingAnchor.constraint(equalTo: _view.trailingAnchor),
            hostingController.view.bottomAnchor.constraint(equalTo: _view.bottomAnchor)
        ])
    }
}
