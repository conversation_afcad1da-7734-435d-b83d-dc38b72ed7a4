//
//  CameraView.swift
//  shootz-camera
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 9/20/25.
//

import AVFoundation
import Combine
import CoreBluetooth
import SwiftUI

struct CameraPreview: UIViewRepresentable {
    @ObservedObject var cameraManager: CameraManager
    
    func makeUIView(context: Context) -> UIView {
        let view = UIView()
        view.backgroundColor = .black
        return view
    }

    func updateUIView(_ uiView: UIView, context: Context) {
        print("CameraPreview updateUIView called, bounds: \(uiView.bounds)")
        print("CameraManager session running: \(cameraManager.session.isRunning)")
        print("CameraManager preview exists: \(cameraManager.preview != nil)")

        // 确保预览层存在且只添加一次
        if let previewLayer = cameraManager.preview {
            print("Preview layer found, frame: \(previewLayer.frame)")
            print("Preview layer connection: \(previewLayer.connection != nil)")
            print("Preview layer session: \(previewLayer.session != nil)")

            // 检查是否已经添加过预览层
            let hasPreviewLayer =
                uiView.layer.sublayers?.contains { $0 is AVCaptureVideoPreviewLayer } ?? false

            if !hasPreviewLayer {
                print("Adding preview layer to view")
                // 设置预览层属性
                previewLayer.videoGravity = .resizeAspectFill
                previewLayer.backgroundColor = UIColor.black.cgColor

                // 确保预览层在正确的层级
                uiView.layer.insertSublayer(previewLayer, at: 0)
                print("Preview layer added")
            }

            // 始终更新frame，确保尺寸正确
            if uiView.bounds.width > 0 && uiView.bounds.height > 0 {
                previewLayer.frame = uiView.bounds
                print("Preview layer frame updated to: \(previewLayer.frame)")
            } else {
                print("UIView bounds is zero: \(uiView.bounds)------\(previewLayer.bounds) --- \(UIDevice.current.orientation), waiting for proper size")
                // 强制设置一个最小尺寸，避免预览层为0
               // if previewLayer.frame.width == 0.0 || previewLayer.frame.height == 0.0 {
                    //previewLayer.frame = CGRect(x: 0, y: 0, width: 375, height: 812)
                previewLayer.frame = CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
            }
        } else {
            print("Preview layer is nil!")
        }

        // 更新网格线
        updateGridLines(in: uiView)
    }

    private func updateGridLines(in view: UIView) {
        // 移除旧的网格线
        view.subviews.forEach { subview in
            if subview.tag == 999 {  // 网格线标签
                subview.removeFromSuperview()
            }
        }

        // 如果网格线开启，添加网格线
        if cameraManager.showGridLines {
            let gridView = createGridView(frame: view.bounds)
            gridView.tag = 999
            view.addSubview(gridView)
        }
    }

    private func createGridView(frame: CGRect) -> UIView {
        let gridView = UIView(frame: frame)
        gridView.backgroundColor = .clear
        gridView.isUserInteractionEnabled = false

        // 创建网格线
        let lineWidth: CGFloat = 1.0
        let lineColor = UIColor.white.withAlphaComponent(0.5)

        // 垂直线
        for i in 1...2 {
            let x = frame.width * CGFloat(i) / 3
            let verticalLine = UIView(
                frame: CGRect(x: x, y: 0, width: lineWidth, height: frame.height))
            verticalLine.backgroundColor = lineColor
            gridView.addSubview(verticalLine)
        }

        // 水平线
        for i in 1...2 {
            let y = frame.height * CGFloat(i) / 3
            let horizontalLine = UIView(
                frame: CGRect(x: 0, y: y, width: frame.width, height: lineWidth))
            horizontalLine.backgroundColor = lineColor
            gridView.addSubview(horizontalLine)
        }

        return gridView
    }
}

struct NativeCameraView: View {
    @ObservedObject private var cameraManager = CameraManager.shared
    @ObservedObject var gimbalManager: GimbalManager
    @ObservedObject var trackManager: TrackManager
    @State private var showingSettings = false
    @State private var showingResolutions = false
    @State private var showingGimbalStatus = false
    @State private var showingLimitSettings = false
    @State private var isCameraReady = false
    @State private var isInitializing = true
//    @State private var initializationMessage = "云台初始化中..."
    @State private var isSavingVideo = false

    // 引导弹框状态
    @State private var showingGuide1 = false  // 效果图弹框
    @State private var showingGuide2 = false  // 云台摆放位置示意图
    @State private var showingGuide3 = false  // 提示设置左右限位
    @State private var showingGuide4 = false  // 提示向左转动云台
    @State private var showingGuide5 = false  // 提示向右移动云台
    @State private var currentGuideStep = 0

    // 横屏引导弹框状态
    @State private var showingLandscapeGuide1 = false  // 横屏效果图弹框
    @State private var showingLandscapeGuide2 = false  // 横屏云台摆放位置示意图
    @State private var showingLandscapeGuide3 = false  // 横屏提示设置左右限位
    @State private var showingLandscapeGuide4 = false  // 横屏提示向左转动云台
    @State private var showingLandscapeGuide5 = false  // 横屏提示向右移动云台
    @State private var currentLandscapeGuideStep = 0

    // 返回回调
    var onBack: (() -> Void)?

    init(
        gimbalManager: GimbalManager,
        trackManager: TrackManager,
        onBack: (() -> Void)? = nil
    ) {
        self.gimbalManager = gimbalManager
        self.trackManager = trackManager
        self.onBack = onBack
    }

    // 初始化相机和追踪系统
    private func initializeCameraAndTracking() {
        print("开始初始化相机...")

        // 显示初始化提示
//        initializationMessage = "相机初始化中..."

        // 异步初始化相机，避免阻塞UI
        DispatchQueue.global(qos: .userInitiated).async {
            print("Starting camera initialization...")
            cameraManager.initializeCamera()

            // 设置帧处理回调 - TrackManager将在云台连接成功后初始化
            cameraManager.frameHandler = { pixelBuffer in
                trackManager.handleFrame(pixelBuffer)
            }

            // 相机初始化完成后，检查session状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                if cameraManager.session.isRunning {
                    print("Camera session is running after initialization")
                    self.isCameraReady = true
                }
            }
        }

        // 短暂延迟后隐藏初始化界面
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.isInitializing = false
            print("相机初始化完成")
        }
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Camera Preview
                if isCameraReady {
                    CameraPreview(cameraManager: cameraManager)
                        .ignoresSafeArea()
                } else {
                    // 初始化状态
                    Color.black
                        .ignoresSafeArea()
//                        .overlay(
//                            VStack(spacing: 20) {
//                                // 进度指示器
//                                VStack(spacing: 16) {
//                                    ProgressView()
//                                        .progressViewStyle(
//                                            CircularProgressViewStyle(tint: .white)
//                                        )
//                                        .scaleEffect(1.5)
//
//                                    Text(initializationMessage)
//                                        .foregroundColor(.white)
//                                        .font(.system(size: 18, weight: .medium))
//                                }
//                                .padding()
//                                .background(
//                                    RoundedRectangle(cornerRadius: 16)
//                                        .fill(Color.black.opacity(0.7))
//                                        .overlay(
//                                            RoundedRectangle(cornerRadius: 16)
//                                                .stroke(Color.white.opacity(0.2), lineWidth: 1)
//                                        )
//                                )
//                                .padding(.horizontal, 40)
//                            }
//                        )
                }
            }
        }
        .onAppear {
            print("NativeCameraView onAppear called")

            // 开始初始化流程
            initializeCameraAndTracking()

            // 使用定时器检查相机状态
            Timer.scheduledTimer(withTimeInterval: 0.3, repeats: true) { timer in
                print("Checking camera status...")
                print("Camera session running: \(cameraManager.session.isRunning)")
                print("isCameraReady: \(self.isCameraReady)")

                if cameraManager.session.isRunning {
                    print("Camera session is running, showing preview")
                    self.isCameraReady = true
                    timer.invalidate()
                } else if self.isInitializing == false {
                    // 如果初始化界面已经隐藏但相机还没启动，强制显示相机
                    print("Forcing camera ready after initialization timeout")
                    self.isCameraReady = true
                    timer.invalidate()
                }

                // 避免无限检查，最多检查10次
                if timer.fireDate.timeIntervalSinceNow < -3.0 {
                    print("Camera check timeout, forcing ready")
                    self.isCameraReady = true
                    timer.invalidate()
                }
            }
        }
        .onDisappear {
            cameraManager.stopSession()
        }
        .alert(isPresented: $cameraManager.showAlert) {
            Alert(
                title: Text("提示"),
                message: Text(cameraManager.alertMessage),
                dismissButton: .default(Text("确定"))
            )
        }
        .overlay(
            // 视频存储提示框
            Group {
                if isSavingVideo {
                    ZStack {
                        Color.black.opacity(0.7)
                            .ignoresSafeArea()

                        VStack(spacing: 20) {
                            ProgressView()
                                .progressViewStyle(
                                    CircularProgressViewStyle(tint: .white)
                                )
                                .scaleEffect(1.5)

                            Text("视频存储中...")
                                .foregroundColor(.white)
                                .font(.system(size: 18, weight: .medium))

                            Text("请稍候，正在保存到相册")
                                .foregroundColor(.white.opacity(0.8))
                                .font(.system(size: 14))
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.black.opacity(0.8))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                )
                        )
                        .padding(.horizontal, 40)
                    }
                }
            }
        )
        .onTapGesture {
            if showingSettings {
                showingSettings = false
            }
            if showingGimbalStatus {
                showingGimbalStatus = false
            }
        }
        .sheet(isPresented: $showingGimbalStatus) {
            GimbalStatusView(gimbalManager: gimbalManager)
        }
        .sheet(isPresented: $showingLimitSettings) {
            LimitSettingsView(gimbalManager: gimbalManager)
        }
        
        // 锁定屏幕旋转为竖屏
        .statusBar(hidden: true)
        .preferredColorScheme(.dark)
    }
}

struct SettingsDropdownView: View {
    @ObservedObject var cameraManager: CameraManager
    let isPortraitMode: Bool
    let onLimitSettingsTap: () -> Void
    @State private var showingFocusModePicker = false
    @State private var showingExposurePicker = false

    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            // 对焦模式
            Button(action: {
                showingFocusModePicker = true
            }) {
                HStack {
                    Image(systemName: "camera.metering.spot")
                    Text("对焦模式")
                    Spacer()
                    Text(cameraManager.getFocusModeDisplayName())
                        .foregroundColor(.gray)
                    Image(systemName: "chevron.right")
                        .foregroundColor(.gray)
                        .font(.caption)
                }
                .foregroundColor(.white)
            }
            .actionSheet(isPresented: $showingFocusModePicker) {
                ActionSheet(
                    title: Text("选择对焦模式"),
                    buttons: [
                        .default(Text("连续自动")) {
                            cameraManager.setFocusMode(.continuousAutoFocus)
                        },
                        .default(Text("自动")) {
                            cameraManager.setFocusMode(.autoFocus)
                        },
                        .default(Text("锁定")) {
                            cameraManager.setFocusMode(.locked)
                        },
                        .cancel(),
                    ]
                )
            }

            Divider()
                .background(Color.white.opacity(0.3))

            // 曝光补偿
            Button(action: {
                showingExposurePicker = true
            }) {
                HStack {
                    Image(systemName: "sun.max")
                    Text("曝光补偿")
                    Spacer()
                    Text(String(format: "%.1f", cameraManager.exposureBias))
                        .foregroundColor(.gray)
                    Image(systemName: "chevron.right")
                        .foregroundColor(.gray)
                        .font(.caption)
                }
                .foregroundColor(.white)
            }
            .actionSheet(isPresented: $showingExposurePicker) {
                ActionSheet(
                    title: Text("选择曝光补偿"),
                    buttons: [
                        .default(Text("-2.0")) {
                            cameraManager.setExposureBias(-2.0)
                        },
                        .default(Text("-1.0")) {
                            cameraManager.setExposureBias(-1.0)
                        },
                        .default(Text("0.0")) {
                            cameraManager.setExposureBias(0.0)
                        },
                        .default(Text("+1.0")) {
                            cameraManager.setExposureBias(1.0)
                        },
                        .default(Text("+2.0")) {
                            cameraManager.setExposureBias(2.0)
                        },
                        .cancel(),
                    ]
                )
            }

            Divider()
                .background(Color.white.opacity(0.3))

            // 网格线
            Button(action: {
                cameraManager.toggleGridLines()
            }) {
                HStack {
                    Image(systemName: "square.grid.3x3")
                    Text("网格线")
                    Spacer()
                    Text(cameraManager.showGridLines ? "开启" : "关闭")
                        .foregroundColor(.gray)
                    Image(systemName: cameraManager.showGridLines ? "checkmark" : "xmark")
                        .foregroundColor(cameraManager.showGridLines ? .green : .gray)
                        .font(.caption)
                }
                .foregroundColor(.white)
            }

            Divider()
                .background(Color.white.opacity(0.3))

            // 限位设置
            Button(action: onLimitSettingsTap) {
                HStack {
                    Image(systemName: "arrow.left.and.right")
                    Text("限位设置")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .foregroundColor(.gray)
                }
                .foregroundColor(.white)
            }
        }
    }
}

struct GimbalStatusView: View {
    @ObservedObject var gimbalManager: GimbalManager
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Connection Status
                VStack(spacing: 16) {
                    Image(
                        systemName: gimbalManager.connectionStatus == .connected
                            ? "antenna.radiowaves.left.and.right"
                            : "antenna.radiowaves.left.and.right.slash"
                    )
                    .font(.system(size: 50))
                    .foregroundColor(gimbalManager.connectionStatus.color)

                    Text(gimbalManager.connectionStatus.description)
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(gimbalManager.connectionStatus.color)
                }

                // Device Info
                if let device = gimbalManager.connectedDevice {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("设备信息")
                            .font(.headline)

                        InfoRow(title: "设备名称", value: device.name)
                        InfoRow(title: "MAC地址", value: device.macAddress)
                        InfoRow(title: "信号强度", value: "\(device.rssi) dBm")

                        if !device.serial.isEmpty {
                            InfoRow(title: "序列号", value: device.serial)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                }

                if !gimbalManager.errorMessage.isEmpty {
                    Text(gimbalManager.errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                }

                Spacer()

                // Action Buttons
                VStack(spacing: 12) {
                    if gimbalManager.connectionStatus == .connected {
                        HStack(spacing: 12) {
                            Button("居中") {
                                gimbalManager.gimbalCentering()
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(12)

                            Button("左转") {
                                gimbalManager.gimbalTurnLeft()
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(12)

                            Button("右转") {
                                gimbalManager.gimbalTurnRight()
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }

                        Button("断开连接") {
                            gimbalManager.disconnect()
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    } else {
                        Text("云台未连接")
                            .font(.headline)
                            .foregroundColor(.gray)

                        Text("请返回权限设置页面重新连接云台")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                    }
                }
            }
            .padding()
            .navigationTitle("云台状态")
            .navigationBarItems(
                trailing: Button("关闭") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
    }
}

struct InfoRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.gray)
            Spacer()
            Text(value)
                .fontWeight(.medium)
        }
    }
}

struct LimitSettingsView: View {
    @ObservedObject var gimbalManager: GimbalManager
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部标题栏
                HStack {
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.primary)
                    }

                    Spacer()

                    Text("限位设置")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.primary)

                    Spacer()

                    // 占位符，保持标题居中
                    Color.clear
                        .frame(width: 24, height: 24)
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)
                .padding(.bottom, 20)

                // 内容区域
                ScrollView {
                    VStack(spacing: 24) {
                        // 设置限位区域
                        VStack(spacing: 16) {
                            HStack {
                                Image(systemName: "target")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.blue)
                                Text("设置限位")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.primary)
                                Spacer()
                            }

                            HStack(spacing: 12) {
                                Button(action: {
                                    gimbalManager.setLeftLimit()
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "arrow.left")
                                            .font(.system(size: 14, weight: .medium))
                                        Text("左限位")
                                            .font(.system(size: 14, weight: .medium))
                                    }
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 12)
                                    .background(Color.blue)
                                    .cornerRadius(8)
                                }

                                Button(action: {
                                    gimbalManager.setRightLimit()
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "arrow.right")
                                            .font(.system(size: 14, weight: .medium))
                                        Text("右限位")
                                            .font(.system(size: 14, weight: .medium))
                                    }
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 12)
                                    .background(Color.blue)
                                    .cornerRadius(8)
                                }
                            }
                        }
                        .padding(16)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)

                        // 移动到限位区域
                        VStack(spacing: 16) {
                            HStack {
                                Image(systemName: "arrow.triangle.turn.up.right.diamond")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.green)
                                Text("移动到限位")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.primary)
                                Spacer()
                            }

                            HStack(spacing: 12) {
                                Button(action: {
                                    gimbalManager.goToLeftLimit()
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "arrow.left.square")
                                            .font(.system(size: 14, weight: .medium))
                                        Text("到左限位")
                                            .font(.system(size: 14, weight: .medium))
                                    }
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 12)
                                    .background(Color.green)
                                    .cornerRadius(8)
                                }

                                Button(action: {
                                    gimbalManager.goToRightLimit()
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "arrow.right.square")
                                            .font(.system(size: 14, weight: .medium))
                                        Text("到右限位")
                                            .font(.system(size: 14, weight: .medium))
                                    }
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 12)
                                    .background(Color.green)
                                    .cornerRadius(8)
                                }
                            }
                        }
                        .padding(16)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)

                        // 限位控制区域
                        VStack(spacing: 16) {
                            HStack {
                                Image(systemName: "slider.horizontal.3")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.orange)
                                Text("限位控制")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.primary)
                                Spacer()
                            }

                            HStack(spacing: 12) {
                                Button(action: {
                                    gimbalManager.enableLimit(true)
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "checkmark")
                                            .font(.system(size: 14, weight: .medium))
                                        Text("启用")
                                            .font(.system(size: 14, weight: .medium))
                                    }
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 12)
                                    .background(Color.orange)
                                    .cornerRadius(8)
                                }

                                Button(action: {
                                    gimbalManager.enableLimit(false)
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "xmark")
                                            .font(.system(size: 14, weight: .medium))
                                        Text("停用")
                                            .font(.system(size: 14, weight: .medium))
                                    }
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 12)
                                    .background(Color.red)
                                    .cornerRadius(8)
                                }
                            }
                        }
                        .padding(16)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                }
            }
            .background(Color(.systemBackground))
            .navigationBarHidden(true)
        }
    }
}

// MARK: - 效果图弹框
struct EffectGuideView: View {
    let isPresented: Binding<Bool>
    let onAction: () -> Void

    var body: some View {
        ZStack {
            Color.clear
                .ignoresSafeArea()

            VStack(spacing: 25) {
                // 相机图标
                Image(systemName: "camera.viewfinder")
                    .font(.system(size: 100))
                    .foregroundColor(.blue)
                    .padding(.top, 30)

                VStack(spacing: 15) {
                    Text("效果图")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("竖屏模式适合拍摄半场篮球")
                        .font(.title3)
                        .foregroundColor(.blue)
                        .fontWeight(.medium)

                    Text("请确保云台已连接并开始拍摄")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                }

                Button(action: onAction) {
                    Text("开始拍摄")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [.blue, .purple]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(30)
                }
                .padding(.horizontal, 50)
                .padding(.bottom, 30)
            }
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(Color.black.opacity(0.9))
                    .overlay(
                        RoundedRectangle(cornerRadius: 25)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 2)
                    )
            )
            .padding(.horizontal, 40)
        }
    }
}

// MARK: - 云台摆放位置弹框
struct PositionGuideView: View {
    let isPresented: Binding<Bool>
    let onAction: () -> Void

    var body: some View {
        ZStack {
            Color.clear
                .ignoresSafeArea()

            VStack(spacing: 25) {
                // 位置图标
                Image(systemName: "location.circle.fill")
                    .font(.system(size: 100))
                    .foregroundColor(.green)
                    .padding(.top, 30)

                VStack(spacing: 15) {
                    Text("云台摆放位置")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("篮球场边线附近")
                        .font(.title3)
                        .foregroundColor(.green)
                        .fontWeight(.medium)

                    Text("请将云台放置在篮球场边线附近，确保能够拍摄到整个半场区域")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 10)
                }

                Button(action: onAction) {
                    Text("知道了")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [.green, .mint]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(30)
                }
                .padding(.horizontal, 50)
                .padding(.bottom, 30)
            }
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(Color.black.opacity(0.9))
                    .overlay(
                        RoundedRectangle(cornerRadius: 25)
                            .stroke(Color.green.opacity(0.3), lineWidth: 2)
                    )
            )
            .padding(.horizontal, 40)
        }
    }
}

// MARK: - 设置限位弹框
struct LimitGuideView: View {
    let isPresented: Binding<Bool>
    let onAction: () -> Void

    var body: some View {
        ZStack {
            Color.clear
                .ignoresSafeArea()

            VStack(spacing: 25) {
                // 设置图标
                Image(systemName: "gearshape.2.fill")
                    .font(.system(size: 100))
                    .foregroundColor(.orange)
                    .padding(.top, 30)

                VStack(spacing: 15) {
                    Text("设置限位")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("确保拍摄范围准确")
                        .font(.title3)
                        .foregroundColor(.orange)
                        .fontWeight(.medium)

                    Text("为了确保拍摄范围准确，需要设置云台的左右限位")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 10)
                }

                Button(action: onAction) {
                    Text("开始设置")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [.orange, .red]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(30)
                }
                .padding(.horizontal, 50)
                .padding(.bottom, 30)
            }
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(Color.black.opacity(0.9))
                    .overlay(
                        RoundedRectangle(cornerRadius: 25)
                            .stroke(Color.orange.opacity(0.3), lineWidth: 2)
                    )
            )
            .padding(.horizontal, 40)
        }
    }
}

// MARK: - 设置左限位弹框
struct LeftLimitGuideView: View {
    let isPresented: Binding<Bool>
    let onAction: () -> Void

    var body: some View {
        ZStack {
            Color.clear
                .ignoresSafeArea()

            VStack(spacing: 25) {
                // 左箭头图标
                Image(systemName: "arrow.left.circle.fill")
                    .font(.system(size: 100))
                    .foregroundColor(.cyan)
                    .padding(.top, 30)

                VStack(spacing: 15) {
                    Text("设置左限位")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("向左转动云台")
                        .font(.title3)
                        .foregroundColor(.cyan)
                        .fontWeight(.medium)

                    Text("请向左转动云台到合适位置，然后点击下一步设置左限位")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 10)
                }

                Button(action: onAction) {
                    Text("下一步")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [.cyan, .blue]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(30)
                }
                .padding(.horizontal, 50)
                .padding(.bottom, 30)
            }
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(Color.black.opacity(0.9))
                    .overlay(
                        RoundedRectangle(cornerRadius: 25)
                            .stroke(Color.cyan.opacity(0.3), lineWidth: 2)
                    )
            )
            .padding(.horizontal, 40)
        }
    }
}

// MARK: - 设置右限位弹框
struct RightLimitGuideView: View {
    let isPresented: Binding<Bool>
    let onAction: () -> Void

    var body: some View {
        ZStack {
            Color.clear
                .ignoresSafeArea()

            VStack(spacing: 25) {
                // 右箭头图标
                Image(systemName: "arrow.right.circle.fill")
                    .font(.system(size: 100))
                    .foregroundColor(.purple)
                    .padding(.top, 30)

                VStack(spacing: 15) {
                    Text("设置右限位")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("向右移动云台")
                        .font(.title3)
                        .foregroundColor(.purple)
                        .fontWeight(.medium)

                    Text("请向右转动云台到合适位置，然后点击确定完成右限位设置")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 10)
                }

                Button(action: onAction) {
                    Text("确定")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [.purple, .pink]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(30)
                }
                .padding(.horizontal, 50)
                .padding(.bottom, 30)
            }
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(Color.black.opacity(0.9))
                    .overlay(
                        RoundedRectangle(cornerRadius: 25)
                            .stroke(Color.purple.opacity(0.3), lineWidth: 2)
                    )
            )
            .padding(.horizontal, 40)
        }
    }
}

// MARK: - 横屏引导弹框组件

// 横屏效果图弹框
struct LandscapeEffectGuideView: View {
    let isPresented: Binding<Bool>
    let onAction: () -> Void

    var body: some View {
        ZStack {
            Color.clear
                .ignoresSafeArea()

            HStack(spacing: 20) {
                // 左侧图标
                Image(systemName: "camera.viewfinder")
                    .font(.system(size: 80))
                    .foregroundColor(.blue)

                // 右侧内容
                VStack(alignment: .leading, spacing: 15) {
                    Text("横屏效果图")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("横屏模式适合拍摄全场篮球")
                        .font(.title3)
                        .foregroundColor(.blue)
                        .fontWeight(.medium)

                    Text("请确保云台已连接并开始拍摄")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.leading)

                    Button(action: onAction) {
                        Text("开始拍摄")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [.blue, .purple]
                                    ),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(25)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(.horizontal, 30)
            .padding(.vertical, 25)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.black.opacity(0.9))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 2)
                    )
            )
            .padding(.horizontal, 20)
        }
    }
}

// 横屏云台摆放位置弹框
struct LandscapePositionGuideView: View {
    let isPresented: Binding<Bool>
    let onAction: () -> Void

    var body: some View {
        ZStack {
            Color.clear
                .ignoresSafeArea()

            HStack(spacing: 20) {
                Image(systemName: "location.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.green)

                VStack(alignment: .leading, spacing: 15) {
                    Text("云台摆放位置")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("篮球场边线附近")
                        .font(.title3)
                        .foregroundColor(.green)
                        .fontWeight(.medium)

                    Text("请将云台放置在篮球场边线附近，确保能够拍摄到整个全场区域")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.leading)

                    Button(action: onAction) {
                        Text("知道了")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [.green, .mint]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(25)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(.horizontal, 30)
            .padding(.vertical, 25)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.black.opacity(0.9))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.green.opacity(0.3), lineWidth: 2)
                    )
            )
            .padding(.horizontal, 20)
        }
    }
}

// 横屏设置限位弹框
struct LandscapeLimitGuideView: View {
    let isPresented: Binding<Bool>
    let onAction: () -> Void

    var body: some View {
        ZStack {
            Color.clear
                .ignoresSafeArea()

            HStack(spacing: 20) {
                Image(systemName: "gearshape.2.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.orange)

                VStack(alignment: .leading, spacing: 15) {
                    Text("设置限位")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("确保拍摄范围准确")
                        .font(.title3)
                        .foregroundColor(.orange)
                        .fontWeight(.medium)

                    Text("为了确保拍摄范围准确，需要设置云台的左右限位")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.leading)

                    Button(action: onAction) {
                        Text("开始设置")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [.orange, .red]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(25)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(.horizontal, 30)
            .padding(.vertical, 25)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.black.opacity(0.9))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.orange.opacity(0.3), lineWidth: 2)
                    )
            )
            .padding(.horizontal, 20)
        }
    }
}

// 横屏设置左限位弹框
struct LandscapeLeftLimitGuideView: View {
    let isPresented: Binding<Bool>
    let onAction: () -> Void

    var body: some View {
        ZStack {
            Color.clear
                .ignoresSafeArea()

            HStack(spacing: 20) {
                Image(systemName: "arrow.left.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.cyan)

                VStack(alignment: .leading, spacing: 15) {
                    Text("设置左限位")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("向左转动云台")
                        .font(.title3)
                        .foregroundColor(.cyan)
                        .fontWeight(.medium)

                    Text("请向左转动云台到合适位置，然后点击下一步设置左限位")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.leading)

                    Button(action: onAction) {
                        Text("下一步")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [.cyan, .blue]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(25)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(.horizontal, 30)
            .padding(.vertical, 25)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.black.opacity(0.9))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.cyan.opacity(0.3), lineWidth: 2)
                    )
            )
            .padding(.horizontal, 20)
        }
    }
}

// 横屏设置右限位弹框
struct LandscapeRightLimitGuideView: View {
    let isPresented: Binding<Bool>
    let onAction: () -> Void

    var body: some View {
        ZStack {
            Color.clear
                .ignoresSafeArea()

            HStack(spacing: 20) {
                Image(systemName: "arrow.right.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.purple)

                VStack(alignment: .leading, spacing: 15) {
                    Text("设置右限位")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("向右移动云台")
                        .font(.title3)
                        .foregroundColor(.purple)
                        .fontWeight(.medium)

                    Text("请向右转动云台到合适位置，然后点击确定完成右限位设置")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.leading)

                    Button(action: onAction) {
                        Text("确定")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [.purple, .pink]
                                    ),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(25)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(.horizontal, 30)
            .padding(.vertical, 25)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.black.opacity(0.9))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.purple.opacity(0.3), lineWidth: 2)
                    )
            )
            .padding(.horizontal, 20)
        }
    }
}

#Preview {
    NativeCameraView(
        gimbalManager: GimbalManager.shared,
        trackManager: TrackManager.shared,
        onBack: {
            print("Preview back button pressed")
        }
    )
}
