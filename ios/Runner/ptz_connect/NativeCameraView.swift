//
//  CameraView.swift
//  shootz-camera
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 9/20/25.
//

import SwiftUI
import AVFoundation
import Combine
import CoreBluetooth

struct CameraPreview: UIViewRepresentable {
    @ObservedObject var cameraManager: CameraManager
    
    func makeUIView(context: Context) -> UIView {
        let view = UIView()
        view.backgroundColor = .black
        return view
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        // 确保预览层存在且只添加一次
        if let previewLayer = cameraManager.preview {
            // 移除旧的预览层
            uiView.layer.sublayers?.removeAll { $0 is AVCaptureVideoPreviewLayer }
            
            // 设置预览层frame并添加到视图
            previewLayer.frame = uiView.bounds
            previewLayer.backgroundColor = UIColor(hex: "#2F2F3B").cgColor
            print("!!!!!!!\(uiView.bounds)")
            previewLayer.videoGravity = .resizeAspectFill
            uiView.layer.addSublayer(previewLayer)
            
            // 确保预览层在正确的层级
            uiView.layer.insertSublayer(previewLayer, at: 0)
        }
    }
}

struct NativeCameraView: View {
    @StateObject private var cameraManager = CameraManager()
    @ObservedObject var gimbalManager: GimbalManager
    @ObservedObject var trackManager: TrackManager
    @State private var showingSettings = false
    @State private var showingTips = false
    @State private var showingResolutions = false
    @State private var showingHome = false
    @State private var showingGimbalStatus = false
    @State private var showingLimitSettings = false
    @State private var isCameraReady = false
    
    // 返回回调
    var onBack: (() -> Void)?
    
    init(gimbalManager: GimbalManager, trackManager: TrackManager, onBack: (() -> Void)? = nil) {
        self.gimbalManager = gimbalManager
        self.trackManager = trackManager
        self.onBack = onBack
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Camera Preview
                if isCameraReady {
                    CameraPreview(cameraManager: cameraManager)
                        .ignoresSafeArea()
                } else {
                    // 加载状态
                    Color.black
                        .ignoresSafeArea()
                        .overlay(
                            VStack {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(1.5)
                                Text("正在初始化相机...")
                                    .foregroundColor(.white)
                                    .padding(.top, 16)
                            }
                        )
                }
            }
        }
        .onAppear {
            // 立即显示相机预览，因为预览层已经预初始化
            isCameraReady = true
            
            // 异步初始化相机，避免阻塞UI
            DispatchQueue.global(qos: .userInitiated).async {
                cameraManager.initializeCamera()
                
                // 设置帧处理回调 - 只有在 trackManager 存在时才处理帧
                cameraManager.frameHandler = { pixelBuffer in
                    trackManager.handleFrame(pixelBuffer)
                }
            }
        }
        .onDisappear {
            cameraManager.stopSession()
        }
        .alert(isPresented: $cameraManager.showAlert) {
            Alert(title: Text("提示"), message: Text(cameraManager.alertMessage), dismissButton: .default(Text("确定")))
        }
        .onTapGesture {
            if showingSettings {
                showingSettings = false
            }
            if showingGimbalStatus {
                showingGimbalStatus = false
            }
        }
        .sheet(isPresented: $showingGimbalStatus) {
            GimbalStatusView(gimbalManager: gimbalManager)
        }
        .sheet(isPresented: $showingLimitSettings) {
            LimitSettingsView(gimbalManager: gimbalManager)
        }
        // 锁定屏幕旋转为竖屏
        .statusBar(hidden: true)
        .preferredColorScheme(.dark)
    }
}

struct SettingsDropdownView: View {
    let isPortraitMode: Bool
    let onLimitSettingsTap: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Image(systemName: "camera.metering.spot")
                Text("对焦模式")
                Spacer()
                Text("自动")
                    .foregroundColor(.gray)
            }
            .foregroundColor(.white)
            
            Divider()
                .background(Color.white.opacity(0.3))
            
            HStack {
                Image(systemName: "sun.max")
                Text("曝光补偿")
                Spacer()
                Text("0")
                    .foregroundColor(.gray)
            }
            .foregroundColor(.white)
            
            Divider()
                .background(Color.white.opacity(0.3))
            
            HStack {
                Image(systemName: "timer")
                Text("定时器")
                Spacer()
                Text("关闭")
                    .foregroundColor(.gray)
            }
            .foregroundColor(.white)
            
            Divider()
                .background(Color.white.opacity(0.3))
            
            HStack {
                Image(systemName: "square.grid.3x3")
                Text("网格线")
                Spacer()
                Text("关闭")
                    .foregroundColor(.gray)
            }
            .foregroundColor(.white)
            
            Divider()
                .background(Color.white.opacity(0.3))
            
            Button(action: onLimitSettingsTap) {
                HStack {
                    Image(systemName: "arrow.left.and.right")
                    Text("限位设置")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .foregroundColor(.gray)
                }
                .foregroundColor(.white)
            }
        }
    }
}

struct GimbalStatusView: View {
    @ObservedObject var gimbalManager: GimbalManager
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Connection Status
                VStack(spacing: 16) {
                    Image(systemName: gimbalManager.connectionStatus == .connected ? "antenna.radiowaves.left.and.right" : "antenna.radiowaves.left.and.right.slash")
                        .font(.system(size: 50))
                        .foregroundColor(gimbalManager.connectionStatus.color)
                    
                    Text(gimbalManager.connectionStatus.description)
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(gimbalManager.connectionStatus.color)
                }
                
                // Device Info
                if let device = gimbalManager.connectedDevice {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("设备信息")
                            .font(.headline)
                        
                        InfoRow(title: "设备名称", value: device.name)
                        InfoRow(title: "MAC地址", value: device.macAddress)
                        InfoRow(title: "信号强度", value: "\(device.rssi) dBm")
                        
                        if !device.serial.isEmpty {
                            InfoRow(title: "序列号", value: device.serial)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                }
                
                if !gimbalManager.errorMessage.isEmpty {
                    Text(gimbalManager.errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                }
                
                Spacer()
                
                // Action Buttons
                VStack(spacing: 12) {
                    if gimbalManager.connectionStatus == .connected {
                        HStack(spacing: 12) {
                            Button("居中") {
                                gimbalManager.gimbalCentering()
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                            
                            Button("左转") {
                                gimbalManager.gimbalTurnLeft()
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                            
                            Button("右转") {
                                gimbalManager.gimbalTurnRight()
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                        
                        Button("断开连接") {
                            gimbalManager.disconnect()
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    } else {
                        Text("云台未连接")
                            .font(.headline)
                            .foregroundColor(.gray)
                        
                        Text("请返回权限设置页面重新连接云台")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                    }
                }
            }
            .padding()
            .navigationTitle("云台状态")
            .navigationBarItems(trailing: Button("关闭") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.gray)
            Spacer()
            Text(value)
                .fontWeight(.medium)
        }
    }
}

struct HomeView: View {
    @Environment(\.presentationMode) var presentationMode
    var onBack: (() -> Void)?
    
    init(onBack: (() -> Void)? = nil) {
        self.onBack = onBack
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Image(systemName: "camera.fill")
                    .font(.system(size: 100))
                    .foregroundColor(.blue)
                
                Text("Shootz Camera")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("专业摄影应用")
                    .font(.title2)
                    .foregroundColor(.gray)
                
                VStack(spacing: 20) {
                    Button("相机模式") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                    
                    Button("相册") {
                        // TODO: Implement photo gallery
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.2))
                    .foregroundColor(.primary)
                    .cornerRadius(10)
                    
                    Button("设置") {
                        // TODO: Implement settings
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.2))
                    .foregroundColor(.primary)
                    .cornerRadius(10)
                    
                    // 返回上一页按钮
                    if let onBack = onBack {
                        Button("返回上一页") {
                            presentationMode.wrappedValue.dismiss()
                            onBack()
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                }
                .padding(.horizontal)
                
                Spacer()
            }
            .padding()
            .navigationTitle("主页")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

struct LimitSettingsView: View {
    @ObservedObject var gimbalManager: GimbalManager
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("限位设置")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.top)
                
                // 限位设置按钮区域
                VStack(spacing: 16) {
                    // 设置限位按钮
                    VStack(spacing: 12) {
                        Text("设置限位")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        HStack(spacing: 12) {
                            Button("设置左限位") {
                                gimbalManager.setLeftLimit()
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                            
                            Button("设置右限位") {
                                gimbalManager.setRightLimit()
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                    }
                    
                    Divider()
                    
                    // 到限位按钮
                    VStack(spacing: 12) {
                        Text("移动到限位")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        HStack(spacing: 12) {
                            Button("到左限位") {
                                gimbalManager.goToLeftLimit()
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                            
                            Button("到右限位") {
                                gimbalManager.goToRightLimit()
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                    }
                    
                    Divider()
                    
                    // 启用/停用限位按钮
                    VStack(spacing: 12) {
                        Text("限位控制")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        HStack(spacing: 12) {
                            Button("启用限位") {
                                gimbalManager.enableLimit(true)
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.orange)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                            
                            Button("停用限位") {
                                gimbalManager.enableLimit(false)
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.red)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                    }
                }
                .padding()
                
                Spacer()
                
                // 关闭按钮
                Button("关闭") {
                    presentationMode.wrappedValue.dismiss()
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.gray.opacity(0.2))
                .foregroundColor(.primary)
                .cornerRadius(10)
                .padding(.horizontal)
                .padding(.bottom)
            }
            .navigationBarHidden(true)
        }
    }
}

#Preview {
    NativeCameraView(
        gimbalManager: GimbalManager.shared,
        trackManager: TrackManager.shared,
        onBack: {
            print("Preview back button pressed")
        }
    )
}
// 创建 Color 扩展
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, (int >> 16) & 0xFF, (int >> 8) & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = ((int >> 24) & 0xFF, (int >> 16) & 0xFF, (int >> 8) & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 0, 0, 0)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}
