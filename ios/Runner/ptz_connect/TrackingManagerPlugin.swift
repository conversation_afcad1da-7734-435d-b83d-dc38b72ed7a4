import Flutter
import UIKit
import CoreVideo

public class TrackingManagerPlugin: NSObject, FlutterPlugin {
    private var methodChannel: FlutterMethodChannel?
    private var trackInstance: TrackManager
    private var isSDKAvailable = false
//    private var timer: Timer?
    override init() {
        self.trackInstance = TrackManager.shared
        super.init()
    }
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "tracking_manager", binaryMessenger: registrar.messenger())
        let instance = TrackingManagerPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
        instance.methodChannel = channel
//        instance.setupManagers()
    }
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "initializeSDK":
            initializeSDK(result: result)
//        case "startTracking":
//            startTracking(result: result)
//        case "stopTracking":
//            stopTracking(result: result)
//        case "clearScore":
//            clearScore(result: result)
//        case "handleFrame":
//            handleFrame(call: call, result: result)
//        case "setCameraType":
//            setCameraType(call: call, result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func initializeSDK(result: @escaping FlutterResult) {
        print("重新设置代理")
        DispatchQueue.main.async {
//            self.trackInstance = HHTrackManager(listener: self)
//            self.trackInstance?.isFrontCamera = false // 默认后置摄像头
//            self.isSDKAvailable = true
//            
//            self.methodChannel?.invokeMethod("onSDKStatusChanged", arguments: [
//                "available": true
//            ])
//
            if let connectManager = self.trackInstance.trackManager {
                connectManager.listener = self
            }
            result(nil)
        }
    }
//    
//    private func startTracking(result: @escaping FlutterResult) {
////        guard let trackManager = trackManager else {
////            result(FlutterError(code: "SDK_NOT_INITIALIZED", message: "SDK not initialized", details: nil))
////            return
////        }
//        
//        DispatchQueue.main.async {
//            TrackManager.shared.startTracking()
//            result(nil)
//        }
//    }
//    func startTimer() {
//        // 停止之前的定时器
//        stopTimer()
//        
//        // 创建新的定时器，每10秒执行一次
//        timer = Timer.scheduledTimer(timeInterval: 10.0,
//                                   target: self,
//                                   selector: #selector(timerAction),
//                                   userInfo: nil,
//                                   repeats: true)
//        
//        // 让定时器在滚动模式下也能正常工作
//        RunLoop.current.add(timer!, forMode: .common)
//        
//        print("定时器已启动，每10秒记录一次")
//    }
//    
//    func stopTimer() {
//        timer?.invalidate()
//        timer = nil
//        print("定时器已停止")
//    }
//    @objc private func timerAction() {
//        recordLog()
//    }
//    
//    private func recordLog() {
//        print("!!!!!!!!!!!!!!!onTrackingEvent")
//        let timestampMilliseconds = Int(Date().timeIntervalSince1970 * 1000)
//        DispatchQueue.main.async {
//            self.methodChannel?.invokeMethod("onTrackingEvent", arguments: [
//                "eventType": "shot",
//                "score": 1,
//                "timeStamp":timestampMilliseconds,
//                "trainingId":CameraManager.shared.trainingId
//            ])
//        }
//    }
//    
//    deinit {
//        stopTimer()
//    }
//    private func stopTracking(result: @escaping FlutterResult) {
////        guard let trackManager = trackManager else {
////            result(FlutterError(code: "SDK_NOT_INITIALIZED", message: "SDK not initialized", details: nil))
////            return
////        }
//        
//        DispatchQueue.main.async {
//            TrackManager.shared.stopTracking()
//            result(nil)
//        }
//    }
//    
//    private func clearScore(result: @escaping FlutterResult) {
//        guard let trackInstance = trackManager else {
//            result(FlutterError(code: "SDK_NOT_INITIALIZED", message: "SDK not initialized", details: nil))
//            return
//        }
//        
//        DispatchQueue.main.async {
//            trackManager.clearScore()
//            result(nil)
//        }
//    }
//    
//    private func handleFrame(call: FlutterMethodCall, result: @escaping FlutterResult) {
//        guard let trackManager = trackManager else {
//            result(FlutterError(code: "SDK_NOT_INITIALIZED", message: "SDK not initialized", details: nil))
//            return
//        }
//        
//        // 注意：这里需要从Flutter传递的pixelBuffer参数
//        // 实际实现中需要将Flutter的CVPixelBuffer转换为iOS的CVPixelBuffer
//        // 这是一个简化的实现
//        result(nil)
//    }
//    
//    private func setCameraType(call: FlutterMethodCall, result: @escaping FlutterResult) {
//        guard let args = call.arguments as? [String: Any],
//              let isFrontCamera = args["isFrontCamera"] as? Bool else {
//            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
//            return
//        }
//        
//        trackManager?.isFrontCamera = isFrontCamera
//        result(nil)
//    }
}

// MARK: - HHTracklistener
extension TrackingManagerPlugin: HHTracklistener {
    public func onTrackEvent(_ event: TrackEvent) {
        // 使用与iOS demo相同的UUID比较方式
        guard event.uuid == TrackEventName.basketballShoot.rawValue else { return }
        
        var eventType = ""
        var score = 0
        var message = ""
        
        switch Int(event.type) {
        case Int(TrackEventType_Shot.rawValue):
            eventType = "shot"
            score = Int(event.score)
            message = "投篮数: \(score)"
        case Int(TrackEventType_FieldGoalMade.rawValue):
            eventType = "made"
            score = Int(event.score)
            message = "命中数: \(score)"
        case Int(TrackEventType_FieldGoalMissed.rawValue):
            eventType = "missed"
            score = Int(event.score)
            message = "未中数: \(score)"
        default:
            eventType = "unknown"
            message = "未知事件"
        }
        
        DispatchQueue.main.async {
            self.methodChannel?.invokeMethod("onTrackingEvent", arguments: [
                "eventType": eventType,
                "score": score,
                "message": message,
                "timeStamp":event.timestamp,
                "trainingId":CameraManager.shared.trainingId,
                "startRecordingTimeStamp":CameraManager.shared.startRecordingTimeStamp
            ])
        }
    }
}
