//
//  TrackManager.swift
//  shootz-camera
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 9/20/25.
//

import Foundation
import SwiftUI
import Combine
import CoreVideo

class TrackManager: NSObject, ObservableObject {
    static let shared = TrackManager()
    
    @Published var isTracking = false
    @Published var shotCount = 0
    @Published var madeCount = 0
    @Published var missedCount = 0
    @Published var trackingMessage = ""
    @Published var isSDKAvailable = false
    
    private var trackManager: HHTrackManager?
    
    var accuracy: Double {
        guard shotCount > 0 else { return 0.0 }
        return Double(madeCount) / Double(shotCount) * 100.0
    }
    
    
    deinit {
        // 清理资源
        trackManager = nil
        print("TrackManager 已销毁")
    }
    
    private func setupTrackManager() {
        guard trackManager == nil else { return }
        
        print("开始初始化 HHTrackManager...")
        
        // 添加短暂延迟，避免与其他 Metal 操作冲突
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.trackManager = HHTrackManager(listener: self)
            
            // 在同一个异步块中更新 @Published 属性
            if self.trackManager != nil {
                self.trackManager?.isFrontCamera = false // 默认后置摄像头
                self.isSDKAvailable = true
                print("HHTrackManager 初始化成功")
                
                // 设置初始方向为竖屏，避免不必要的横竖屏切换指令
                print("设置追踪方向为竖屏模式")
            } else {
                self.isSDKAvailable = false
                print("HHTrackManager 初始化失败")
            }
        }
    }
    
    
    // MARK: - Public Methods
    
    func startTracking() {
        guard !isTracking else { return }
        
        // 如果还没有初始化 HHTrackManager，现在初始化
        if trackManager == nil {
            setupTrackManager()
            
            // 等待初始化完成后再调用 startTrack
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                self.startTrackingAfterInit()
            }
        } else {
            // 已经初始化，直接开始追踪
            startTrackingAfterInit()
        }
        
        isTracking = true
        trackingMessage = "正在启动篮球追踪..."
        print("开始篮球追踪")
    }
    
    private func startTrackingAfterInit() {
        // 确保在主线程上调用 SDK 的 startTrack 方法
        DispatchQueue.main.async {
            if let manager = self.trackManager {
                manager.startTrack()
                print("调用 HHTrackManager.startTrack()")
                
                // 更新状态
                self.trackingMessage = self.isSDKAvailable ? "篮球追踪已开启" : "追踪模式（SDK 不可用）"
            } else {
                print("HHTrackManager 未初始化，无法开始追踪")
                self.isTracking = false
                self.trackingMessage = "追踪启动失败"
            }
        }
    }
    
    func stopTracking() {
        guard isTracking else { return }
        
        // 确保在主线程上调用 SDK 的 stopTrack 方法
        DispatchQueue.main.async {
            if let manager = self.trackManager {
                manager.stopTrack()
                print("调用 HHTrackManager.stopTrack()")
            }
        }
        
        isTracking = false
        trackingMessage = "篮球追踪已关闭"
        print("停止篮球追踪")
    }
    
    func clearScore() {
        // 确保在主线程上调用 SDK
        DispatchQueue.main.async {
            if let trackManager = self.trackManager {
                trackManager.clearScore()
            }
            
            self.shotCount = 0
            self.madeCount = 0
            self.missedCount = 0
            self.trackingMessage = "得分已清零"
            
            print("清零投篮得分")
        }
    }
    
    func handleFrame(_ pixelBuffer: CVPixelBuffer) {
        // 根据 demo 逻辑，总是将帧传递给 SDK（如果可用）
        guard let trackManager = trackManager else { return }
        
        // 确保在主线程上调用 SDK，避免 Metal 线程冲突
        if Thread.isMainThread {
            trackManager.handleFrame(pixelBuffer)
        } else {
            DispatchQueue.main.async {
                trackManager.handleFrame(pixelBuffer)
            }
        }
    }
    
    
    func setCameraType(isFrontCamera: Bool) {
        guard let trackManager = trackManager else { return }
        trackManager.isFrontCamera = isFrontCamera
    }
    
    // MARK: - Helper Methods
    
    func getStatsText() -> String {
        return "投篮: \(shotCount) | 命中: \(madeCount) | 未中: \(missedCount) | 命中率: \(String(format: "%.1f", accuracy))%"
    }
}

// MARK: - HHTracklistener

extension TrackManager: HHTracklistener {
    func onTrackEvent(_ event: TrackEvent) {
        DispatchQueue.main.async {
            // 使用与 demo 相同的 UUID 比较方式
            guard event.uuid == TrackEventName.basketballShoot.rawValue else { return }
            
            if event.type == TrackEventType_Shot.rawValue {
                // 根据 demo 逻辑，只更新投篮数
                self.shotCount = Int(event.score)
                self.trackingMessage = "投篮数: \(self.shotCount)"
                print("检测到投篮事件，总投篮数: \(self.shotCount)")
            } else if event.type == TrackEventType_FieldGoalMade.rawValue {
                // 根据 demo 注释，算法暂未提供数据
                print("投篮命中事件（算法暂未提供数据）")
            } else if event.type == TrackEventType_FieldGoalMissed.rawValue {
                // 根据 demo 注释，算法暂未提供数据
                print("投篮未命中事件（算法暂未提供数据）")
            } else {
                print("未知追踪事件类型: \(event.type)")
            }
        }
    }
}
