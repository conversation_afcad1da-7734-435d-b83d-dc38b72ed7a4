//
//  CameraManager.swift
//  shootz-camera
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 9/20/25.
//

import Foundation
import AVFoundation
import SwiftUI
import Combine
import CoreVideo

class CameraManager: NSObject, ObservableObject {
    @Published var session = AVCaptureSession()
    @Published var photoOutput = AVCapturePhotoOutput()
    @Published var videoOutput = AVCaptureMovieFileOutput()
    @Published var preview: AVCaptureVideoPreviewLayer!
    @Published var isSessionRunning = false
    @Published var capturedImage: UIImage?
    @Published var isRecording = false
    @Published var showAlert = false
    @Published var alertMessage = ""
    @Published var availableResolutions: [AVCaptureSession.Preset] = []
    @Published var currentResolution: AVCaptureSession.Preset = .hd1920x1080
    @Published var frameRate: Int = 30
    @Published var isPortraitMode = true
    
    // 帧处理回调
    var frameHandler: ((CVPixelBuffer) -> Void)?
    
    override init() {
        super.init()
        // 预初始化相机预览层
        preview = AVCaptureVideoPreviewLayer(session: session)
        preview.videoGravity = .resizeAspectFill
        preview.backgroundColor = UIColor.black.cgColor
        
        // 异步检查权限，避免阻塞初始化
        DispatchQueue.main.async {
            self.checkPermissions()
        }
    }
    
    func checkPermissions() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            // 权限已授权，直接设置相机
            setupCamera()
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                if granted {
                    self.setupCamera()
                }
            }
        case .denied, .restricted:
            DispatchQueue.main.async {
                self.alertMessage = "Camera access is required to use this feature"
                self.showAlert = true
            }
        @unknown default:
            break
        }
    }
    
    func setupCamera() {
        // 如果会话已经在运行，先停止
        if session.isRunning {
            session.stopRunning()
        }
        
        do {
            session.beginConfiguration()
            
            // Remove existing inputs
            session.inputs.forEach { session.removeInput($0) }
            
            // Add camera input
            guard let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else {
                session.commitConfiguration()
                DispatchQueue.main.async {
                    self.alertMessage = "Back camera not available"
                    self.showAlert = true
                }
                return
            }
            
            let input = try AVCaptureDeviceInput(device: device)
            if session.canAddInput(input) {
                session.addInput(input)
            }
            
            // Add photo output
            if session.canAddOutput(photoOutput) {
                session.addOutput(photoOutput)
            }
            
            // Add video output
            if session.canAddOutput(videoOutput) {
                session.addOutput(videoOutput)
            }
            
            // Add video data output for frame processing
            let videoDataOutput = AVCaptureVideoDataOutput()
            videoDataOutput.setSampleBufferDelegate(self, queue: DispatchQueue(label: "VideoDataOutputQueue"))
            if session.canAddOutput(videoDataOutput) {
                session.addOutput(videoDataOutput)
            }
            
            // Set initial resolution
            if session.canSetSessionPreset(currentResolution) {
                session.sessionPreset = currentResolution
            }
            
            // Set frame rate like in demo
            do {
                try device.lockForConfiguration()
                
                // Find and set the best format for the desired resolution and frame rate
                var bestFormat: AVCaptureDevice.Format?
                for format in device.formats {
                    let dimensions = CMVideoFormatDescriptionGetDimensions(format.formatDescription)
                    let width = Int(dimensions.width)
                    let height = Int(dimensions.height)
                    
                    // Match 1920x1080 resolution
                    if width == 1920 && height == 1080 {
                        for range in format.videoSupportedFrameRateRanges {
                            if range.minFrameRate <= Double(frameRate) && Double(frameRate) <= range.maxFrameRate {
                                bestFormat = format
                                break
                            }
                        }
                        if bestFormat != nil { break }
                    }
                }
                
                if let format = bestFormat {
                    device.activeFormat = format
                    device.activeVideoMinFrameDuration = CMTime(value: 1, timescale: CMTimeScale(frameRate))
                    device.activeVideoMaxFrameDuration = CMTime(value: 1, timescale: CMTimeScale(frameRate))
                    print("设置成功: 1920x1080 @ \(frameRate)fps")
                } else {
                    print("未找到支持 1920x1080 @ \(frameRate)fps 的格式")
                }
                
                device.unlockForConfiguration()
            } catch {
                print("设置帧率失败: \(error)")
            }
            
            // Setup available resolutions
            setupAvailableResolutions()
            
            session.commitConfiguration()
            
            // 预览层已在init中创建，只需更新方向
            DispatchQueue.main.async {
                self.updatePreviewOrientation()
            }
            
            // 在配置完全提交后启动会话
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.startSession()
            }
            
        } catch {
            DispatchQueue.main.async {
                self.alertMessage = error.localizedDescription
                self.showAlert = true
            }
        }
    }
    
    func setupAvailableResolutions() {
        let resolutions: [AVCaptureSession.Preset] = [
            .photo,
            .hd4K3840x2160,
            .hd1920x1080,
            .hd1280x720,
            .vga640x480,
            .high,
            .medium,
            .low
        ]
        
        availableResolutions = resolutions.filter { session.canSetSessionPreset($0) }
        
        // 确保至少有一个可用分辨率
        if availableResolutions.isEmpty {
            availableResolutions = [.photo]
        }
    }
    
    func startSession() {
        if !session.isRunning {
            print("开始启动相机会话")
            DispatchQueue.global(qos: .userInitiated).async {
                self.session.startRunning()
                DispatchQueue.main.async {
                    print("session.outputs.count: \(self.session.outputs.count)")
                    self.isSessionRunning = true
                    print("相机会话已启动，isSessionRunning: \(self.isSessionRunning)")
                    // 验证预览层状态
                    if let preview = self.preview {
                        print("预览层存在，session: \(preview.session != nil ? "已连接" : "未连接")")
                        print("预览层frame: \(preview.frame)")
                    } else {
                        print("警告: 预览层为空")
                    }
                }
            }
        }else {
            print("相机会话已经在运行")
        }
    }
    
    // 手动初始化相机（用于调试）
    func initializeCamera() {
        checkPermissions()
    }
    
    func stopSession() {
        if session.isRunning {
            DispatchQueue.global(qos: .userInitiated).async {
                self.session.stopRunning()
                DispatchQueue.main.async {
                    self.isSessionRunning = false
                }
            }
        }
    }
    
    func capturePhoto() {
        let settings = AVCapturePhotoSettings()
        photoOutput.capturePhoto(with: settings, delegate: self)
    }
    
    func toggleRecording() {
        if isRecording {
            stopRecording()
        } else {
            startRecording()
        }
    }
    
    private func startRecording() {
        guard !isRecording else { return }
        
        // 确保输出目录存在
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first
        guard let documentsURL = documentsPath else {
            print("无法获取文档目录")
            alertMessage = "无法访问存储目录"
            showAlert = true
            return
        }
        
        // 创建唯一的文件名
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd_HHmmss"
        let timestamp = formatter.string(from: Date())
        let fileName = "video_\(timestamp).mov"
        let outputURL = documentsURL.appendingPathComponent(fileName)
        
        print("开始录制到: \(outputURL.path)")
        
        // 确保没有重复的文件
        if FileManager.default.fileExists(atPath: outputURL.path) {
            do {
                try FileManager.default.removeItem(at: outputURL)
            } catch {
                print("删除已存在文件失败: \(error)")
            }
        }
        
        // 验证 URL 是否有效
        guard !outputURL.absoluteString.isEmpty,
              outputURL.path.count > 0 else {
            print("无效的输出 URL: \(outputURL)")
            alertMessage = "文件路径无效"
            showAlert = true
            return
        }
        
        do {
            // 确保可以写入文件
            let testData = Data()
            try testData.write(to: outputURL)
            try FileManager.default.removeItem(at: outputURL)
            
            videoOutput.startRecording(to: outputURL, recordingDelegate: self)
            isRecording = true
        } catch {
            print("无法写入到输出路径: \(error)")
            alertMessage = "无法创建录制文件: \(error.localizedDescription)"
            showAlert = true
        }
    }
    
    private func stopRecording() {
        guard isRecording else { return }
        videoOutput.stopRecording()
        isRecording = false
    }
    
    func changeResolution(to preset: AVCaptureSession.Preset) {
        session.beginConfiguration()
        if session.canSetSessionPreset(preset) {
            session.sessionPreset = preset
            currentResolution = preset
        }
        session.commitConfiguration()
    }
    
    func toggleOrientation() {
        isPortraitMode.toggle()
        // 不再更改预览层的方向，只切换UI状态
    }
    
    private func updatePreviewOrientation() {
        guard let preview = preview else { return }
        
        // 保持预览层始终为竖屏方向
        let orientation: AVCaptureVideoOrientation = .portrait
        if preview.connection?.isVideoOrientationSupported == true {
            preview.connection?.videoOrientation = orientation
        }
    }
    
    func getResolutionDisplayName(for preset: AVCaptureSession.Preset) -> String {
        switch preset {
        case .photo:
            return "最高"
        case .hd4K3840x2160:
            return "4K"
        case .hd1920x1080:
            return "1080"
        case .hd1280x720:
            return "720"
        case .vga640x480:
            return "480"
        case .medium:
            return "480"
        case .low:
            return "360"
        case .high:
            return "720"
        default:
            return "自动"
        }
    }
}

extension CameraManager: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        if let error = error {
            DispatchQueue.main.async {
                self.alertMessage = error.localizedDescription
                self.showAlert = true
            }
            return
        }
        
        guard let data = photo.fileDataRepresentation(),
              let image = UIImage(data: data) else {
            DispatchQueue.main.async {
                self.alertMessage = "Failed to process photo"
                self.showAlert = true
            }
            return
        }
        
        DispatchQueue.main.async {
            self.capturedImage = image
        }
    }
}

extension CameraManager: AVCaptureFileOutputRecordingDelegate {
    func fileOutput(_ output: AVCaptureFileOutput, didStartRecordingTo fileURL: URL, from connections: [AVCaptureConnection]) {
        DispatchQueue.main.async {
            self.isRecording = true
        }
    }
    
    func fileOutput(_ output: AVCaptureFileOutput, didFinishRecordingTo outputFileURL: URL, from connections: [AVCaptureConnection], error: Error?) {
        DispatchQueue.main.async {
            self.isRecording = false
            if let error = error {
                self.alertMessage = "录像失败: \(error.localizedDescription)"
                self.showAlert = true
            } else {
                self.alertMessage = "录像已保存到: \(outputFileURL.path)"
                self.showAlert = true
            }
        }
    }
}

// MARK: - AVCaptureVideoDataOutputSampleBufferDelegate

extension CameraManager: AVCaptureVideoDataOutputSampleBufferDelegate {
    func captureOutput(_ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        guard let pixelBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) else { return }
        
        // 在 Swift 中，Core Foundation 对象是自动内存管理的
        // 不需要手动调用 retain/release
        
        // 调用帧处理回调
        frameHandler?(pixelBuffer)
    }
}
