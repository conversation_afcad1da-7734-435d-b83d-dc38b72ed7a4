//
//  CameraManager.swift
//  shootz-camera
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 9/20/25.
//

import Foundation
import AVFoundation
import SwiftUI
import Combine
import CoreVideo
import Photos

class CameraManager: NSObject, ObservableObject {
    @Published var session = AVCaptureSession()
    @Published var photoOutput = AVCapturePhotoOutput()
    @Published var videoOutput = AVCaptureMovieFileOutput()
    @Published var preview: AVCaptureVideoPreviewLayer!
    @Published var isSessionRunning = false
    @Published var capturedImage: UIImage?
    @Published var isRecording = false
    @Published var showAlert = false
    @Published var alertMessage = ""
    @Published var folderName = ""
    @Published var availableResolutions: [AVCaptureSession.Preset] = []
    @Published var currentResolution: AVCaptureSession.Preset = .hd1920x1080
    @Published var frameRate: Int = 30
    @Published var isPortraitMode = true
    
    // 相机设置
    @Published var focusMode: AVCaptureDevice.FocusMode = .continuousAutoFocus
    @Published var exposureMode: AVCaptureDevice.ExposureMode = .continuousAutoExposure
    @Published var exposureBias: Float = 0.0
    @Published var showGridLines = false
    
    // 帧处理回调
    var frameHandler: ((CVPixelBuffer) -> Void)?
    
    override init() {
        super.init()
        print("managerInit!!!!!!!!!")
        // 预初始化相机预览层
        preview = AVCaptureVideoPreviewLayer(session: session)
        preview.videoGravity = .resizeAspectFill
        preview.backgroundColor = UIColor.black.cgColor
        
        // 异步检查权限，避免阻塞初始化
        DispatchQueue.main.async {
            self.checkPermissions()
        }
    }
    
    func checkPermissions() {
        print("CameraManager: Checking camera permissions...")
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        print("CameraManager: Permission status: \(status.rawValue)")
        
        switch status {
        case .authorized:
            // 权限已授权，直接设置相机
            print("CameraManager: Permission authorized, setting up camera")
            setupCamera()
        case .notDetermined:
            print("CameraManager: Permission not determined, requesting access")
            AVCaptureDevice.requestAccess(for: .video) { granted in
                print("CameraManager: Permission request result: \(granted)")
                if granted {
                    self.setupCamera()
                }
            }
        case .denied, .restricted:
            print("CameraManager: Permission denied or restricted")
            DispatchQueue.main.async {
                self.alertMessage = "Camera access is required to use this feature"
                self.showAlert = true
            }
        @unknown default:
            print("CameraManager: Unknown permission status")
            break
        }
    }
    
    func setupCamera() {
        print("CameraManager: setupCamera called")
        // 如果会话已经在运行，先停止
        if session.isRunning {
            print("CameraManager: Stopping existing session")
            session.stopRunning()
        }
        
        // 防止重复配置
        if session.inputs.count > 0 {
            print("CameraManager: Camera already configured, skipping setup")
            return
        }
        
        do {
            session.beginConfiguration()
            
            // Remove existing inputs
            session.inputs.forEach { session.removeInput($0) }
            
            // Add camera input
            guard let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else {
                session.commitConfiguration()
                DispatchQueue.main.async {
                    self.alertMessage = "Back camera not available"
                    self.showAlert = true
                }
                return
            }
            
            let input = try AVCaptureDeviceInput(device: device)
            if session.canAddInput(input) {
                session.addInput(input)
            }
            
            // Add photo output
            if session.canAddOutput(photoOutput) {
                session.addOutput(photoOutput)
            }
            
            // Add video output
            if session.canAddOutput(videoOutput) {
                session.addOutput(videoOutput)
                // 设置初始视频方向
                setupVideoOrientation()
            }
            
            // Add video data output for frame processing
            let videoDataOutput = AVCaptureVideoDataOutput()
            videoDataOutput.setSampleBufferDelegate(self, queue: DispatchQueue(label: "VideoDataOutputQueue"))
            if session.canAddOutput(videoDataOutput) {
                session.addOutput(videoDataOutput)
            }
            
            // 简化相机配置，避免冲突
            do {
                try device.lockForConfiguration()
                
                // 使用默认格式，避免与sessionPreset冲突
                print("使用默认相机格式")
                
                // 设置帧率
                device.activeVideoMinFrameDuration = CMTime(value: 1, timescale: CMTimeScale(frameRate))
                device.activeVideoMaxFrameDuration = CMTime(value: 1, timescale: CMTimeScale(frameRate))
                
                device.unlockForConfiguration()
                print("相机配置完成")
            } catch {
                print("设置相机配置失败: \(error)")
            }
            
            // 设置session preset
            if session.canSetSessionPreset(currentResolution) {
                session.sessionPreset = currentResolution
                print("设置session preset: \(currentResolution)")
            } else {
                print("无法设置session preset: \(currentResolution)")
            }
            
            // Setup available resolutions
            setupAvailableResolutions()
            
            session.commitConfiguration()
            
            // 预览层已在init中创建，只需更新方向
            DispatchQueue.main.async {
                self.updatePreviewOrientation()
            }
            
            // 在配置完全提交后启动会话
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.startSession()
            }
            
        } catch {
            DispatchQueue.main.async {
                self.alertMessage = error.localizedDescription
                self.showAlert = true
            }
        }
    }
    
    func setupAvailableResolutions() {
        // 在后台线程执行耗时操作
        DispatchQueue.global(qos: .userInitiated).async {
            guard let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else {
                // 如果无法获取设备，使用默认分辨率
                DispatchQueue.main.async {
                    self.availableResolutions = [.hd1920x1080]
                    self.currentResolution = .hd1920x1080
                }
                return
            }
            
            var supportedResolutions: [AVCaptureSession.Preset] = []
            var addedResolutions = Set<String>() // 用于去重
            
            // 按优先级顺序检查分辨率，避免重复
            let resolutionOrder: [AVCaptureSession.Preset] = [
                .hd4K3840x2160,
                .hd1920x1080,
                .hd1280x720,
                .vga640x480,
                .high,
                .medium,
                .low
            ]
            
            for preset in resolutionOrder {
                if self.session.canSetSessionPreset(preset) {
                    let displayName = self.getResolutionDisplayName(for: preset)
                    // 只有当显示名称不重复时才添加
                    if !addedResolutions.contains(displayName) {
                        supportedResolutions.append(preset)
                        addedResolutions.insert(displayName)
                    }
                }
            }
            
            // 如果没有找到任何分辨率，使用默认的1080p
            if supportedResolutions.isEmpty {
                supportedResolutions = [.hd1920x1080]
            }
            
            // 在主线程更新@Published属性
            DispatchQueue.main.async {
                self.availableResolutions = supportedResolutions
                
                // 设置默认分辨率为1080p（如果支持的话）
                if supportedResolutions.contains(.hd1920x1080) {
                    self.currentResolution = .hd1920x1080
                } else if !supportedResolutions.isEmpty {
                    self.currentResolution = supportedResolutions.first!
                }
            }
            
            print("支持的分辨率: \(supportedResolutions.map { self.getResolutionDisplayName(for: $0) })")
        }
    }
    
    func startSession() {
        print("CameraManager: startSession called, session running: \(session.isRunning)")
        if !session.isRunning {
            print("CameraManager: Starting session...")
            
            // 在后台线程启动session，避免UI阻塞
            DispatchQueue.global(qos: .userInitiated).async {
                self.session.startRunning()
                print("CameraManager: Session started, running: \(self.session.isRunning)")
                
                DispatchQueue.main.async {
                    self.isSessionRunning = self.session.isRunning
                    print("CameraManager: isSessionRunning set to \(self.isSessionRunning)")
                }
            }
        } else {
            print("CameraManager: Session already running")
            isSessionRunning = true
        }
    }
    
    // 手动初始化相机（用于调试）
    func initializeCamera() {
        print("CameraManager: initializeCamera called")
        checkPermissions()
        
        // 确保相机启动 - 只启动一次
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            print("CameraManager: Checking session status after delay")
            print("CameraManager: Session running: \(self.session.isRunning)")
            print("CameraManager: Session inputs count: \(self.session.inputs.count)")
            
            if !self.session.isRunning && self.session.inputs.count > 0 {
                print("CameraManager: Starting session from initializeCamera")
                self.startSession()
            } else if self.session.inputs.count == 0 {
                print("CameraManager: No inputs configured, retrying setup")
                self.setupCamera()
            }
        }
    }
    
    func stopSession() {
        if session.isRunning {
            DispatchQueue.global(qos: .userInitiated).async {
                self.session.stopRunning()
                DispatchQueue.main.async {
                    self.isSessionRunning = false
                }
            }
        }
    }
    
    func capturePhoto() {
        let settings = AVCapturePhotoSettings()
        photoOutput.capturePhoto(with: settings, delegate: self)
    }
    
    func toggleRecording() {
        print("!!!!!!!!11111startRecording\(isRecording)\(session.isRunning)")
        if isRecording {
            _stopRecording()
        } else {
            startRecording()
        }
    }
    
    private func startRecording() {
        print("!!!!!!!!11111startRecording\(isRecording)\(session.isRunning)")
        guard !isRecording else { return }
        
        // 确保输出目录存在
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first
        guard let documentsURL = documentsPath else {
            print("无法获取文档目录")
            alertMessage = "无法访问存储目录"
            showAlert = true
            return
        }
        
        // 创建唯一的文件名
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd_HHmmss"
        let timestamp = formatter.string(from: Date())
        let fileName = "video_\(timestamp).mov"
        let outputURL = documentsURL.appendingPathComponent(fileName)
        
        print("开始录制到: \(outputURL.path)")
        
        // 确保没有重复的文件
        if FileManager.default.fileExists(atPath: outputURL.path) {
            do {
                try FileManager.default.removeItem(at: outputURL)
            } catch {
                print("删除已存在文件失败: \(error)")
            }
        }
        
        // 验证 URL 是否有效
        guard !outputURL.absoluteString.isEmpty,
              outputURL.path.count > 0 else {
            print("无效的输出 URL: \(outputURL)")
            alertMessage = "文件路径无效"
            showAlert = true
            return
        }
        
        do {
            // 确保可以写入文件
            let testData = Data()
            try testData.write(to: outputURL)
            try FileManager.default.removeItem(at: outputURL)
            
            // 设置视频方向
            setupVideoOrientation()
            
            videoOutput.startRecording(to: outputURL, recordingDelegate: self)
            isRecording = true
        } catch {
            print("无法写入到输出路径: \(error)")
            alertMessage = "无法创建录制文件: \(error.localizedDescription)"
            showAlert = true
        }
    }
    
    private func _stopRecording() {
        guard isRecording else { return }
        videoOutput.stopRecording()
        isRecording = false
    }
    
    func stopRecording() {
        _stopRecording()
    }
    
    // 设置视频录制方向
    private func setupVideoOrientation() {
        guard let connection = videoOutput.connection(with: .video) else { return }
        
        if connection.isVideoOrientationSupported {
            if isPortraitMode {
                // 竖屏模式
                connection.videoOrientation = .portrait
            } else {
                // 横屏模式
                connection.videoOrientation = .landscapeRight
            }
        }
        
        // 确保视频稳定化开启
        if connection.isVideoStabilizationSupported {
            connection.preferredVideoStabilizationMode = .auto
        }
    }
    
    // 更新视频方向（当模式切换时调用）
    func updateVideoOrientation() {
        guard let connection = videoOutput.connection(with: .video) else { return }
        
        if connection.isVideoOrientationSupported {
            if isPortraitMode {
                connection.videoOrientation = .portrait
            } else {
                connection.videoOrientation = .landscapeRight
            }
        }
    }
    
    func changeResolution(to preset: AVCaptureSession.Preset) {
        // 在后台线程执行session配置
        DispatchQueue.global(qos: .userInitiated).async {
            self.session.beginConfiguration()
            if self.session.canSetSessionPreset(preset) {
                self.session.sessionPreset = preset
                // 在主线程更新@Published属性
                DispatchQueue.main.async {
                    self.currentResolution = preset
                }
            }
            self.session.commitConfiguration()
        }
    }
    
    func toggleOrientation() {
        // 在主线程更新@Published属性
        DispatchQueue.main.async {
            self.isPortraitMode.toggle()
        }
        // 不再更改预览层的方向，只切换UI状态
    }
    
    func setPortraitMode(_ portrait: Bool) {
        // 在主线程更新@Published属性
        DispatchQueue.main.async {
            self.isPortraitMode = portrait
        }
        // 更新视频录制方向
        updateVideoOrientation()
    }
    
    private func updatePreviewOrientation() {
        guard let preview = preview else { return }
        
        // 保持预览层始终为竖屏方向
        let orientation: AVCaptureVideoOrientation = .portrait
        if preview.connection?.isVideoOrientationSupported == true {
            preview.connection?.videoOrientation = orientation
        }
    }
    
    func getResolutionDisplayName(for preset: AVCaptureSession.Preset) -> String {
        switch preset {
        case .hd4K3840x2160:
            return "4K"
        case .hd1920x1080:
            return "1080p"
        case .hd1280x720:
            return "720p"
        case .vga640x480:
            return "640p"
        case .medium:
            return "480p"
        case .low:
            return "360p"
        case .high:
            return "720p"
        default:
            return "自动"
        }
    }
    
    // MARK: - 相机设置方法
    
    func setFocusMode(_ mode: AVCaptureDevice.FocusMode) {
        guard let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else { return }
        
        do {
            try device.lockForConfiguration()
            if device.isFocusModeSupported(mode) {
                device.focusMode = mode
                focusMode = mode
            }
            device.unlockForConfiguration()
        } catch {
            print("设置对焦模式失败: \(error)")
        }
    }
    
    func setExposureMode(_ mode: AVCaptureDevice.ExposureMode) {
        guard let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else { return }
        
        do {
            try device.lockForConfiguration()
            if device.isExposureModeSupported(mode) {
                device.exposureMode = mode
                exposureMode = mode
            }
            device.unlockForConfiguration()
        } catch {
            print("设置曝光模式失败: \(error)")
        }
    }
    
    func setExposureBias(_ bias: Float) {
        guard let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else { return }
        
        do {
            try device.lockForConfiguration()
            let minBias = device.minExposureTargetBias
            let maxBias = device.maxExposureTargetBias
            let clampedBias = max(minBias, min(maxBias, bias))
            device.setExposureTargetBias(clampedBias, completionHandler: nil)
            exposureBias = clampedBias
            device.unlockForConfiguration()
        } catch {
            print("设置曝光补偿失败: \(error)")
        }
    }
    
    func toggleGridLines() {
        showGridLines.toggle()
    }
    
    func getFocusModeDisplayName() -> String {
        switch focusMode {
        case .locked:
            return "锁定"
        case .autoFocus:
            return "自动"
        case .continuousAutoFocus:
            return "连续自动"
        @unknown default:
            return "未知"
        }
    }
    
    func getExposureModeDisplayName() -> String {
        switch exposureMode {
        case .locked:
            return "锁定"
        case .autoExpose:
            return "自动"
        case .continuousAutoExposure:
            return "连续自动"
        case .custom:
            return "自定义"
        @unknown default:
            return "未知"
        }
    }
}

extension CameraManager: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        if let error = error {
            DispatchQueue.main.async {
                self.alertMessage = error.localizedDescription
                self.showAlert = true
            }
            return
        }
        
        guard let data = photo.fileDataRepresentation(),
              let image = UIImage(data: data) else {
            DispatchQueue.main.async {
                self.alertMessage = "Failed to process photo"
                self.showAlert = true
            }
            return
        }
        
        DispatchQueue.main.async {
            self.capturedImage = image
        }
    }
}

extension CameraManager: AVCaptureFileOutputRecordingDelegate {
    func fileOutput(_ output: AVCaptureFileOutput, didStartRecordingTo fileURL: URL, from connections: [AVCaptureConnection]) {
        DispatchQueue.main.async {
            self.isRecording = true
        }
    }
    
    func fileOutput(_ output: AVCaptureFileOutput, didFinishRecordingTo outputFileURL: URL, from connections: [AVCaptureConnection], error: Error?) {
        DispatchQueue.main.async {
            self.isRecording = false
            if let error = error {
                self.alertMessage = "录像失败: \(error.localizedDescription)"
                self.showAlert = true
            } else {
                // 将视频保存到相册
                self.saveVideoToPhotoLibrary(url: outputFileURL)
            }
        }
    }
    
    // 保存视频到相册
    private func saveVideoToPhotoLibrary(url: URL) {
        // 在后台线程执行权限检查和保存操作
        DispatchQueue.global(qos: .userInitiated).async {
            // 检查相册权限
            let status = PHPhotoLibrary.authorizationStatus(for: .addOnly)
            
            switch status {
            case .authorized, .limited:
                // 已有权限，直接保存
                self.performSaveVideo(url: url)
            case .notDetermined:
                // 请求权限
                PHPhotoLibrary.requestAuthorization(for: .addOnly) { [weak self] newStatus in
                    if newStatus == .authorized || newStatus == .limited {
                        self?.performSaveVideo(url: url)
                    } else {
                        DispatchQueue.main.async {
                            self?.alertMessage = "需要相册权限才能保存视频"
                            self?.showAlert = true
                        }
                    }
                }
            case .denied, .restricted:
                // 权限被拒绝
                DispatchQueue.main.async {
                    self.alertMessage = "需要相册权限才能保存视频，请在设置中允许访问相册"
                    self.showAlert = true
                }
            @unknown default:
                DispatchQueue.main.async {
                    self.alertMessage = "无法访问相册"
                    self.showAlert = true
                }
            }
        }
    }
    
    // 执行保存视频到相册
    private func performSaveVideo(url: URL) {
        PHPhotoLibrary.shared().performChanges({
            PHAssetChangeRequest.creationRequestForAssetFromVideo(atFileURL: url)
        }) { [weak self] success, error in
            // 在后台线程删除临时文件
            DispatchQueue.global(qos: .background).async {
                do {
                    try FileManager.default.removeItem(at: url)
                    print("临时文件已删除: \(url.path)")
                } catch {
                    print("删除临时文件失败: \(error)")
                }
            }
            
            // 在主线程更新UI
            DispatchQueue.main.async {
                if success {
                    self?.alertMessage = "视频已保存到相册"
                    self?.showAlert = true
                } else {
                    self?.alertMessage = "保存到相册失败: \(error?.localizedDescription ?? "未知错误")"
                    self?.showAlert = true
                }
            }
        }
    }
}

// MARK: - AVCaptureVideoDataOutputSampleBufferDelegate

extension CameraManager: AVCaptureVideoDataOutputSampleBufferDelegate {
    func captureOutput(_ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        guard let pixelBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) else { return }
        
        // 在 Swift 中，Core Foundation 对象是自动内存管理的
        // 不需要手动调用 retain/release
        
        // 调用帧处理回调
        frameHandler?(pixelBuffer)
    }
}
