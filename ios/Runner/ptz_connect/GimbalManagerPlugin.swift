import Flutter
import UIKit
import CoreBluetooth

public class GimbalManagerPlugin: NSObject, FlutterPlugin, HHConnListener {
    private var methodChannel: FlutterMethodChannel?
    private var gimbalManager: GimbalManager
    
    override init() {
        self.gimbalManager = GimbalManager.shared
        super.init()
    }
    
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "gimbal_manager", binaryMessenger: registrar.messenger())
        let instance = GimbalManagerPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
        instance.methodChannel = channel
        instance.setupManagers()
    }
    
    private func setupManagers() {
        // 使用全局GimbalManager实例
        gimbalManager.setupManagers()
        
        // 设置GimbalManagerPlugin为监听器
        if let connectManager = gimbalManager.connectManager {
            connectManager.listener = self
        }
    }
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "startScanning":
            startScanning(result: result)
        case "stopScanning":
            stopScanning(result: result)
        case "connectToDevice":
            connectToDevice(call: call, result: result)
        case "disconnect":
            disconnect(result: result)
        case "gimbalCentering":
            gimbalCentering(result: result)
        case "gimbalTurnLeft":
            gimbalTurnLeft(result: result)
        case "gimbalTurnRight":
            gimbalTurnRight(result: result)
        case "setDirection":
            setDirection(call: call, result: result)
        case "setLeftLimit":
            setLeftLimit(result: result)
        case "setRightLimit":
            setRightLimit(result: result)
        case "goToLeftLimit":
            goToLeftLimit(result: result)
        case "goToRightLimit":
            goToRightLimit(result: result)
        case "enableLimit":
            enableLimit(call: call, result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func startScanning(result: @escaping FlutterResult) {
        gimbalManager.startScanning()
        result(nil)
    }
    
    private func stopScanning(result: @escaping FlutterResult) {
        gimbalManager.stopScanning()
        result(nil)
    }
    
    private func connectToDevice(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let name = args["name"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
            return
        }
        
        // 查找设备
        if let device = gimbalManager.discoveredDevices.first(where: { $0.name == name }) {
            gimbalManager.connectToDevice(device)
        }
        result(nil)
    }
    
    private func disconnect(result: @escaping FlutterResult) {
        gimbalManager.disconnect()
        result(nil)
    }
    
    private func gimbalCentering(result: @escaping FlutterResult) {
        gimbalManager.gimbalCentering()
        result(nil)
    }
    
    private func gimbalTurnLeft(result: @escaping FlutterResult) {
        gimbalManager.gimbalTurnLeft()
        result(nil)
    }
    
    private func gimbalTurnRight(result: @escaping FlutterResult) {
        gimbalManager.gimbalTurnRight()
        result(nil)
    }
    
    private func setDirection(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let isPortrait = args["isPortrait"] as? Bool else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
            return
        }
        
        gimbalManager.setDirection(isPortrait)
        result(nil)
    }
    
    private func setLeftLimit(result: @escaping FlutterResult) {
        gimbalManager.setLeftLimit()
        result(nil)
    }
    
    private func setRightLimit(result: @escaping FlutterResult) {
        gimbalManager.setRightLimit()
        result(nil)
    }
    
    private func goToLeftLimit(result: @escaping FlutterResult) {
        gimbalManager.goToLeftLimit()
        result(nil)
    }
    
    private func goToRightLimit(result: @escaping FlutterResult) {
        gimbalManager.goToRightLimit()
        result(nil)
    }
    
    private func enableLimit(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let enable = args["enable"] as? Bool else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
            return
        }
        
        gimbalManager.enableLimit(enable)
        result(nil)
    }
}

// MARK: - HHConnListener
extension GimbalManagerPlugin {
    public func onDeviceFound(_ deviceList: [HHDevice]) {
        print("GimbalManagerPluginonDeviceFound!!!!!")
        gimbalManager.onDeviceFound(deviceList)
        let deviceData = deviceList.map { device in
            return [
                "name": device.name,
                "macAddress": device.macAddress,
                "serial": device.serial,
                "rssi": device.rssi,
                "isConnected": false // HHDevice没有isConnected属性，默认为false
            ]
        }
        methodChannel?.invokeMethod("onDeviceFound", arguments: ["devices": deviceData])
    }
    
    public func onConnChange(_ event: ConnEvent) {
        gimbalManager.onConnChange(event)
        
        switch event.type {
        case ConnStatus_ConnectSuccessful:
            methodChannel?.invokeMethod("onConnectionStatusChanged", arguments: ["status": "connected", "message": ""])
            
        case ConnStatus_ConnectFailure:
            methodChannel?.invokeMethod("onConnectionStatusChanged", arguments: ["status": "error", "message": event.desc ?? "连接失败"])
            
        case ConnStatus_Disconnected:
            methodChannel?.invokeMethod("onConnectionStatusChanged", arguments: ["status": "disconnected", "message": ""])
            
        case ConnStatus_BLEPoweredOff:
            methodChannel?.invokeMethod("onConnectionStatusChanged", arguments: ["status": "error", "message": "蓝牙未开启"])
            
        case ConnStatus_BLEUnauthorized:
            methodChannel?.invokeMethod("onConnectionStatusChanged", arguments: ["status": "error", "message": "蓝牙权限未授权"])
            
        default:
            methodChannel?.invokeMethod("onConnectionStatusChanged", arguments: ["status": "error", "message": "未知错误"])
        }
    }
    
    public func onDeviceStateChanged(_ event: StateEvent) {
        gimbalManager.onDeviceStateChanged(event)
        
        switch event.type {
        case DeviceStatus_LOW_BATTERY:
            methodChannel?.invokeMethod("onDeviceStateChanged", arguments: ["state": "low_battery", "message": "云台电量低"])
            
        case DeviceStatus_OVERLOAD:
            methodChannel?.invokeMethod("onDeviceStateChanged", arguments: ["state": "overload", "message": "云台过载"])
            
        case DeviceStatus_HARDWARE_ERROR:
            methodChannel?.invokeMethod("onDeviceStateChanged", arguments: ["state": "hardware_error", "message": "云台硬件错误"])
            
        case DeviceStatus_LEFT_LIMIT:
            methodChannel?.invokeMethod("onDeviceStateChanged", arguments: ["state": "left_limit", "message": "云台到达左限位"])
            
        default:
            methodChannel?.invokeMethod("onDeviceStateChanged", arguments: ["state": "unknown", "message": "其他设备状态变化"])
        }
    }
}
