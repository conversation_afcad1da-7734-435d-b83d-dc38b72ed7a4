//
//  GimbalManager.swift
//  shootz-camera
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 9/20/25.
//

import Foundation
import SwiftUI
import Combine

class GimbalManager: NSObject, ObservableObject {
    static let shared = GimbalManager()
    
    @Published var connectionStatus: ConnectionStatus = .disconnected
    @Published var discoveredDevices: [HHDevice] = []
    @Published var connectedDevice: HHDevice?
    @Published var isScanning = false
    @Published var errorMessage: String = ""
    
    var connectManager: HHConnectManager!
    private var yunTaiDevice: HHYunTaiDevice!
    
    private override init() {
        super.init()
        setupManagers()
    }
    
    enum ConnectionStatus: Equatable {
        case disconnected
        case connecting
        case connected
        case error(String)
        
        var description: String {
            switch self {
            case .disconnected:
                return "未连接"
            case .connecting:
                return "连接中..."
            case .connected:
                return "已连接"
            case .error(let message):
                return "错误: \(message)"
            }
        }
        
        var color: Color {
            switch self {
            case .disconnected:
                return .gray
            case .connecting:
                return .orange
            case .connected:
                return .green
            case .error:
                return .red
            }
        }
        
        static func == (lhs: ConnectionStatus, rhs: ConnectionStatus) -> Bool {
            switch (lhs, rhs) {
            case (.disconnected, .disconnected),
                 (.connecting, .connecting),
                 (.connected, .connected):
                return true
            case (.error(let lhsMessage), .error(let rhsMessage)):
                return lhsMessage == rhsMessage
            default:
                return false
            }
        }
    }
    
    
    deinit {
        connectManager?.disconnect()
    }
    
    func setupManagers() {
        connectManager = HHConnectManager(listener: self)
        yunTaiDevice = HHYunTaiDevice()
        connectManager.enableLogPrint(true)
    }
    
    // MARK: - Public Methods
    
    func startScanning() {
        print("!!!!!!!!!!startScanning")
        guard !isScanning else { return }
        
        discoveredDevices.removeAll()
        isScanning = true
        connectionStatus = .disconnected
        connectManager.startScan()
        
        // 30秒后自动停止扫描
        DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
            if self.isScanning {
                self.stopScanning()
            }
        }
    }
    
    func stopScanning() {
        isScanning = false
        connectManager.stopScan()
    }
    
    func connectToDevice(_ device: HHDevice) {
        stopScanning()
        connectionStatus = .connecting
        connectedDevice = device
        
        print("尝试连接云台设备: \(device.name), MAC: \(device.macAddress)")
        
        // 尝试使用设备名称连接
        connectManager.connect(device.name)
        
        // 设置连接超时
        DispatchQueue.main.asyncAfter(deadline: .now() + 15) {
            if self.connectionStatus == .connecting {
                self.connectionStatus = .error("连接超时")
                self.errorMessage = "连接超时"
                print("云台连接超时")
            }
        }
    }
    
    func disconnect() {
        connectManager.disconnect()
        connectionStatus = .disconnected
        connectedDevice = nil
    }
    
    // MARK: - Gimbal Control
    
    func gimbalCentering() {
        guard connectionStatus == .connected else { return }
        yunTaiDevice.gimbalCentering()
    }
    
    func gimbalTurnLeft() {
        guard connectionStatus == .connected else { return }
        yunTaiDevice.gimbalTurnLeftLimt()
    }
    
    func gimbalTurnRight() {
        guard connectionStatus == .connected else { return }
        yunTaiDevice.gimbalTurnRightLimt()
    }
    
    func setDirection(_ isPortrait: Bool) {
        print("设置竖屏\(isPortrait)")
        guard connectionStatus == .connected else { return }
        let direction: DirectionType = isPortrait ? DirectionTypePortrait : DirectionTypeLandscape
        connectManager.setDirection(direction)
    }
    
    func setRotationLimit(_ limit: Rotationlimit) {
        guard connectionStatus == .connected else { return }
        connectManager.setRotationlimit(limit)
    }
    
    func enableRotationLimit(_ enable: Bool) {
        guard connectionStatus == .connected else { return }
        connectManager.enableRotationlimit(enable)
    }
    
    // MARK: - Limit Settings Methods
    
    func setLeftLimit() {
        guard connectionStatus == .connected else { return }
        connectManager.setRotationlimit(RotationlimitLeft)
        print("设置左限位")
    }
    
    func setRightLimit() {
        guard connectionStatus == .connected else { return }
        connectManager.setRotationlimit(RotationlimitRight)
        print("设置右限位")
    }
    
    func goToLeftLimit() {
        guard connectionStatus == .connected else { return }
        yunTaiDevice.gimbalTurnLeftLimt()
        print("移动到左限位")
    }
    
    func goToRightLimit() {
        guard connectionStatus == .connected else { return }
        yunTaiDevice.gimbalTurnRightLimt()
        print("移动到右限位")
    }
    
    func enableLimit(_ enable: Bool) {
        guard connectionStatus == .connected else { return }
        connectManager.enableRotationlimit(enable)
        print(enable ? "启用限位" : "停用限位")
    }
}

// MARK: - HHConnListener

extension GimbalManager: HHConnListener {
    func onDeviceFound(_ deviceList: [HHDevice]) {
        print("onDeviceFound!!!!!!!")
        DispatchQueue.main.async {
            self.discoveredDevices = deviceList
        }
    }
    
    func onConnChange(_ event: ConnEvent) {
        DispatchQueue.main.async {
            print("云台连接状态变化: \(event.type), 描述: \(event.desc ?? "无描述")")
            
            switch event.type {
            case ConnStatus_ConnectSuccessful:
                self.connectionStatus = .connected
                self.errorMessage = ""
                print("云台连接成功")
                
                // 连接成功后延迟一下，让SDK完成内部初始化
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    print("云台连接稳定，可以开始控制")
                }
                
            case ConnStatus_ConnectFailure:
                self.connectionStatus = .error("连接失败")
                self.connectedDevice = nil
                self.errorMessage = "连接失败: \(event.desc ?? "未知错误")"
                print("云台连接失败: \(event.desc ?? "未知错误")")
                
            case ConnStatus_Disconnected:
                self.connectionStatus = .disconnected
                self.connectedDevice = nil
                self.errorMessage = ""
                print("云台连接断开")
                
            case ConnStatus_BLEPoweredOff:
                self.connectionStatus = .error("蓝牙未开启")
                self.errorMessage = "蓝牙未开启，请开启蓝牙后重试"
                print("蓝牙未开启")
                
            case ConnStatus_BLEUnauthorized:
                self.connectionStatus = .error("蓝牙未授权")
                self.errorMessage = "蓝牙权限未授权，请检查应用权限"
                print("蓝牙未授权")
                
            default:
                self.connectionStatus = .error("未知错误")
                self.errorMessage = "未知错误: \(event.desc ?? "")"
                print("未知连接事件: \(event.type.rawValue)")
            }
        }
    }
    
    func onDeviceStateChanged(_ event: StateEvent) {
        DispatchQueue.main.async {
            switch event.type {
            case DeviceStatus_LOW_BATTERY:
                self.errorMessage = "云台电量低"
                print("云台电量低")
                
            case DeviceStatus_OVERLOAD:
                self.errorMessage = "云台过载"
                print("云台过载")
                
            case DeviceStatus_HARDWARE_ERROR:
                self.errorMessage = "云台硬件错误"
                print("云台硬件错误")
                
            case DeviceStatus_LEFT_LIMIT:
                print("云台到达左限位")
                
            default:
                print("其他设备状态变化: \(event.type.rawValue)")
            }
        }
    }
}
