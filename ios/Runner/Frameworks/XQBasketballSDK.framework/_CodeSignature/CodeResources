<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/BluetoothParamInfo.h</key>
		<data>
		NKha6e+ZZmZabNA7mNSB+TsMTJU=
		</data>
		<key>Headers/HHConnectManager.h</key>
		<data>
		EavS6VZyQp6HzJMeYoHNkAOn0Fg=
		</data>
		<key>Headers/HHStateManager.h</key>
		<data>
		vSP78BGGhbuKxVacvh1tpZ9PXLE=
		</data>
		<key>Headers/HHTrackManager.h</key>
		<data>
		lyqWcoq7qlswUW7+/QO16hjW/Rs=
		</data>
		<key>Headers/HHYunTaiDevice.h</key>
		<data>
		UEXpEeGFCNV7Aku3ySJQLJfBTkQ=
		</data>
		<key>Headers/StateEvent.h</key>
		<data>
		/1uMenKXUmJfEfmbUgvU1Gz8tjw=
		</data>
		<key>Headers/TrackEvent.h</key>
		<data>
		QuSj/HanzoDx6dCpjaLyKMna/7Q=
		</data>
		<key>Headers/XQBasketballSDK.h</key>
		<data>
		x4lSlpZGbY8D1pxr++RPW/Z6pos=
		</data>
		<key>Info.plist</key>
		<data>
		flfYCfAWN2L1FoA7yxj/dkXA8lg=
		</data>
		<key>MQImageResizeFilter.fsh</key>
		<data>
		+37uGLIOgpeNH2H/tlMTrADIt+o=
		</data>
		<key>MQImageResizeFilter.vsh</key>
		<data>
		c92hjTK0gw+nRUrrWSicNmUsTBg=
		</data>
		<key>MQImageYUVResizeFilter.fsh</key>
		<data>
		QFMZhbCnB1lxrUGgq9aS+hB8Y6I=
		</data>
		<key>MQImageYUVResizeFilter.vsh</key>
		<data>
		c92hjTK0gw+nRUrrWSicNmUsTBg=
		</data>
		<key>hohemCoreMLModel_1.mlmodelc/analytics/coremldata.bin</key>
		<data>
		p/PjegSzXDvFPeUKH8hrZRxN3gQ=
		</data>
		<key>hohemCoreMLModel_1.mlmodelc/coremldata.bin</key>
		<data>
		ffG/WMsXXd+e7IQYOgogYjxdp5E=
		</data>
		<key>hohemCoreMLModel_1.mlmodelc/metadata.json</key>
		<data>
		Dyh4T63bqjiqoBfo8yVSHKTrpCU=
		</data>
		<key>hohemCoreMLModel_1.mlmodelc/model.mil</key>
		<data>
		1hqx1srWeDczxnMg1/sITZbpx/I=
		</data>
		<key>hohemCoreMLModel_1.mlmodelc/weights/weight.bin</key>
		<data>
		to8aJOimkjF+KkLwTn2p49CFRS4=
		</data>
		<key>hohemCoreMLModel_2.mlmodelc/analytics/coremldata.bin</key>
		<data>
		fOJyW4nlw8YfNTjyTQEu6UO+lqk=
		</data>
		<key>hohemCoreMLModel_2.mlmodelc/coremldata.bin</key>
		<data>
		44Mwy2pRZn0tVSkK6vgSXmendBg=
		</data>
		<key>hohemCoreMLModel_2.mlmodelc/metadata.json</key>
		<data>
		FeXvs7SDNQZ4W1x4rwNjY8PkEkU=
		</data>
		<key>hohemCoreMLModel_2.mlmodelc/model.mil</key>
		<data>
		v9lcdRqkWK4fYdObruYBbR+HZtg=
		</data>
		<key>hohemCoreMLModel_2.mlmodelc/weights/weight.bin</key>
		<data>
		yqsyIA5LCsWOcEaI4dz/687+O8c=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/BluetoothParamInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			NKha6e+ZZmZabNA7mNSB+TsMTJU=
			</data>
			<key>hash2</key>
			<data>
			F+dVBd1O77Q8mUnDuAbazTj1y38U+IyqPGAZCRNmXgU=
			</data>
		</dict>
		<key>Headers/HHConnectManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			EavS6VZyQp6HzJMeYoHNkAOn0Fg=
			</data>
			<key>hash2</key>
			<data>
			ZFX4jwrgoLSpn1qySEhv7aS1Q0pO18ZStTYp7BN808U=
			</data>
		</dict>
		<key>Headers/HHStateManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			vSP78BGGhbuKxVacvh1tpZ9PXLE=
			</data>
			<key>hash2</key>
			<data>
			yMqS1037/hGpiINRXg5mXAjlwET2U7G2VPOJEWQcEeA=
			</data>
		</dict>
		<key>Headers/HHTrackManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			lyqWcoq7qlswUW7+/QO16hjW/Rs=
			</data>
			<key>hash2</key>
			<data>
			yAp2DvRF7p4uLAmpsIhenuInaCCV8nZIvWDmGA/OAPI=
			</data>
		</dict>
		<key>Headers/HHYunTaiDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			UEXpEeGFCNV7Aku3ySJQLJfBTkQ=
			</data>
			<key>hash2</key>
			<data>
			7pr2Z4It2cSzWdLTKEkJM/X03/Fjl09xWGbZDCwEtIE=
			</data>
		</dict>
		<key>Headers/StateEvent.h</key>
		<dict>
			<key>hash</key>
			<data>
			/1uMenKXUmJfEfmbUgvU1Gz8tjw=
			</data>
			<key>hash2</key>
			<data>
			+LnmpMlJHnfCzpbbunSziImXveMqMDCDxpudYFGQQ/o=
			</data>
		</dict>
		<key>Headers/TrackEvent.h</key>
		<dict>
			<key>hash</key>
			<data>
			QuSj/HanzoDx6dCpjaLyKMna/7Q=
			</data>
			<key>hash2</key>
			<data>
			V9rS4yGJmNrkhuGGbPMhNT6SYkU6Ef0JE/yMwXANyV8=
			</data>
		</dict>
		<key>Headers/XQBasketballSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			x4lSlpZGbY8D1pxr++RPW/Z6pos=
			</data>
			<key>hash2</key>
			<data>
			wuqOTV9CH/GmiRfT5Ju6lKvwBTUHUi2qfB7ZlMRRmFk=
			</data>
		</dict>
		<key>MQImageResizeFilter.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			+37uGLIOgpeNH2H/tlMTrADIt+o=
			</data>
			<key>hash2</key>
			<data>
			lHwLiECsLf0vDDfSqrXl9lkAJmI48NH+I3ENQnUk0gE=
			</data>
		</dict>
		<key>MQImageResizeFilter.vsh</key>
		<dict>
			<key>hash</key>
			<data>
			c92hjTK0gw+nRUrrWSicNmUsTBg=
			</data>
			<key>hash2</key>
			<data>
			FEYLkwffIwIWIGKLWIEULKMj8jF4xbjVjQ3jgEFnw0w=
			</data>
		</dict>
		<key>MQImageYUVResizeFilter.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			QFMZhbCnB1lxrUGgq9aS+hB8Y6I=
			</data>
			<key>hash2</key>
			<data>
			znia13b8T895dgX+6bCezgoLqxKDK61sUuszpBHO714=
			</data>
		</dict>
		<key>MQImageYUVResizeFilter.vsh</key>
		<dict>
			<key>hash</key>
			<data>
			c92hjTK0gw+nRUrrWSicNmUsTBg=
			</data>
			<key>hash2</key>
			<data>
			FEYLkwffIwIWIGKLWIEULKMj8jF4xbjVjQ3jgEFnw0w=
			</data>
		</dict>
		<key>hohemCoreMLModel_1.mlmodelc/analytics/coremldata.bin</key>
		<dict>
			<key>hash</key>
			<data>
			p/PjegSzXDvFPeUKH8hrZRxN3gQ=
			</data>
			<key>hash2</key>
			<data>
			X6j7eCd3rWi15j0QldFKf7YsM2RHHVNHUfUFg33ijME=
			</data>
		</dict>
		<key>hohemCoreMLModel_1.mlmodelc/coremldata.bin</key>
		<dict>
			<key>hash</key>
			<data>
			ffG/WMsXXd+e7IQYOgogYjxdp5E=
			</data>
			<key>hash2</key>
			<data>
			fQlhE2xGU1C5N+s4kqt2eGeeTjiteSFlS3l9bpy8ssA=
			</data>
		</dict>
		<key>hohemCoreMLModel_1.mlmodelc/metadata.json</key>
		<dict>
			<key>hash</key>
			<data>
			Dyh4T63bqjiqoBfo8yVSHKTrpCU=
			</data>
			<key>hash2</key>
			<data>
			3lZE71LcU5Ii9slFLOa0AWdwGmyzROnxEFTldjkMmrc=
			</data>
		</dict>
		<key>hohemCoreMLModel_1.mlmodelc/model.mil</key>
		<dict>
			<key>hash</key>
			<data>
			1hqx1srWeDczxnMg1/sITZbpx/I=
			</data>
			<key>hash2</key>
			<data>
			TKzn/kykTl+A6SMGcIRIn1Lx1ajhMAIu5Vp8zrozEgA=
			</data>
		</dict>
		<key>hohemCoreMLModel_1.mlmodelc/weights/weight.bin</key>
		<dict>
			<key>hash</key>
			<data>
			to8aJOimkjF+KkLwTn2p49CFRS4=
			</data>
			<key>hash2</key>
			<data>
			+M01LD2wPZUciSoxhwD73XBC12FG8DLMouiPccgTsE0=
			</data>
		</dict>
		<key>hohemCoreMLModel_2.mlmodelc/analytics/coremldata.bin</key>
		<dict>
			<key>hash</key>
			<data>
			fOJyW4nlw8YfNTjyTQEu6UO+lqk=
			</data>
			<key>hash2</key>
			<data>
			1AeryVHtmEYxOze/cYNQAQrPXomkBh/x5MFa3Mfrpls=
			</data>
		</dict>
		<key>hohemCoreMLModel_2.mlmodelc/coremldata.bin</key>
		<dict>
			<key>hash</key>
			<data>
			44Mwy2pRZn0tVSkK6vgSXmendBg=
			</data>
			<key>hash2</key>
			<data>
			aMTWuaEi7h04q7d+D1L/IBLPTCkpHTptg6uSQpEye0Y=
			</data>
		</dict>
		<key>hohemCoreMLModel_2.mlmodelc/metadata.json</key>
		<dict>
			<key>hash</key>
			<data>
			FeXvs7SDNQZ4W1x4rwNjY8PkEkU=
			</data>
			<key>hash2</key>
			<data>
			2ftWXo1B2pOxV0ZHwy3cG63KY3F82ni5Dkc+Q0zvT0w=
			</data>
		</dict>
		<key>hohemCoreMLModel_2.mlmodelc/model.mil</key>
		<dict>
			<key>hash</key>
			<data>
			v9lcdRqkWK4fYdObruYBbR+HZtg=
			</data>
			<key>hash2</key>
			<data>
			vPvpCcQW87V9l1t6UiPbezcX9vIW8SfAUBKPrG19aU0=
			</data>
		</dict>
		<key>hohemCoreMLModel_2.mlmodelc/weights/weight.bin</key>
		<dict>
			<key>hash</key>
			<data>
			yqsyIA5LCsWOcEaI4dz/687+O8c=
			</data>
			<key>hash2</key>
			<data>
			rqFbkwAiPNVkXsZVPE25Yw2silbpOmDNwwmK8hqvUu0=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
