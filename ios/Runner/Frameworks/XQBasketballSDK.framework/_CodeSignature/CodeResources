<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/BluetoothParamInfo.h</key>
		<data>
		NKha6e+ZZmZabNA7mNSB+TsMTJU=
		</data>
		<key>Headers/HHConnectManager.h</key>
		<data>
		FbIJuwxDkneu3Ork4RKP5dVNeJo=
		</data>
		<key>Headers/HHStateManager.h</key>
		<data>
		bXdY8OGYLyETMYIqMY9x/dDJ3O0=
		</data>
		<key>Headers/HHTrackManager.h</key>
		<data>
		lyqWcoq7qlswUW7+/QO16hjW/Rs=
		</data>
		<key>Headers/HHYunTaiDevice.h</key>
		<data>
		iLKh/4ri4lhw14lXCTAw295SoMw=
		</data>
		<key>Headers/StateEvent.h</key>
		<data>
		NB6EtudAZI7tpiphxxBiC1MsUoo=
		</data>
		<key>Headers/TrackEvent.h</key>
		<data>
		QuSj/HanzoDx6dCpjaLyKMna/7Q=
		</data>
		<key>Headers/XQBasketballSDK.h</key>
		<data>
		x4lSlpZGbY8D1pxr++RPW/Z6pos=
		</data>
		<key>Info.plist</key>
		<data>
		Z7rS6MZ2+JdE6vVJBqIdAmQBB1Q=
		</data>
		<key>MQImageResizeFilter.fsh</key>
		<data>
		+37uGLIOgpeNH2H/tlMTrADIt+o=
		</data>
		<key>MQImageResizeFilter.vsh</key>
		<data>
		c92hjTK0gw+nRUrrWSicNmUsTBg=
		</data>
		<key>MQImageYUVResizeFilter.fsh</key>
		<data>
		QFMZhbCnB1lxrUGgq9aS+hB8Y6I=
		</data>
		<key>MQImageYUVResizeFilter.vsh</key>
		<data>
		c92hjTK0gw+nRUrrWSicNmUsTBg=
		</data>
		<key>hohemCoreMLModel_1.mlmodelc/analytics/coremldata.bin</key>
		<data>
		vurZMUi126ua3tidv3KL/8m5lqk=
		</data>
		<key>hohemCoreMLModel_1.mlmodelc/coremldata.bin</key>
		<data>
		CL3kRgFHcExpTZd/fDneYFzGEr8=
		</data>
		<key>hohemCoreMLModel_1.mlmodelc/encryptionInfo/coremldata.bin</key>
		<data>
		y70K/F+M+wAczFpt4nafX2VpA6A=
		</data>
		<key>hohemCoreMLModel_1.mlmodelc/model.mil</key>
		<data>
		ksp8Le17GVgTzW/sppyXijE21BQ=
		</data>
		<key>hohemCoreMLModel_1.mlmodelc/weights/weight.bin</key>
		<data>
		o2tA17Nyv7hV/RfmtzeT7RA/pIw=
		</data>
		<key>hohemCoreMLModel_2.mlmodelc/analytics/coremldata.bin</key>
		<data>
		UqzeE9uPFMcGszOavyTsHTHYDNs=
		</data>
		<key>hohemCoreMLModel_2.mlmodelc/coremldata.bin</key>
		<data>
		zzcBqoPmF3fJNEFyOH2UB4kjB3M=
		</data>
		<key>hohemCoreMLModel_2.mlmodelc/encryptionInfo/coremldata.bin</key>
		<data>
		vwHUQ/qXNGbo+CFIQwYJnzHBygs=
		</data>
		<key>hohemCoreMLModel_2.mlmodelc/model.mil</key>
		<data>
		ouL0HKo+QxdqnK3GDFQ4x+xH8k4=
		</data>
		<key>hohemCoreMLModel_2.mlmodelc/weights/weight.bin</key>
		<data>
		iYEPgCE0Ar1vf3ZtfS/W9vvD4uo=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/BluetoothParamInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			NKha6e+ZZmZabNA7mNSB+TsMTJU=
			</data>
			<key>hash2</key>
			<data>
			F+dVBd1O77Q8mUnDuAbazTj1y38U+IyqPGAZCRNmXgU=
			</data>
		</dict>
		<key>Headers/HHConnectManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			FbIJuwxDkneu3Ork4RKP5dVNeJo=
			</data>
			<key>hash2</key>
			<data>
			Hfeg8Z1FQHnPaMQHu0dG0XqYm7Zjlt5QPqFibEWCUcU=
			</data>
		</dict>
		<key>Headers/HHStateManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			bXdY8OGYLyETMYIqMY9x/dDJ3O0=
			</data>
			<key>hash2</key>
			<data>
			P7KsIuApmjWER/esasQF8Ttgs8Y7LX9Jg7htIL7zcUU=
			</data>
		</dict>
		<key>Headers/HHTrackManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			lyqWcoq7qlswUW7+/QO16hjW/Rs=
			</data>
			<key>hash2</key>
			<data>
			yAp2DvRF7p4uLAmpsIhenuInaCCV8nZIvWDmGA/OAPI=
			</data>
		</dict>
		<key>Headers/HHYunTaiDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			iLKh/4ri4lhw14lXCTAw295SoMw=
			</data>
			<key>hash2</key>
			<data>
			wSLqWGhLD0IZosUt+YZ1nNF30dJf8W/B1eUTcFVRkj0=
			</data>
		</dict>
		<key>Headers/StateEvent.h</key>
		<dict>
			<key>hash</key>
			<data>
			NB6EtudAZI7tpiphxxBiC1MsUoo=
			</data>
			<key>hash2</key>
			<data>
			jvBfzC4pVBOzMCraeMHjYRbuD/hMxVmdUG8lL5ITlm0=
			</data>
		</dict>
		<key>Headers/TrackEvent.h</key>
		<dict>
			<key>hash</key>
			<data>
			QuSj/HanzoDx6dCpjaLyKMna/7Q=
			</data>
			<key>hash2</key>
			<data>
			V9rS4yGJmNrkhuGGbPMhNT6SYkU6Ef0JE/yMwXANyV8=
			</data>
		</dict>
		<key>Headers/XQBasketballSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			x4lSlpZGbY8D1pxr++RPW/Z6pos=
			</data>
			<key>hash2</key>
			<data>
			wuqOTV9CH/GmiRfT5Ju6lKvwBTUHUi2qfB7ZlMRRmFk=
			</data>
		</dict>
		<key>MQImageResizeFilter.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			+37uGLIOgpeNH2H/tlMTrADIt+o=
			</data>
			<key>hash2</key>
			<data>
			lHwLiECsLf0vDDfSqrXl9lkAJmI48NH+I3ENQnUk0gE=
			</data>
		</dict>
		<key>MQImageResizeFilter.vsh</key>
		<dict>
			<key>hash</key>
			<data>
			c92hjTK0gw+nRUrrWSicNmUsTBg=
			</data>
			<key>hash2</key>
			<data>
			FEYLkwffIwIWIGKLWIEULKMj8jF4xbjVjQ3jgEFnw0w=
			</data>
		</dict>
		<key>MQImageYUVResizeFilter.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			QFMZhbCnB1lxrUGgq9aS+hB8Y6I=
			</data>
			<key>hash2</key>
			<data>
			znia13b8T895dgX+6bCezgoLqxKDK61sUuszpBHO714=
			</data>
		</dict>
		<key>MQImageYUVResizeFilter.vsh</key>
		<dict>
			<key>hash</key>
			<data>
			c92hjTK0gw+nRUrrWSicNmUsTBg=
			</data>
			<key>hash2</key>
			<data>
			FEYLkwffIwIWIGKLWIEULKMj8jF4xbjVjQ3jgEFnw0w=
			</data>
		</dict>
		<key>hohemCoreMLModel_1.mlmodelc/analytics/coremldata.bin</key>
		<dict>
			<key>hash</key>
			<data>
			vurZMUi126ua3tidv3KL/8m5lqk=
			</data>
			<key>hash2</key>
			<data>
			OEGQTmZem5ClKTdbv8pGLX3TVWtte/Wqg672HXzhc9A=
			</data>
		</dict>
		<key>hohemCoreMLModel_1.mlmodelc/coremldata.bin</key>
		<dict>
			<key>hash</key>
			<data>
			CL3kRgFHcExpTZd/fDneYFzGEr8=
			</data>
			<key>hash2</key>
			<data>
			86Wskt/1tmkGsapmZotYET3ALwRqZMhsUzTNWNoG0zg=
			</data>
		</dict>
		<key>hohemCoreMLModel_1.mlmodelc/encryptionInfo/coremldata.bin</key>
		<dict>
			<key>hash</key>
			<data>
			y70K/F+M+wAczFpt4nafX2VpA6A=
			</data>
			<key>hash2</key>
			<data>
			Jn4GFbhErMeHgnSlyPJihh3vDCxxgS2hGQ6IWYvdWJA=
			</data>
		</dict>
		<key>hohemCoreMLModel_1.mlmodelc/model.mil</key>
		<dict>
			<key>hash</key>
			<data>
			ksp8Le17GVgTzW/sppyXijE21BQ=
			</data>
			<key>hash2</key>
			<data>
			0qeAUBEk/9o0CAkn3Ft0HGt66tY7JY2EWBNeU7woqbY=
			</data>
		</dict>
		<key>hohemCoreMLModel_1.mlmodelc/weights/weight.bin</key>
		<dict>
			<key>hash</key>
			<data>
			o2tA17Nyv7hV/RfmtzeT7RA/pIw=
			</data>
			<key>hash2</key>
			<data>
			kq6K0rzOdxeiRpMjfAB22XFvmXMvH5+DL/tmBZHqp+w=
			</data>
		</dict>
		<key>hohemCoreMLModel_2.mlmodelc/analytics/coremldata.bin</key>
		<dict>
			<key>hash</key>
			<data>
			UqzeE9uPFMcGszOavyTsHTHYDNs=
			</data>
			<key>hash2</key>
			<data>
			hbVAKD0lq0m8GL1dMn1CXAQbBTDtFRQ1gyHPpo/VKtA=
			</data>
		</dict>
		<key>hohemCoreMLModel_2.mlmodelc/coremldata.bin</key>
		<dict>
			<key>hash</key>
			<data>
			zzcBqoPmF3fJNEFyOH2UB4kjB3M=
			</data>
			<key>hash2</key>
			<data>
			DXrWeaE0suBevNgKBEVOG2KJL6GuSLJ02maitcasxNw=
			</data>
		</dict>
		<key>hohemCoreMLModel_2.mlmodelc/encryptionInfo/coremldata.bin</key>
		<dict>
			<key>hash</key>
			<data>
			vwHUQ/qXNGbo+CFIQwYJnzHBygs=
			</data>
			<key>hash2</key>
			<data>
			39vlqORnDPcBVwS6P97y49Je/2b5BNFNF7/Vfvs2mdM=
			</data>
		</dict>
		<key>hohemCoreMLModel_2.mlmodelc/model.mil</key>
		<dict>
			<key>hash</key>
			<data>
			ouL0HKo+QxdqnK3GDFQ4x+xH8k4=
			</data>
			<key>hash2</key>
			<data>
			fT9gyPSnHFM9gwNPUi/nDnlJf0RfN0U9Q7YcjrgTzw0=
			</data>
		</dict>
		<key>hohemCoreMLModel_2.mlmodelc/weights/weight.bin</key>
		<dict>
			<key>hash</key>
			<data>
			iYEPgCE0Ar1vf3ZtfS/W9vvD4uo=
			</data>
			<key>hash2</key>
			<data>
			8oBNeD64aSXzdX9yP4JkF/vrTQuhW1wYhaAkXvasN5s=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
