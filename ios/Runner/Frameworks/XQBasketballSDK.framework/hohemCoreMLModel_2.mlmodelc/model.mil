program(1.0)
[buildInfo = dict<tensor<string, []>, tensor<string, []>>({{"coremlc-component-MIL", "3405.2.1"}, {"coremlc-version", "3404.23.1"}, {"coremltools-component-torch", "2.5.1+cu121"}, {"coremltools-source-dialect", "TorchScript"}, {"coremltools-version", "8.2"}})]
{
    func main<ios15>(tensor<fp32, [1, 3, 640, 640]> image) {
            tensor<fp32, []> image__scaled___y_0 = const()[name = tensor<string, []>("image__scaled___y_0"), val = tensor<fp32, []>(0x1.010102p-8)];
            tensor<fp32, [1, 3, 640, 640]> image__scaled__ = mul(x = image, y = image__scaled___y_0)[name = tensor<string, []>("image__scaled__")];
            tensor<string, []> input_1_pad_type_0 = const()[name = tensor<string, []>("input_1_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_1_pad_0 = const()[name = tensor<string, []>("input_1_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_1_strides_0 = const()[name = tensor<string, []>("input_1_strides_0"), val = tensor<int32, [2]>([2, 2])];
            tensor<int32, [2]> input_1_dilations_0 = const()[name = tensor<string, []>("input_1_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_1_groups_0 = const()[name = tensor<string, []>("input_1_groups_0"), val = tensor<int32, []>(1)];
            tensor<string, []> image_to_fp16_dtype_0 = const()[name = tensor<string, []>("image_to_fp16_dtype_0"), val = tensor<string, []>("fp16")];
            tensor<fp16, [16, 3, 3, 3]> model_0_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_0_conv_weight_to_fp16"), val = tensor<fp16, [16, 3, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(64)))];
            tensor<fp16, [16]> model_0_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_0_conv_bias_to_fp16"), val = tensor<fp16, [16]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(1024)))];
            tensor<fp16, [1, 3, 640, 640]> image_to_fp16 = cast(dtype = image_to_fp16_dtype_0, x = image__scaled__)[name = tensor<string, []>("cast_8")];
            tensor<fp16, [1, 16, 320, 320]> input_1_cast_fp16 = conv(bias = model_0_conv_bias_to_fp16, dilations = input_1_dilations_0, groups = input_1_groups_0, pad = input_1_pad_0, pad_type = input_1_pad_type_0, strides = input_1_strides_0, weight = model_0_conv_weight_to_fp16, x = image_to_fp16)[name = tensor<string, []>("input_1_cast_fp16")];
            tensor<fp16, [1, 16, 320, 320]> input_3_cast_fp16 = silu(x = input_1_cast_fp16)[name = tensor<string, []>("input_3_cast_fp16")];
            tensor<string, []> input_5_pad_type_0 = const()[name = tensor<string, []>("input_5_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_5_pad_0 = const()[name = tensor<string, []>("input_5_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_5_strides_0 = const()[name = tensor<string, []>("input_5_strides_0"), val = tensor<int32, [2]>([2, 2])];
            tensor<int32, [2]> input_5_dilations_0 = const()[name = tensor<string, []>("input_5_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_5_groups_0 = const()[name = tensor<string, []>("input_5_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [32, 16, 3, 3]> model_1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_1_conv_weight_to_fp16"), val = tensor<fp16, [32, 16, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(1152)))];
            tensor<fp16, [32]> model_1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_1_conv_bias_to_fp16"), val = tensor<fp16, [32]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(10432)))];
            tensor<fp16, [1, 32, 160, 160]> input_5_cast_fp16 = conv(bias = model_1_conv_bias_to_fp16, dilations = input_5_dilations_0, groups = input_5_groups_0, pad = input_5_pad_0, pad_type = input_5_pad_type_0, strides = input_5_strides_0, weight = model_1_conv_weight_to_fp16, x = input_3_cast_fp16)[name = tensor<string, []>("input_5_cast_fp16")];
            tensor<fp16, [1, 32, 160, 160]> input_7_cast_fp16 = silu(x = input_5_cast_fp16)[name = tensor<string, []>("input_7_cast_fp16")];
            tensor<int32, []> var_68 = const()[name = tensor<string, []>("op_68"), val = tensor<int32, []>(1)];
            tensor<string, []> input_9_pad_type_0 = const()[name = tensor<string, []>("input_9_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_9_strides_0 = const()[name = tensor<string, []>("input_9_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_9_pad_0 = const()[name = tensor<string, []>("input_9_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_9_dilations_0 = const()[name = tensor<string, []>("input_9_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_9_groups_0 = const()[name = tensor<string, []>("input_9_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [32, 32, 1, 1]> model_2_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_2_cv1_conv_weight_to_fp16"), val = tensor<fp16, [32, 32, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(10560)))];
            tensor<fp16, [32]> model_2_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_2_cv1_conv_bias_to_fp16"), val = tensor<fp16, [32]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(12672)))];
            tensor<fp16, [1, 32, 160, 160]> input_9_cast_fp16 = conv(bias = model_2_cv1_conv_bias_to_fp16, dilations = input_9_dilations_0, groups = input_9_groups_0, pad = input_9_pad_0, pad_type = input_9_pad_type_0, strides = input_9_strides_0, weight = model_2_cv1_conv_weight_to_fp16, x = input_7_cast_fp16)[name = tensor<string, []>("input_9_cast_fp16")];
            tensor<fp16, [1, 32, 160, 160]> var_81_cast_fp16 = silu(x = input_9_cast_fp16)[name = tensor<string, []>("op_81_cast_fp16")];
            tensor<int32, [2]> var_82 = const()[name = tensor<string, []>("op_82"), val = tensor<int32, [2]>([16, 16])];
            tensor<int32, []> var_83_axis_0 = const()[name = tensor<string, []>("op_83_axis_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [1, 16, 160, 160]> var_83_cast_fp16_0, tensor<fp16, [1, 16, 160, 160]> var_83_cast_fp16_1 = split(axis = var_83_axis_0, split_sizes = var_82, x = var_81_cast_fp16)[name = tensor<string, []>("op_83_cast_fp16")];
            tensor<string, []> input_13_pad_type_0 = const()[name = tensor<string, []>("input_13_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_13_pad_0 = const()[name = tensor<string, []>("input_13_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_13_strides_0 = const()[name = tensor<string, []>("input_13_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_13_dilations_0 = const()[name = tensor<string, []>("input_13_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_13_groups_0 = const()[name = tensor<string, []>("input_13_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [16, 16, 3, 3]> model_2_m_0_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_2_m_0_cv1_conv_weight_to_fp16"), val = tensor<fp16, [16, 16, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(12800)))];
            tensor<fp16, [16]> model_2_m_0_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_2_m_0_cv1_conv_bias_to_fp16"), val = tensor<fp16, [16]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(17472)))];
            tensor<fp16, [1, 16, 160, 160]> input_13_cast_fp16 = conv(bias = model_2_m_0_cv1_conv_bias_to_fp16, dilations = input_13_dilations_0, groups = input_13_groups_0, pad = input_13_pad_0, pad_type = input_13_pad_type_0, strides = input_13_strides_0, weight = model_2_m_0_cv1_conv_weight_to_fp16, x = var_83_cast_fp16_1)[name = tensor<string, []>("input_13_cast_fp16")];
            tensor<fp16, [1, 16, 160, 160]> input_15_cast_fp16 = silu(x = input_13_cast_fp16)[name = tensor<string, []>("input_15_cast_fp16")];
            tensor<string, []> input_17_pad_type_0 = const()[name = tensor<string, []>("input_17_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_17_pad_0 = const()[name = tensor<string, []>("input_17_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_17_strides_0 = const()[name = tensor<string, []>("input_17_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_17_dilations_0 = const()[name = tensor<string, []>("input_17_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_17_groups_0 = const()[name = tensor<string, []>("input_17_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [16, 16, 3, 3]> model_2_m_0_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_2_m_0_cv2_conv_weight_to_fp16"), val = tensor<fp16, [16, 16, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(17600)))];
            tensor<fp16, [16]> model_2_m_0_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_2_m_0_cv2_conv_bias_to_fp16"), val = tensor<fp16, [16]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(22272)))];
            tensor<fp16, [1, 16, 160, 160]> input_17_cast_fp16 = conv(bias = model_2_m_0_cv2_conv_bias_to_fp16, dilations = input_17_dilations_0, groups = input_17_groups_0, pad = input_17_pad_0, pad_type = input_17_pad_type_0, strides = input_17_strides_0, weight = model_2_m_0_cv2_conv_weight_to_fp16, x = input_15_cast_fp16)[name = tensor<string, []>("input_17_cast_fp16")];
            tensor<fp16, [1, 16, 160, 160]> var_105_cast_fp16 = silu(x = input_17_cast_fp16)[name = tensor<string, []>("op_105_cast_fp16")];
            tensor<fp16, [1, 16, 160, 160]> var_106_cast_fp16 = add(x = var_83_cast_fp16_1, y = var_105_cast_fp16)[name = tensor<string, []>("op_106_cast_fp16")];
            tensor<bool, []> input_19_interleave_0 = const()[name = tensor<string, []>("input_19_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 48, 160, 160]> input_19_cast_fp16 = concat(axis = var_68, interleave = input_19_interleave_0, values = (var_83_cast_fp16_0, var_83_cast_fp16_1, var_106_cast_fp16))[name = tensor<string, []>("input_19_cast_fp16")];
            tensor<string, []> input_21_pad_type_0 = const()[name = tensor<string, []>("input_21_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_21_strides_0 = const()[name = tensor<string, []>("input_21_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_21_pad_0 = const()[name = tensor<string, []>("input_21_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_21_dilations_0 = const()[name = tensor<string, []>("input_21_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_21_groups_0 = const()[name = tensor<string, []>("input_21_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [32, 48, 1, 1]> model_2_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_2_cv2_conv_weight_to_fp16"), val = tensor<fp16, [32, 48, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(22400)))];
            tensor<fp16, [32]> model_2_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_2_cv2_conv_bias_to_fp16"), val = tensor<fp16, [32]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(25536)))];
            tensor<fp16, [1, 32, 160, 160]> input_21_cast_fp16 = conv(bias = model_2_cv2_conv_bias_to_fp16, dilations = input_21_dilations_0, groups = input_21_groups_0, pad = input_21_pad_0, pad_type = input_21_pad_type_0, strides = input_21_strides_0, weight = model_2_cv2_conv_weight_to_fp16, x = input_19_cast_fp16)[name = tensor<string, []>("input_21_cast_fp16")];
            tensor<fp16, [1, 32, 160, 160]> input_23_cast_fp16 = silu(x = input_21_cast_fp16)[name = tensor<string, []>("input_23_cast_fp16")];
            tensor<string, []> input_25_pad_type_0 = const()[name = tensor<string, []>("input_25_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_25_pad_0 = const()[name = tensor<string, []>("input_25_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_25_strides_0 = const()[name = tensor<string, []>("input_25_strides_0"), val = tensor<int32, [2]>([2, 2])];
            tensor<int32, [2]> input_25_dilations_0 = const()[name = tensor<string, []>("input_25_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_25_groups_0 = const()[name = tensor<string, []>("input_25_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 32, 3, 3]> model_3_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_3_conv_weight_to_fp16"), val = tensor<fp16, [64, 32, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(25664)))];
            tensor<fp16, [64]> model_3_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_3_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(62592)))];
            tensor<fp16, [1, 64, 80, 80]> input_25_cast_fp16 = conv(bias = model_3_conv_bias_to_fp16, dilations = input_25_dilations_0, groups = input_25_groups_0, pad = input_25_pad_0, pad_type = input_25_pad_type_0, strides = input_25_strides_0, weight = model_3_conv_weight_to_fp16, x = input_23_cast_fp16)[name = tensor<string, []>("input_25_cast_fp16")];
            tensor<fp16, [1, 64, 80, 80]> input_27_cast_fp16 = silu(x = input_25_cast_fp16)[name = tensor<string, []>("input_27_cast_fp16")];
            tensor<int32, []> var_136 = const()[name = tensor<string, []>("op_136"), val = tensor<int32, []>(1)];
            tensor<string, []> input_29_pad_type_0 = const()[name = tensor<string, []>("input_29_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_29_strides_0 = const()[name = tensor<string, []>("input_29_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_29_pad_0 = const()[name = tensor<string, []>("input_29_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_29_dilations_0 = const()[name = tensor<string, []>("input_29_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_29_groups_0 = const()[name = tensor<string, []>("input_29_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 1, 1]> model_4_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_4_cv1_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(62784)))];
            tensor<fp16, [64]> model_4_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_4_cv1_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(71040)))];
            tensor<fp16, [1, 64, 80, 80]> input_29_cast_fp16 = conv(bias = model_4_cv1_conv_bias_to_fp16, dilations = input_29_dilations_0, groups = input_29_groups_0, pad = input_29_pad_0, pad_type = input_29_pad_type_0, strides = input_29_strides_0, weight = model_4_cv1_conv_weight_to_fp16, x = input_27_cast_fp16)[name = tensor<string, []>("input_29_cast_fp16")];
            tensor<fp16, [1, 64, 80, 80]> var_151_cast_fp16 = silu(x = input_29_cast_fp16)[name = tensor<string, []>("op_151_cast_fp16")];
            tensor<int32, [2]> var_152 = const()[name = tensor<string, []>("op_152"), val = tensor<int32, [2]>([32, 32])];
            tensor<int32, []> var_153_axis_0 = const()[name = tensor<string, []>("op_153_axis_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [1, 32, 80, 80]> var_153_cast_fp16_0, tensor<fp16, [1, 32, 80, 80]> var_153_cast_fp16_1 = split(axis = var_153_axis_0, split_sizes = var_152, x = var_151_cast_fp16)[name = tensor<string, []>("op_153_cast_fp16")];
            tensor<string, []> input_33_pad_type_0 = const()[name = tensor<string, []>("input_33_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_33_pad_0 = const()[name = tensor<string, []>("input_33_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_33_strides_0 = const()[name = tensor<string, []>("input_33_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_33_dilations_0 = const()[name = tensor<string, []>("input_33_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_33_groups_0 = const()[name = tensor<string, []>("input_33_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [32, 32, 3, 3]> model_4_m_0_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_4_m_0_cv1_conv_weight_to_fp16"), val = tensor<fp16, [32, 32, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(71232)))];
            tensor<fp16, [32]> model_4_m_0_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_4_m_0_cv1_conv_bias_to_fp16"), val = tensor<fp16, [32]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(89728)))];
            tensor<fp16, [1, 32, 80, 80]> input_33_cast_fp16 = conv(bias = model_4_m_0_cv1_conv_bias_to_fp16, dilations = input_33_dilations_0, groups = input_33_groups_0, pad = input_33_pad_0, pad_type = input_33_pad_type_0, strides = input_33_strides_0, weight = model_4_m_0_cv1_conv_weight_to_fp16, x = var_153_cast_fp16_1)[name = tensor<string, []>("input_33_cast_fp16")];
            tensor<fp16, [1, 32, 80, 80]> input_35_cast_fp16 = silu(x = input_33_cast_fp16)[name = tensor<string, []>("input_35_cast_fp16")];
            tensor<string, []> input_37_pad_type_0 = const()[name = tensor<string, []>("input_37_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_37_pad_0 = const()[name = tensor<string, []>("input_37_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_37_strides_0 = const()[name = tensor<string, []>("input_37_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_37_dilations_0 = const()[name = tensor<string, []>("input_37_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_37_groups_0 = const()[name = tensor<string, []>("input_37_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [32, 32, 3, 3]> model_4_m_0_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_4_m_0_cv2_conv_weight_to_fp16"), val = tensor<fp16, [32, 32, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(89856)))];
            tensor<fp16, [32]> model_4_m_0_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_4_m_0_cv2_conv_bias_to_fp16"), val = tensor<fp16, [32]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(108352)))];
            tensor<fp16, [1, 32, 80, 80]> input_37_cast_fp16 = conv(bias = model_4_m_0_cv2_conv_bias_to_fp16, dilations = input_37_dilations_0, groups = input_37_groups_0, pad = input_37_pad_0, pad_type = input_37_pad_type_0, strides = input_37_strides_0, weight = model_4_m_0_cv2_conv_weight_to_fp16, x = input_35_cast_fp16)[name = tensor<string, []>("input_37_cast_fp16")];
            tensor<fp16, [1, 32, 80, 80]> var_175_cast_fp16 = silu(x = input_37_cast_fp16)[name = tensor<string, []>("op_175_cast_fp16")];
            tensor<fp16, [1, 32, 80, 80]> input_39_cast_fp16 = add(x = var_153_cast_fp16_1, y = var_175_cast_fp16)[name = tensor<string, []>("input_39_cast_fp16")];
            tensor<string, []> input_41_pad_type_0 = const()[name = tensor<string, []>("input_41_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_41_pad_0 = const()[name = tensor<string, []>("input_41_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_41_strides_0 = const()[name = tensor<string, []>("input_41_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_41_dilations_0 = const()[name = tensor<string, []>("input_41_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_41_groups_0 = const()[name = tensor<string, []>("input_41_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [32, 32, 3, 3]> model_4_m_1_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_4_m_1_cv1_conv_weight_to_fp16"), val = tensor<fp16, [32, 32, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(108480)))];
            tensor<fp16, [32]> model_4_m_1_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_4_m_1_cv1_conv_bias_to_fp16"), val = tensor<fp16, [32]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(126976)))];
            tensor<fp16, [1, 32, 80, 80]> input_41_cast_fp16 = conv(bias = model_4_m_1_cv1_conv_bias_to_fp16, dilations = input_41_dilations_0, groups = input_41_groups_0, pad = input_41_pad_0, pad_type = input_41_pad_type_0, strides = input_41_strides_0, weight = model_4_m_1_cv1_conv_weight_to_fp16, x = input_39_cast_fp16)[name = tensor<string, []>("input_41_cast_fp16")];
            tensor<fp16, [1, 32, 80, 80]> input_43_cast_fp16 = silu(x = input_41_cast_fp16)[name = tensor<string, []>("input_43_cast_fp16")];
            tensor<string, []> input_45_pad_type_0 = const()[name = tensor<string, []>("input_45_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_45_pad_0 = const()[name = tensor<string, []>("input_45_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_45_strides_0 = const()[name = tensor<string, []>("input_45_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_45_dilations_0 = const()[name = tensor<string, []>("input_45_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_45_groups_0 = const()[name = tensor<string, []>("input_45_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [32, 32, 3, 3]> model_4_m_1_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_4_m_1_cv2_conv_weight_to_fp16"), val = tensor<fp16, [32, 32, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(127104)))];
            tensor<fp16, [32]> model_4_m_1_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_4_m_1_cv2_conv_bias_to_fp16"), val = tensor<fp16, [32]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(145600)))];
            tensor<fp16, [1, 32, 80, 80]> input_45_cast_fp16 = conv(bias = model_4_m_1_cv2_conv_bias_to_fp16, dilations = input_45_dilations_0, groups = input_45_groups_0, pad = input_45_pad_0, pad_type = input_45_pad_type_0, strides = input_45_strides_0, weight = model_4_m_1_cv2_conv_weight_to_fp16, x = input_43_cast_fp16)[name = tensor<string, []>("input_45_cast_fp16")];
            tensor<fp16, [1, 32, 80, 80]> var_196_cast_fp16 = silu(x = input_45_cast_fp16)[name = tensor<string, []>("op_196_cast_fp16")];
            tensor<fp16, [1, 32, 80, 80]> var_197_cast_fp16 = add(x = input_39_cast_fp16, y = var_196_cast_fp16)[name = tensor<string, []>("op_197_cast_fp16")];
            tensor<bool, []> input_47_interleave_0 = const()[name = tensor<string, []>("input_47_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 128, 80, 80]> input_47_cast_fp16 = concat(axis = var_136, interleave = input_47_interleave_0, values = (var_153_cast_fp16_0, var_153_cast_fp16_1, input_39_cast_fp16, var_197_cast_fp16))[name = tensor<string, []>("input_47_cast_fp16")];
            tensor<string, []> input_49_pad_type_0 = const()[name = tensor<string, []>("input_49_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_49_strides_0 = const()[name = tensor<string, []>("input_49_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_49_pad_0 = const()[name = tensor<string, []>("input_49_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_49_dilations_0 = const()[name = tensor<string, []>("input_49_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_49_groups_0 = const()[name = tensor<string, []>("input_49_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 128, 1, 1]> model_4_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_4_cv2_conv_weight_to_fp16"), val = tensor<fp16, [64, 128, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(145728)))];
            tensor<fp16, [64]> model_4_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_4_cv2_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(162176)))];
            tensor<fp16, [1, 64, 80, 80]> input_49_cast_fp16 = conv(bias = model_4_cv2_conv_bias_to_fp16, dilations = input_49_dilations_0, groups = input_49_groups_0, pad = input_49_pad_0, pad_type = input_49_pad_type_0, strides = input_49_strides_0, weight = model_4_cv2_conv_weight_to_fp16, x = input_47_cast_fp16)[name = tensor<string, []>("input_49_cast_fp16")];
            tensor<fp16, [1, 64, 80, 80]> input_51_cast_fp16 = silu(x = input_49_cast_fp16)[name = tensor<string, []>("input_51_cast_fp16")];
            tensor<string, []> input_53_pad_type_0 = const()[name = tensor<string, []>("input_53_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_53_pad_0 = const()[name = tensor<string, []>("input_53_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_53_strides_0 = const()[name = tensor<string, []>("input_53_strides_0"), val = tensor<int32, [2]>([2, 2])];
            tensor<int32, [2]> input_53_dilations_0 = const()[name = tensor<string, []>("input_53_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_53_groups_0 = const()[name = tensor<string, []>("input_53_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [128, 64, 3, 3]> model_5_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_5_conv_weight_to_fp16"), val = tensor<fp16, [128, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(162368)))];
            tensor<fp16, [128]> model_5_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_5_conv_bias_to_fp16"), val = tensor<fp16, [128]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(309888)))];
            tensor<fp16, [1, 128, 40, 40]> input_53_cast_fp16 = conv(bias = model_5_conv_bias_to_fp16, dilations = input_53_dilations_0, groups = input_53_groups_0, pad = input_53_pad_0, pad_type = input_53_pad_type_0, strides = input_53_strides_0, weight = model_5_conv_weight_to_fp16, x = input_51_cast_fp16)[name = tensor<string, []>("input_53_cast_fp16")];
            tensor<fp16, [1, 128, 40, 40]> input_55_cast_fp16 = silu(x = input_53_cast_fp16)[name = tensor<string, []>("input_55_cast_fp16")];
            tensor<int32, []> var_227 = const()[name = tensor<string, []>("op_227"), val = tensor<int32, []>(1)];
            tensor<string, []> input_57_pad_type_0 = const()[name = tensor<string, []>("input_57_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_57_strides_0 = const()[name = tensor<string, []>("input_57_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_57_pad_0 = const()[name = tensor<string, []>("input_57_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_57_dilations_0 = const()[name = tensor<string, []>("input_57_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_57_groups_0 = const()[name = tensor<string, []>("input_57_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [128, 128, 1, 1]> model_6_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_6_cv1_conv_weight_to_fp16"), val = tensor<fp16, [128, 128, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(310208)))];
            tensor<fp16, [128]> model_6_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_6_cv1_conv_bias_to_fp16"), val = tensor<fp16, [128]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(343040)))];
            tensor<fp16, [1, 128, 40, 40]> input_57_cast_fp16 = conv(bias = model_6_cv1_conv_bias_to_fp16, dilations = input_57_dilations_0, groups = input_57_groups_0, pad = input_57_pad_0, pad_type = input_57_pad_type_0, strides = input_57_strides_0, weight = model_6_cv1_conv_weight_to_fp16, x = input_55_cast_fp16)[name = tensor<string, []>("input_57_cast_fp16")];
            tensor<fp16, [1, 128, 40, 40]> var_242_cast_fp16 = silu(x = input_57_cast_fp16)[name = tensor<string, []>("op_242_cast_fp16")];
            tensor<int32, [2]> var_243 = const()[name = tensor<string, []>("op_243"), val = tensor<int32, [2]>([64, 64])];
            tensor<int32, []> var_244_axis_0 = const()[name = tensor<string, []>("op_244_axis_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [1, 64, 40, 40]> var_244_cast_fp16_0, tensor<fp16, [1, 64, 40, 40]> var_244_cast_fp16_1 = split(axis = var_244_axis_0, split_sizes = var_243, x = var_242_cast_fp16)[name = tensor<string, []>("op_244_cast_fp16")];
            tensor<string, []> input_61_pad_type_0 = const()[name = tensor<string, []>("input_61_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_61_pad_0 = const()[name = tensor<string, []>("input_61_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_61_strides_0 = const()[name = tensor<string, []>("input_61_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_61_dilations_0 = const()[name = tensor<string, []>("input_61_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_61_groups_0 = const()[name = tensor<string, []>("input_61_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_6_m_0_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_6_m_0_cv1_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(343360)))];
            tensor<fp16, [64]> model_6_m_0_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_6_m_0_cv1_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(417152)))];
            tensor<fp16, [1, 64, 40, 40]> input_61_cast_fp16 = conv(bias = model_6_m_0_cv1_conv_bias_to_fp16, dilations = input_61_dilations_0, groups = input_61_groups_0, pad = input_61_pad_0, pad_type = input_61_pad_type_0, strides = input_61_strides_0, weight = model_6_m_0_cv1_conv_weight_to_fp16, x = var_244_cast_fp16_1)[name = tensor<string, []>("input_61_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> input_63_cast_fp16 = silu(x = input_61_cast_fp16)[name = tensor<string, []>("input_63_cast_fp16")];
            tensor<string, []> input_65_pad_type_0 = const()[name = tensor<string, []>("input_65_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_65_pad_0 = const()[name = tensor<string, []>("input_65_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_65_strides_0 = const()[name = tensor<string, []>("input_65_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_65_dilations_0 = const()[name = tensor<string, []>("input_65_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_65_groups_0 = const()[name = tensor<string, []>("input_65_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_6_m_0_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_6_m_0_cv2_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(417344)))];
            tensor<fp16, [64]> model_6_m_0_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_6_m_0_cv2_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(491136)))];
            tensor<fp16, [1, 64, 40, 40]> input_65_cast_fp16 = conv(bias = model_6_m_0_cv2_conv_bias_to_fp16, dilations = input_65_dilations_0, groups = input_65_groups_0, pad = input_65_pad_0, pad_type = input_65_pad_type_0, strides = input_65_strides_0, weight = model_6_m_0_cv2_conv_weight_to_fp16, x = input_63_cast_fp16)[name = tensor<string, []>("input_65_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> var_266_cast_fp16 = silu(x = input_65_cast_fp16)[name = tensor<string, []>("op_266_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> input_67_cast_fp16 = add(x = var_244_cast_fp16_1, y = var_266_cast_fp16)[name = tensor<string, []>("input_67_cast_fp16")];
            tensor<string, []> input_69_pad_type_0 = const()[name = tensor<string, []>("input_69_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_69_pad_0 = const()[name = tensor<string, []>("input_69_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_69_strides_0 = const()[name = tensor<string, []>("input_69_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_69_dilations_0 = const()[name = tensor<string, []>("input_69_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_69_groups_0 = const()[name = tensor<string, []>("input_69_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_6_m_1_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_6_m_1_cv1_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(491328)))];
            tensor<fp16, [64]> model_6_m_1_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_6_m_1_cv1_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(565120)))];
            tensor<fp16, [1, 64, 40, 40]> input_69_cast_fp16 = conv(bias = model_6_m_1_cv1_conv_bias_to_fp16, dilations = input_69_dilations_0, groups = input_69_groups_0, pad = input_69_pad_0, pad_type = input_69_pad_type_0, strides = input_69_strides_0, weight = model_6_m_1_cv1_conv_weight_to_fp16, x = input_67_cast_fp16)[name = tensor<string, []>("input_69_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> input_71_cast_fp16 = silu(x = input_69_cast_fp16)[name = tensor<string, []>("input_71_cast_fp16")];
            tensor<string, []> input_73_pad_type_0 = const()[name = tensor<string, []>("input_73_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_73_pad_0 = const()[name = tensor<string, []>("input_73_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_73_strides_0 = const()[name = tensor<string, []>("input_73_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_73_dilations_0 = const()[name = tensor<string, []>("input_73_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_73_groups_0 = const()[name = tensor<string, []>("input_73_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_6_m_1_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_6_m_1_cv2_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(565312)))];
            tensor<fp16, [64]> model_6_m_1_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_6_m_1_cv2_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(639104)))];
            tensor<fp16, [1, 64, 40, 40]> input_73_cast_fp16 = conv(bias = model_6_m_1_cv2_conv_bias_to_fp16, dilations = input_73_dilations_0, groups = input_73_groups_0, pad = input_73_pad_0, pad_type = input_73_pad_type_0, strides = input_73_strides_0, weight = model_6_m_1_cv2_conv_weight_to_fp16, x = input_71_cast_fp16)[name = tensor<string, []>("input_73_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> var_287_cast_fp16 = silu(x = input_73_cast_fp16)[name = tensor<string, []>("op_287_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> var_288_cast_fp16 = add(x = input_67_cast_fp16, y = var_287_cast_fp16)[name = tensor<string, []>("op_288_cast_fp16")];
            tensor<bool, []> input_75_interleave_0 = const()[name = tensor<string, []>("input_75_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 256, 40, 40]> input_75_cast_fp16 = concat(axis = var_227, interleave = input_75_interleave_0, values = (var_244_cast_fp16_0, var_244_cast_fp16_1, input_67_cast_fp16, var_288_cast_fp16))[name = tensor<string, []>("input_75_cast_fp16")];
            tensor<string, []> input_77_pad_type_0 = const()[name = tensor<string, []>("input_77_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_77_strides_0 = const()[name = tensor<string, []>("input_77_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_77_pad_0 = const()[name = tensor<string, []>("input_77_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_77_dilations_0 = const()[name = tensor<string, []>("input_77_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_77_groups_0 = const()[name = tensor<string, []>("input_77_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [128, 256, 1, 1]> model_6_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_6_cv2_conv_weight_to_fp16"), val = tensor<fp16, [128, 256, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(639296)))];
            tensor<fp16, [128]> model_6_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_6_cv2_conv_bias_to_fp16"), val = tensor<fp16, [128]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(704896)))];
            tensor<fp16, [1, 128, 40, 40]> input_77_cast_fp16 = conv(bias = model_6_cv2_conv_bias_to_fp16, dilations = input_77_dilations_0, groups = input_77_groups_0, pad = input_77_pad_0, pad_type = input_77_pad_type_0, strides = input_77_strides_0, weight = model_6_cv2_conv_weight_to_fp16, x = input_75_cast_fp16)[name = tensor<string, []>("input_77_cast_fp16")];
            tensor<fp16, [1, 128, 40, 40]> input_79_cast_fp16 = silu(x = input_77_cast_fp16)[name = tensor<string, []>("input_79_cast_fp16")];
            tensor<string, []> input_81_pad_type_0 = const()[name = tensor<string, []>("input_81_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_81_pad_0 = const()[name = tensor<string, []>("input_81_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_81_strides_0 = const()[name = tensor<string, []>("input_81_strides_0"), val = tensor<int32, [2]>([2, 2])];
            tensor<int32, [2]> input_81_dilations_0 = const()[name = tensor<string, []>("input_81_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_81_groups_0 = const()[name = tensor<string, []>("input_81_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [256, 128, 3, 3]> model_7_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_7_conv_weight_to_fp16"), val = tensor<fp16, [256, 128, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(705216)))];
            tensor<fp16, [256]> model_7_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_7_conv_bias_to_fp16"), val = tensor<fp16, [256]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(1295104)))];
            tensor<fp16, [1, 256, 20, 20]> input_81_cast_fp16 = conv(bias = model_7_conv_bias_to_fp16, dilations = input_81_dilations_0, groups = input_81_groups_0, pad = input_81_pad_0, pad_type = input_81_pad_type_0, strides = input_81_strides_0, weight = model_7_conv_weight_to_fp16, x = input_79_cast_fp16)[name = tensor<string, []>("input_81_cast_fp16")];
            tensor<fp16, [1, 256, 20, 20]> input_83_cast_fp16 = silu(x = input_81_cast_fp16)[name = tensor<string, []>("input_83_cast_fp16")];
            tensor<int32, []> var_318 = const()[name = tensor<string, []>("op_318"), val = tensor<int32, []>(1)];
            tensor<string, []> input_85_pad_type_0 = const()[name = tensor<string, []>("input_85_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_85_strides_0 = const()[name = tensor<string, []>("input_85_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_85_pad_0 = const()[name = tensor<string, []>("input_85_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_85_dilations_0 = const()[name = tensor<string, []>("input_85_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_85_groups_0 = const()[name = tensor<string, []>("input_85_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [256, 256, 1, 1]> model_8_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_8_cv1_conv_weight_to_fp16"), val = tensor<fp16, [256, 256, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(1295680)))];
            tensor<fp16, [256]> model_8_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_8_cv1_conv_bias_to_fp16"), val = tensor<fp16, [256]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(1426816)))];
            tensor<fp16, [1, 256, 20, 20]> input_85_cast_fp16 = conv(bias = model_8_cv1_conv_bias_to_fp16, dilations = input_85_dilations_0, groups = input_85_groups_0, pad = input_85_pad_0, pad_type = input_85_pad_type_0, strides = input_85_strides_0, weight = model_8_cv1_conv_weight_to_fp16, x = input_83_cast_fp16)[name = tensor<string, []>("input_85_cast_fp16")];
            tensor<fp16, [1, 256, 20, 20]> var_331_cast_fp16 = silu(x = input_85_cast_fp16)[name = tensor<string, []>("op_331_cast_fp16")];
            tensor<int32, [2]> var_332 = const()[name = tensor<string, []>("op_332"), val = tensor<int32, [2]>([128, 128])];
            tensor<int32, []> var_333_axis_0 = const()[name = tensor<string, []>("op_333_axis_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [1, 128, 20, 20]> var_333_cast_fp16_0, tensor<fp16, [1, 128, 20, 20]> var_333_cast_fp16_1 = split(axis = var_333_axis_0, split_sizes = var_332, x = var_331_cast_fp16)[name = tensor<string, []>("op_333_cast_fp16")];
            tensor<string, []> input_89_pad_type_0 = const()[name = tensor<string, []>("input_89_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_89_pad_0 = const()[name = tensor<string, []>("input_89_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_89_strides_0 = const()[name = tensor<string, []>("input_89_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_89_dilations_0 = const()[name = tensor<string, []>("input_89_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_89_groups_0 = const()[name = tensor<string, []>("input_89_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [128, 128, 3, 3]> model_8_m_0_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_8_m_0_cv1_conv_weight_to_fp16"), val = tensor<fp16, [128, 128, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(1427392)))];
            tensor<fp16, [128]> model_8_m_0_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_8_m_0_cv1_conv_bias_to_fp16"), val = tensor<fp16, [128]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(1722368)))];
            tensor<fp16, [1, 128, 20, 20]> input_89_cast_fp16 = conv(bias = model_8_m_0_cv1_conv_bias_to_fp16, dilations = input_89_dilations_0, groups = input_89_groups_0, pad = input_89_pad_0, pad_type = input_89_pad_type_0, strides = input_89_strides_0, weight = model_8_m_0_cv1_conv_weight_to_fp16, x = var_333_cast_fp16_1)[name = tensor<string, []>("input_89_cast_fp16")];
            tensor<fp16, [1, 128, 20, 20]> input_91_cast_fp16 = silu(x = input_89_cast_fp16)[name = tensor<string, []>("input_91_cast_fp16")];
            tensor<string, []> input_93_pad_type_0 = const()[name = tensor<string, []>("input_93_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_93_pad_0 = const()[name = tensor<string, []>("input_93_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_93_strides_0 = const()[name = tensor<string, []>("input_93_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_93_dilations_0 = const()[name = tensor<string, []>("input_93_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_93_groups_0 = const()[name = tensor<string, []>("input_93_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [128, 128, 3, 3]> model_8_m_0_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_8_m_0_cv2_conv_weight_to_fp16"), val = tensor<fp16, [128, 128, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(1722688)))];
            tensor<fp16, [128]> model_8_m_0_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_8_m_0_cv2_conv_bias_to_fp16"), val = tensor<fp16, [128]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2017664)))];
            tensor<fp16, [1, 128, 20, 20]> input_93_cast_fp16 = conv(bias = model_8_m_0_cv2_conv_bias_to_fp16, dilations = input_93_dilations_0, groups = input_93_groups_0, pad = input_93_pad_0, pad_type = input_93_pad_type_0, strides = input_93_strides_0, weight = model_8_m_0_cv2_conv_weight_to_fp16, x = input_91_cast_fp16)[name = tensor<string, []>("input_93_cast_fp16")];
            tensor<fp16, [1, 128, 20, 20]> var_355_cast_fp16 = silu(x = input_93_cast_fp16)[name = tensor<string, []>("op_355_cast_fp16")];
            tensor<fp16, [1, 128, 20, 20]> var_356_cast_fp16 = add(x = var_333_cast_fp16_1, y = var_355_cast_fp16)[name = tensor<string, []>("op_356_cast_fp16")];
            tensor<bool, []> input_95_interleave_0 = const()[name = tensor<string, []>("input_95_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 384, 20, 20]> input_95_cast_fp16 = concat(axis = var_318, interleave = input_95_interleave_0, values = (var_333_cast_fp16_0, var_333_cast_fp16_1, var_356_cast_fp16))[name = tensor<string, []>("input_95_cast_fp16")];
            tensor<string, []> input_97_pad_type_0 = const()[name = tensor<string, []>("input_97_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_97_strides_0 = const()[name = tensor<string, []>("input_97_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_97_pad_0 = const()[name = tensor<string, []>("input_97_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_97_dilations_0 = const()[name = tensor<string, []>("input_97_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_97_groups_0 = const()[name = tensor<string, []>("input_97_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [256, 384, 1, 1]> model_8_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_8_cv2_conv_weight_to_fp16"), val = tensor<fp16, [256, 384, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2017984)))];
            tensor<fp16, [256]> model_8_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_8_cv2_conv_bias_to_fp16"), val = tensor<fp16, [256]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2214656)))];
            tensor<fp16, [1, 256, 20, 20]> input_97_cast_fp16 = conv(bias = model_8_cv2_conv_bias_to_fp16, dilations = input_97_dilations_0, groups = input_97_groups_0, pad = input_97_pad_0, pad_type = input_97_pad_type_0, strides = input_97_strides_0, weight = model_8_cv2_conv_weight_to_fp16, x = input_95_cast_fp16)[name = tensor<string, []>("input_97_cast_fp16")];
            tensor<fp16, [1, 256, 20, 20]> input_99_cast_fp16 = silu(x = input_97_cast_fp16)[name = tensor<string, []>("input_99_cast_fp16")];
            tensor<int32, []> var_373 = const()[name = tensor<string, []>("op_373"), val = tensor<int32, []>(1)];
            tensor<string, []> input_101_pad_type_0 = const()[name = tensor<string, []>("input_101_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_101_strides_0 = const()[name = tensor<string, []>("input_101_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_101_pad_0 = const()[name = tensor<string, []>("input_101_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_101_dilations_0 = const()[name = tensor<string, []>("input_101_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_101_groups_0 = const()[name = tensor<string, []>("input_101_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [128, 256, 1, 1]> model_9_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_9_cv1_conv_weight_to_fp16"), val = tensor<fp16, [128, 256, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2215232)))];
            tensor<fp16, [128]> model_9_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_9_cv1_conv_bias_to_fp16"), val = tensor<fp16, [128]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2280832)))];
            tensor<fp16, [1, 128, 20, 20]> input_101_cast_fp16 = conv(bias = model_9_cv1_conv_bias_to_fp16, dilations = input_101_dilations_0, groups = input_101_groups_0, pad = input_101_pad_0, pad_type = input_101_pad_type_0, strides = input_101_strides_0, weight = model_9_cv1_conv_weight_to_fp16, x = input_99_cast_fp16)[name = tensor<string, []>("input_101_cast_fp16")];
            tensor<fp16, [1, 128, 20, 20]> input_103_cast_fp16 = silu(x = input_101_cast_fp16)[name = tensor<string, []>("input_103_cast_fp16")];
            tensor<int32, [2]> var_385 = const()[name = tensor<string, []>("op_385"), val = tensor<int32, [2]>([5, 5])];
            tensor<int32, [2]> var_386 = const()[name = tensor<string, []>("op_386"), val = tensor<int32, [2]>([1, 1])];
            tensor<string, []> input_105_pad_type_0 = const()[name = tensor<string, []>("input_105_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_105_pad_0 = const()[name = tensor<string, []>("input_105_pad_0"), val = tensor<int32, [4]>([2, 2, 2, 2])];
            tensor<bool, []> input_105_ceil_mode_0 = const()[name = tensor<string, []>("input_105_ceil_mode_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 128, 20, 20]> input_105_cast_fp16 = max_pool(ceil_mode = input_105_ceil_mode_0, kernel_sizes = var_385, pad = input_105_pad_0, pad_type = input_105_pad_type_0, strides = var_386, x = input_103_cast_fp16)[name = tensor<string, []>("input_105_cast_fp16")];
            tensor<int32, [2]> var_390 = const()[name = tensor<string, []>("op_390"), val = tensor<int32, [2]>([5, 5])];
            tensor<int32, [2]> var_391 = const()[name = tensor<string, []>("op_391"), val = tensor<int32, [2]>([1, 1])];
            tensor<string, []> input_107_pad_type_0 = const()[name = tensor<string, []>("input_107_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_107_pad_0 = const()[name = tensor<string, []>("input_107_pad_0"), val = tensor<int32, [4]>([2, 2, 2, 2])];
            tensor<bool, []> input_107_ceil_mode_0 = const()[name = tensor<string, []>("input_107_ceil_mode_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 128, 20, 20]> input_107_cast_fp16 = max_pool(ceil_mode = input_107_ceil_mode_0, kernel_sizes = var_390, pad = input_107_pad_0, pad_type = input_107_pad_type_0, strides = var_391, x = input_105_cast_fp16)[name = tensor<string, []>("input_107_cast_fp16")];
            tensor<int32, [2]> var_395 = const()[name = tensor<string, []>("op_395"), val = tensor<int32, [2]>([5, 5])];
            tensor<int32, [2]> var_396 = const()[name = tensor<string, []>("op_396"), val = tensor<int32, [2]>([1, 1])];
            tensor<string, []> var_399_pad_type_0 = const()[name = tensor<string, []>("op_399_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> var_399_pad_0 = const()[name = tensor<string, []>("op_399_pad_0"), val = tensor<int32, [4]>([2, 2, 2, 2])];
            tensor<bool, []> var_399_ceil_mode_0 = const()[name = tensor<string, []>("op_399_ceil_mode_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 128, 20, 20]> var_399_cast_fp16 = max_pool(ceil_mode = var_399_ceil_mode_0, kernel_sizes = var_395, pad = var_399_pad_0, pad_type = var_399_pad_type_0, strides = var_396, x = input_107_cast_fp16)[name = tensor<string, []>("op_399_cast_fp16")];
            tensor<bool, []> input_109_interleave_0 = const()[name = tensor<string, []>("input_109_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 512, 20, 20]> input_109_cast_fp16 = concat(axis = var_373, interleave = input_109_interleave_0, values = (input_103_cast_fp16, input_105_cast_fp16, input_107_cast_fp16, var_399_cast_fp16))[name = tensor<string, []>("input_109_cast_fp16")];
            tensor<string, []> input_111_pad_type_0 = const()[name = tensor<string, []>("input_111_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_111_strides_0 = const()[name = tensor<string, []>("input_111_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_111_pad_0 = const()[name = tensor<string, []>("input_111_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_111_dilations_0 = const()[name = tensor<string, []>("input_111_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_111_groups_0 = const()[name = tensor<string, []>("input_111_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [256, 512, 1, 1]> model_9_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_9_cv2_conv_weight_to_fp16"), val = tensor<fp16, [256, 512, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2281152)))];
            tensor<fp16, [256]> model_9_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_9_cv2_conv_bias_to_fp16"), val = tensor<fp16, [256]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2543360)))];
            tensor<fp16, [1, 256, 20, 20]> input_111_cast_fp16 = conv(bias = model_9_cv2_conv_bias_to_fp16, dilations = input_111_dilations_0, groups = input_111_groups_0, pad = input_111_pad_0, pad_type = input_111_pad_type_0, strides = input_111_strides_0, weight = model_9_cv2_conv_weight_to_fp16, x = input_109_cast_fp16)[name = tensor<string, []>("input_111_cast_fp16")];
            tensor<fp16, [1, 256, 20, 20]> input_113_cast_fp16 = silu(x = input_111_cast_fp16)[name = tensor<string, []>("input_113_cast_fp16")];
            tensor<fp32, []> var_414_scale_factor_height_0 = const()[name = tensor<string, []>("op_414_scale_factor_height_0"), val = tensor<fp32, []>(0x1p+1)];
            tensor<fp32, []> var_414_scale_factor_width_0 = const()[name = tensor<string, []>("op_414_scale_factor_width_0"), val = tensor<fp32, []>(0x1p+1)];
            tensor<fp16, [1, 256, 40, 40]> var_414_cast_fp16 = upsample_nearest_neighbor(scale_factor_height = var_414_scale_factor_height_0, scale_factor_width = var_414_scale_factor_width_0, x = input_113_cast_fp16)[name = tensor<string, []>("op_414_cast_fp16")];
            tensor<int32, []> var_415 = const()[name = tensor<string, []>("op_415"), val = tensor<int32, []>(1)];
            tensor<bool, []> input_115_interleave_0 = const()[name = tensor<string, []>("input_115_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 384, 40, 40]> input_115_cast_fp16 = concat(axis = var_415, interleave = input_115_interleave_0, values = (var_414_cast_fp16, input_79_cast_fp16))[name = tensor<string, []>("input_115_cast_fp16")];
            tensor<int32, []> var_422 = const()[name = tensor<string, []>("op_422"), val = tensor<int32, []>(1)];
            tensor<string, []> input_117_pad_type_0 = const()[name = tensor<string, []>("input_117_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_117_strides_0 = const()[name = tensor<string, []>("input_117_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_117_pad_0 = const()[name = tensor<string, []>("input_117_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_117_dilations_0 = const()[name = tensor<string, []>("input_117_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_117_groups_0 = const()[name = tensor<string, []>("input_117_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [128, 384, 1, 1]> model_12_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_12_cv1_conv_weight_to_fp16"), val = tensor<fp16, [128, 384, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2543936)))];
            tensor<fp16, [128]> model_12_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_12_cv1_conv_bias_to_fp16"), val = tensor<fp16, [128]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2642304)))];
            tensor<fp16, [1, 128, 40, 40]> input_117_cast_fp16 = conv(bias = model_12_cv1_conv_bias_to_fp16, dilations = input_117_dilations_0, groups = input_117_groups_0, pad = input_117_pad_0, pad_type = input_117_pad_type_0, strides = input_117_strides_0, weight = model_12_cv1_conv_weight_to_fp16, x = input_115_cast_fp16)[name = tensor<string, []>("input_117_cast_fp16")];
            tensor<fp16, [1, 128, 40, 40]> var_435_cast_fp16 = silu(x = input_117_cast_fp16)[name = tensor<string, []>("op_435_cast_fp16")];
            tensor<int32, [2]> var_436 = const()[name = tensor<string, []>("op_436"), val = tensor<int32, [2]>([64, 64])];
            tensor<int32, []> var_437_axis_0 = const()[name = tensor<string, []>("op_437_axis_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [1, 64, 40, 40]> var_437_cast_fp16_0, tensor<fp16, [1, 64, 40, 40]> var_437_cast_fp16_1 = split(axis = var_437_axis_0, split_sizes = var_436, x = var_435_cast_fp16)[name = tensor<string, []>("op_437_cast_fp16")];
            tensor<string, []> input_121_pad_type_0 = const()[name = tensor<string, []>("input_121_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_121_pad_0 = const()[name = tensor<string, []>("input_121_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_121_strides_0 = const()[name = tensor<string, []>("input_121_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_121_dilations_0 = const()[name = tensor<string, []>("input_121_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_121_groups_0 = const()[name = tensor<string, []>("input_121_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_12_m_0_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_12_m_0_cv1_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2642624)))];
            tensor<fp16, [64]> model_12_m_0_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_12_m_0_cv1_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2716416)))];
            tensor<fp16, [1, 64, 40, 40]> input_121_cast_fp16 = conv(bias = model_12_m_0_cv1_conv_bias_to_fp16, dilations = input_121_dilations_0, groups = input_121_groups_0, pad = input_121_pad_0, pad_type = input_121_pad_type_0, strides = input_121_strides_0, weight = model_12_m_0_cv1_conv_weight_to_fp16, x = var_437_cast_fp16_1)[name = tensor<string, []>("input_121_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> input_123_cast_fp16 = silu(x = input_121_cast_fp16)[name = tensor<string, []>("input_123_cast_fp16")];
            tensor<string, []> input_125_pad_type_0 = const()[name = tensor<string, []>("input_125_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_125_pad_0 = const()[name = tensor<string, []>("input_125_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_125_strides_0 = const()[name = tensor<string, []>("input_125_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_125_dilations_0 = const()[name = tensor<string, []>("input_125_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_125_groups_0 = const()[name = tensor<string, []>("input_125_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_12_m_0_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_12_m_0_cv2_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2716608)))];
            tensor<fp16, [64]> model_12_m_0_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_12_m_0_cv2_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2790400)))];
            tensor<fp16, [1, 64, 40, 40]> input_125_cast_fp16 = conv(bias = model_12_m_0_cv2_conv_bias_to_fp16, dilations = input_125_dilations_0, groups = input_125_groups_0, pad = input_125_pad_0, pad_type = input_125_pad_type_0, strides = input_125_strides_0, weight = model_12_m_0_cv2_conv_weight_to_fp16, x = input_123_cast_fp16)[name = tensor<string, []>("input_125_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> var_459_cast_fp16 = silu(x = input_125_cast_fp16)[name = tensor<string, []>("op_459_cast_fp16")];
            tensor<bool, []> input_127_interleave_0 = const()[name = tensor<string, []>("input_127_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 192, 40, 40]> input_127_cast_fp16 = concat(axis = var_422, interleave = input_127_interleave_0, values = (var_437_cast_fp16_0, var_437_cast_fp16_1, var_459_cast_fp16))[name = tensor<string, []>("input_127_cast_fp16")];
            tensor<string, []> input_129_pad_type_0 = const()[name = tensor<string, []>("input_129_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_129_strides_0 = const()[name = tensor<string, []>("input_129_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_129_pad_0 = const()[name = tensor<string, []>("input_129_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_129_dilations_0 = const()[name = tensor<string, []>("input_129_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_129_groups_0 = const()[name = tensor<string, []>("input_129_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [128, 192, 1, 1]> model_12_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_12_cv2_conv_weight_to_fp16"), val = tensor<fp16, [128, 192, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2790592)))];
            tensor<fp16, [128]> model_12_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_12_cv2_conv_bias_to_fp16"), val = tensor<fp16, [128]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2839808)))];
            tensor<fp16, [1, 128, 40, 40]> input_129_cast_fp16 = conv(bias = model_12_cv2_conv_bias_to_fp16, dilations = input_129_dilations_0, groups = input_129_groups_0, pad = input_129_pad_0, pad_type = input_129_pad_type_0, strides = input_129_strides_0, weight = model_12_cv2_conv_weight_to_fp16, x = input_127_cast_fp16)[name = tensor<string, []>("input_129_cast_fp16")];
            tensor<fp16, [1, 128, 40, 40]> input_131_cast_fp16 = silu(x = input_129_cast_fp16)[name = tensor<string, []>("input_131_cast_fp16")];
            tensor<fp32, []> var_474_scale_factor_height_0 = const()[name = tensor<string, []>("op_474_scale_factor_height_0"), val = tensor<fp32, []>(0x1p+1)];
            tensor<fp32, []> var_474_scale_factor_width_0 = const()[name = tensor<string, []>("op_474_scale_factor_width_0"), val = tensor<fp32, []>(0x1p+1)];
            tensor<fp16, [1, 128, 80, 80]> var_474_cast_fp16 = upsample_nearest_neighbor(scale_factor_height = var_474_scale_factor_height_0, scale_factor_width = var_474_scale_factor_width_0, x = input_131_cast_fp16)[name = tensor<string, []>("op_474_cast_fp16")];
            tensor<int32, []> var_475 = const()[name = tensor<string, []>("op_475"), val = tensor<int32, []>(1)];
            tensor<bool, []> input_133_interleave_0 = const()[name = tensor<string, []>("input_133_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 192, 80, 80]> input_133_cast_fp16 = concat(axis = var_475, interleave = input_133_interleave_0, values = (var_474_cast_fp16, input_51_cast_fp16))[name = tensor<string, []>("input_133_cast_fp16")];
            tensor<int32, []> var_482 = const()[name = tensor<string, []>("op_482"), val = tensor<int32, []>(1)];
            tensor<string, []> input_135_pad_type_0 = const()[name = tensor<string, []>("input_135_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_135_strides_0 = const()[name = tensor<string, []>("input_135_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_135_pad_0 = const()[name = tensor<string, []>("input_135_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_135_dilations_0 = const()[name = tensor<string, []>("input_135_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_135_groups_0 = const()[name = tensor<string, []>("input_135_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 192, 1, 1]> model_15_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_15_cv1_conv_weight_to_fp16"), val = tensor<fp16, [64, 192, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2840128)))];
            tensor<fp16, [64]> model_15_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_15_cv1_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2864768)))];
            tensor<fp16, [1, 64, 80, 80]> input_135_cast_fp16 = conv(bias = model_15_cv1_conv_bias_to_fp16, dilations = input_135_dilations_0, groups = input_135_groups_0, pad = input_135_pad_0, pad_type = input_135_pad_type_0, strides = input_135_strides_0, weight = model_15_cv1_conv_weight_to_fp16, x = input_133_cast_fp16)[name = tensor<string, []>("input_135_cast_fp16")];
            tensor<fp16, [1, 64, 80, 80]> var_495_cast_fp16 = silu(x = input_135_cast_fp16)[name = tensor<string, []>("op_495_cast_fp16")];
            tensor<int32, [2]> var_496 = const()[name = tensor<string, []>("op_496"), val = tensor<int32, [2]>([32, 32])];
            tensor<int32, []> var_497_axis_0 = const()[name = tensor<string, []>("op_497_axis_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [1, 32, 80, 80]> var_497_cast_fp16_0, tensor<fp16, [1, 32, 80, 80]> var_497_cast_fp16_1 = split(axis = var_497_axis_0, split_sizes = var_496, x = var_495_cast_fp16)[name = tensor<string, []>("op_497_cast_fp16")];
            tensor<string, []> input_139_pad_type_0 = const()[name = tensor<string, []>("input_139_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_139_pad_0 = const()[name = tensor<string, []>("input_139_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_139_strides_0 = const()[name = tensor<string, []>("input_139_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_139_dilations_0 = const()[name = tensor<string, []>("input_139_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_139_groups_0 = const()[name = tensor<string, []>("input_139_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [32, 32, 3, 3]> model_15_m_0_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_15_m_0_cv1_conv_weight_to_fp16"), val = tensor<fp16, [32, 32, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2864960)))];
            tensor<fp16, [32]> model_15_m_0_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_15_m_0_cv1_conv_bias_to_fp16"), val = tensor<fp16, [32]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2883456)))];
            tensor<fp16, [1, 32, 80, 80]> input_139_cast_fp16 = conv(bias = model_15_m_0_cv1_conv_bias_to_fp16, dilations = input_139_dilations_0, groups = input_139_groups_0, pad = input_139_pad_0, pad_type = input_139_pad_type_0, strides = input_139_strides_0, weight = model_15_m_0_cv1_conv_weight_to_fp16, x = var_497_cast_fp16_1)[name = tensor<string, []>("input_139_cast_fp16")];
            tensor<fp16, [1, 32, 80, 80]> input_141_cast_fp16 = silu(x = input_139_cast_fp16)[name = tensor<string, []>("input_141_cast_fp16")];
            tensor<string, []> input_143_pad_type_0 = const()[name = tensor<string, []>("input_143_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_143_pad_0 = const()[name = tensor<string, []>("input_143_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_143_strides_0 = const()[name = tensor<string, []>("input_143_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_143_dilations_0 = const()[name = tensor<string, []>("input_143_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_143_groups_0 = const()[name = tensor<string, []>("input_143_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [32, 32, 3, 3]> model_15_m_0_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_15_m_0_cv2_conv_weight_to_fp16"), val = tensor<fp16, [32, 32, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2883584)))];
            tensor<fp16, [32]> model_15_m_0_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_15_m_0_cv2_conv_bias_to_fp16"), val = tensor<fp16, [32]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2902080)))];
            tensor<fp16, [1, 32, 80, 80]> input_143_cast_fp16 = conv(bias = model_15_m_0_cv2_conv_bias_to_fp16, dilations = input_143_dilations_0, groups = input_143_groups_0, pad = input_143_pad_0, pad_type = input_143_pad_type_0, strides = input_143_strides_0, weight = model_15_m_0_cv2_conv_weight_to_fp16, x = input_141_cast_fp16)[name = tensor<string, []>("input_143_cast_fp16")];
            tensor<fp16, [1, 32, 80, 80]> var_519_cast_fp16 = silu(x = input_143_cast_fp16)[name = tensor<string, []>("op_519_cast_fp16")];
            tensor<bool, []> input_145_interleave_0 = const()[name = tensor<string, []>("input_145_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 96, 80, 80]> input_145_cast_fp16 = concat(axis = var_482, interleave = input_145_interleave_0, values = (var_497_cast_fp16_0, var_497_cast_fp16_1, var_519_cast_fp16))[name = tensor<string, []>("input_145_cast_fp16")];
            tensor<string, []> input_147_pad_type_0 = const()[name = tensor<string, []>("input_147_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_147_strides_0 = const()[name = tensor<string, []>("input_147_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_147_pad_0 = const()[name = tensor<string, []>("input_147_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_147_dilations_0 = const()[name = tensor<string, []>("input_147_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_147_groups_0 = const()[name = tensor<string, []>("input_147_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 96, 1, 1]> model_15_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_15_cv2_conv_weight_to_fp16"), val = tensor<fp16, [64, 96, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2902208)))];
            tensor<fp16, [64]> model_15_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_15_cv2_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2914560)))];
            tensor<fp16, [1, 64, 80, 80]> input_147_cast_fp16 = conv(bias = model_15_cv2_conv_bias_to_fp16, dilations = input_147_dilations_0, groups = input_147_groups_0, pad = input_147_pad_0, pad_type = input_147_pad_type_0, strides = input_147_strides_0, weight = model_15_cv2_conv_weight_to_fp16, x = input_145_cast_fp16)[name = tensor<string, []>("input_147_cast_fp16")];
            tensor<fp16, [1, 64, 80, 80]> input_149_cast_fp16 = silu(x = input_147_cast_fp16)[name = tensor<string, []>("input_149_cast_fp16")];
            tensor<string, []> input_151_pad_type_0 = const()[name = tensor<string, []>("input_151_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_151_pad_0 = const()[name = tensor<string, []>("input_151_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_151_strides_0 = const()[name = tensor<string, []>("input_151_strides_0"), val = tensor<int32, [2]>([2, 2])];
            tensor<int32, [2]> input_151_dilations_0 = const()[name = tensor<string, []>("input_151_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_151_groups_0 = const()[name = tensor<string, []>("input_151_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_16_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_16_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2914752)))];
            tensor<fp16, [64]> model_16_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_16_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2988544)))];
            tensor<fp16, [1, 64, 40, 40]> input_151_cast_fp16 = conv(bias = model_16_conv_bias_to_fp16, dilations = input_151_dilations_0, groups = input_151_groups_0, pad = input_151_pad_0, pad_type = input_151_pad_type_0, strides = input_151_strides_0, weight = model_16_conv_weight_to_fp16, x = input_149_cast_fp16)[name = tensor<string, []>("input_151_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> var_544_cast_fp16 = silu(x = input_151_cast_fp16)[name = tensor<string, []>("op_544_cast_fp16")];
            tensor<int32, []> var_545 = const()[name = tensor<string, []>("op_545"), val = tensor<int32, []>(1)];
            tensor<bool, []> input_153_interleave_0 = const()[name = tensor<string, []>("input_153_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 192, 40, 40]> input_153_cast_fp16 = concat(axis = var_545, interleave = input_153_interleave_0, values = (var_544_cast_fp16, input_131_cast_fp16))[name = tensor<string, []>("input_153_cast_fp16")];
            tensor<int32, []> var_552 = const()[name = tensor<string, []>("op_552"), val = tensor<int32, []>(1)];
            tensor<string, []> input_155_pad_type_0 = const()[name = tensor<string, []>("input_155_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_155_strides_0 = const()[name = tensor<string, []>("input_155_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_155_pad_0 = const()[name = tensor<string, []>("input_155_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_155_dilations_0 = const()[name = tensor<string, []>("input_155_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_155_groups_0 = const()[name = tensor<string, []>("input_155_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [128, 192, 1, 1]> model_18_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_18_cv1_conv_weight_to_fp16"), val = tensor<fp16, [128, 192, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(2988736)))];
            tensor<fp16, [128]> model_18_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_18_cv1_conv_bias_to_fp16"), val = tensor<fp16, [128]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(3037952)))];
            tensor<fp16, [1, 128, 40, 40]> input_155_cast_fp16 = conv(bias = model_18_cv1_conv_bias_to_fp16, dilations = input_155_dilations_0, groups = input_155_groups_0, pad = input_155_pad_0, pad_type = input_155_pad_type_0, strides = input_155_strides_0, weight = model_18_cv1_conv_weight_to_fp16, x = input_153_cast_fp16)[name = tensor<string, []>("input_155_cast_fp16")];
            tensor<fp16, [1, 128, 40, 40]> var_565_cast_fp16 = silu(x = input_155_cast_fp16)[name = tensor<string, []>("op_565_cast_fp16")];
            tensor<int32, [2]> var_566 = const()[name = tensor<string, []>("op_566"), val = tensor<int32, [2]>([64, 64])];
            tensor<int32, []> var_567_axis_0 = const()[name = tensor<string, []>("op_567_axis_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [1, 64, 40, 40]> var_567_cast_fp16_0, tensor<fp16, [1, 64, 40, 40]> var_567_cast_fp16_1 = split(axis = var_567_axis_0, split_sizes = var_566, x = var_565_cast_fp16)[name = tensor<string, []>("op_567_cast_fp16")];
            tensor<string, []> input_159_pad_type_0 = const()[name = tensor<string, []>("input_159_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_159_pad_0 = const()[name = tensor<string, []>("input_159_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_159_strides_0 = const()[name = tensor<string, []>("input_159_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_159_dilations_0 = const()[name = tensor<string, []>("input_159_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_159_groups_0 = const()[name = tensor<string, []>("input_159_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_18_m_0_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_18_m_0_cv1_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(3038272)))];
            tensor<fp16, [64]> model_18_m_0_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_18_m_0_cv1_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(3112064)))];
            tensor<fp16, [1, 64, 40, 40]> input_159_cast_fp16 = conv(bias = model_18_m_0_cv1_conv_bias_to_fp16, dilations = input_159_dilations_0, groups = input_159_groups_0, pad = input_159_pad_0, pad_type = input_159_pad_type_0, strides = input_159_strides_0, weight = model_18_m_0_cv1_conv_weight_to_fp16, x = var_567_cast_fp16_1)[name = tensor<string, []>("input_159_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> input_161_cast_fp16 = silu(x = input_159_cast_fp16)[name = tensor<string, []>("input_161_cast_fp16")];
            tensor<string, []> input_163_pad_type_0 = const()[name = tensor<string, []>("input_163_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_163_pad_0 = const()[name = tensor<string, []>("input_163_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_163_strides_0 = const()[name = tensor<string, []>("input_163_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_163_dilations_0 = const()[name = tensor<string, []>("input_163_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_163_groups_0 = const()[name = tensor<string, []>("input_163_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_18_m_0_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_18_m_0_cv2_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(3112256)))];
            tensor<fp16, [64]> model_18_m_0_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_18_m_0_cv2_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(3186048)))];
            tensor<fp16, [1, 64, 40, 40]> input_163_cast_fp16 = conv(bias = model_18_m_0_cv2_conv_bias_to_fp16, dilations = input_163_dilations_0, groups = input_163_groups_0, pad = input_163_pad_0, pad_type = input_163_pad_type_0, strides = input_163_strides_0, weight = model_18_m_0_cv2_conv_weight_to_fp16, x = input_161_cast_fp16)[name = tensor<string, []>("input_163_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> var_589_cast_fp16 = silu(x = input_163_cast_fp16)[name = tensor<string, []>("op_589_cast_fp16")];
            tensor<bool, []> input_165_interleave_0 = const()[name = tensor<string, []>("input_165_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 192, 40, 40]> input_165_cast_fp16 = concat(axis = var_552, interleave = input_165_interleave_0, values = (var_567_cast_fp16_0, var_567_cast_fp16_1, var_589_cast_fp16))[name = tensor<string, []>("input_165_cast_fp16")];
            tensor<string, []> input_167_pad_type_0 = const()[name = tensor<string, []>("input_167_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_167_strides_0 = const()[name = tensor<string, []>("input_167_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_167_pad_0 = const()[name = tensor<string, []>("input_167_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_167_dilations_0 = const()[name = tensor<string, []>("input_167_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_167_groups_0 = const()[name = tensor<string, []>("input_167_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [128, 192, 1, 1]> model_18_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_18_cv2_conv_weight_to_fp16"), val = tensor<fp16, [128, 192, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(3186240)))];
            tensor<fp16, [128]> model_18_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_18_cv2_conv_bias_to_fp16"), val = tensor<fp16, [128]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(3235456)))];
            tensor<fp16, [1, 128, 40, 40]> input_167_cast_fp16 = conv(bias = model_18_cv2_conv_bias_to_fp16, dilations = input_167_dilations_0, groups = input_167_groups_0, pad = input_167_pad_0, pad_type = input_167_pad_type_0, strides = input_167_strides_0, weight = model_18_cv2_conv_weight_to_fp16, x = input_165_cast_fp16)[name = tensor<string, []>("input_167_cast_fp16")];
            tensor<fp16, [1, 128, 40, 40]> input_169_cast_fp16 = silu(x = input_167_cast_fp16)[name = tensor<string, []>("input_169_cast_fp16")];
            tensor<string, []> input_171_pad_type_0 = const()[name = tensor<string, []>("input_171_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_171_pad_0 = const()[name = tensor<string, []>("input_171_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_171_strides_0 = const()[name = tensor<string, []>("input_171_strides_0"), val = tensor<int32, [2]>([2, 2])];
            tensor<int32, [2]> input_171_dilations_0 = const()[name = tensor<string, []>("input_171_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_171_groups_0 = const()[name = tensor<string, []>("input_171_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [128, 128, 3, 3]> model_19_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_19_conv_weight_to_fp16"), val = tensor<fp16, [128, 128, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(3235776)))];
            tensor<fp16, [128]> model_19_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_19_conv_bias_to_fp16"), val = tensor<fp16, [128]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(3530752)))];
            tensor<fp16, [1, 128, 20, 20]> input_171_cast_fp16 = conv(bias = model_19_conv_bias_to_fp16, dilations = input_171_dilations_0, groups = input_171_groups_0, pad = input_171_pad_0, pad_type = input_171_pad_type_0, strides = input_171_strides_0, weight = model_19_conv_weight_to_fp16, x = input_169_cast_fp16)[name = tensor<string, []>("input_171_cast_fp16")];
            tensor<fp16, [1, 128, 20, 20]> var_614_cast_fp16 = silu(x = input_171_cast_fp16)[name = tensor<string, []>("op_614_cast_fp16")];
            tensor<int32, []> var_615 = const()[name = tensor<string, []>("op_615"), val = tensor<int32, []>(1)];
            tensor<bool, []> input_173_interleave_0 = const()[name = tensor<string, []>("input_173_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 384, 20, 20]> input_173_cast_fp16 = concat(axis = var_615, interleave = input_173_interleave_0, values = (var_614_cast_fp16, input_113_cast_fp16))[name = tensor<string, []>("input_173_cast_fp16")];
            tensor<int32, []> var_622 = const()[name = tensor<string, []>("op_622"), val = tensor<int32, []>(1)];
            tensor<string, []> input_175_pad_type_0 = const()[name = tensor<string, []>("input_175_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_175_strides_0 = const()[name = tensor<string, []>("input_175_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_175_pad_0 = const()[name = tensor<string, []>("input_175_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_175_dilations_0 = const()[name = tensor<string, []>("input_175_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_175_groups_0 = const()[name = tensor<string, []>("input_175_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [256, 384, 1, 1]> model_21_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_21_cv1_conv_weight_to_fp16"), val = tensor<fp16, [256, 384, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(3531072)))];
            tensor<fp16, [256]> model_21_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_21_cv1_conv_bias_to_fp16"), val = tensor<fp16, [256]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(3727744)))];
            tensor<fp16, [1, 256, 20, 20]> input_175_cast_fp16 = conv(bias = model_21_cv1_conv_bias_to_fp16, dilations = input_175_dilations_0, groups = input_175_groups_0, pad = input_175_pad_0, pad_type = input_175_pad_type_0, strides = input_175_strides_0, weight = model_21_cv1_conv_weight_to_fp16, x = input_173_cast_fp16)[name = tensor<string, []>("input_175_cast_fp16")];
            tensor<fp16, [1, 256, 20, 20]> var_635_cast_fp16 = silu(x = input_175_cast_fp16)[name = tensor<string, []>("op_635_cast_fp16")];
            tensor<int32, [2]> var_636 = const()[name = tensor<string, []>("op_636"), val = tensor<int32, [2]>([128, 128])];
            tensor<int32, []> var_637_axis_0 = const()[name = tensor<string, []>("op_637_axis_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [1, 128, 20, 20]> var_637_cast_fp16_0, tensor<fp16, [1, 128, 20, 20]> var_637_cast_fp16_1 = split(axis = var_637_axis_0, split_sizes = var_636, x = var_635_cast_fp16)[name = tensor<string, []>("op_637_cast_fp16")];
            tensor<string, []> input_179_pad_type_0 = const()[name = tensor<string, []>("input_179_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_179_pad_0 = const()[name = tensor<string, []>("input_179_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_179_strides_0 = const()[name = tensor<string, []>("input_179_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_179_dilations_0 = const()[name = tensor<string, []>("input_179_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_179_groups_0 = const()[name = tensor<string, []>("input_179_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [128, 128, 3, 3]> model_21_m_0_cv1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_21_m_0_cv1_conv_weight_to_fp16"), val = tensor<fp16, [128, 128, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(3728320)))];
            tensor<fp16, [128]> model_21_m_0_cv1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_21_m_0_cv1_conv_bias_to_fp16"), val = tensor<fp16, [128]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4023296)))];
            tensor<fp16, [1, 128, 20, 20]> input_179_cast_fp16 = conv(bias = model_21_m_0_cv1_conv_bias_to_fp16, dilations = input_179_dilations_0, groups = input_179_groups_0, pad = input_179_pad_0, pad_type = input_179_pad_type_0, strides = input_179_strides_0, weight = model_21_m_0_cv1_conv_weight_to_fp16, x = var_637_cast_fp16_1)[name = tensor<string, []>("input_179_cast_fp16")];
            tensor<fp16, [1, 128, 20, 20]> input_181_cast_fp16 = silu(x = input_179_cast_fp16)[name = tensor<string, []>("input_181_cast_fp16")];
            tensor<string, []> input_183_pad_type_0 = const()[name = tensor<string, []>("input_183_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_183_pad_0 = const()[name = tensor<string, []>("input_183_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_183_strides_0 = const()[name = tensor<string, []>("input_183_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_183_dilations_0 = const()[name = tensor<string, []>("input_183_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_183_groups_0 = const()[name = tensor<string, []>("input_183_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [128, 128, 3, 3]> model_21_m_0_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_21_m_0_cv2_conv_weight_to_fp16"), val = tensor<fp16, [128, 128, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4023616)))];
            tensor<fp16, [128]> model_21_m_0_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_21_m_0_cv2_conv_bias_to_fp16"), val = tensor<fp16, [128]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4318592)))];
            tensor<fp16, [1, 128, 20, 20]> input_183_cast_fp16 = conv(bias = model_21_m_0_cv2_conv_bias_to_fp16, dilations = input_183_dilations_0, groups = input_183_groups_0, pad = input_183_pad_0, pad_type = input_183_pad_type_0, strides = input_183_strides_0, weight = model_21_m_0_cv2_conv_weight_to_fp16, x = input_181_cast_fp16)[name = tensor<string, []>("input_183_cast_fp16")];
            tensor<fp16, [1, 128, 20, 20]> var_659_cast_fp16 = silu(x = input_183_cast_fp16)[name = tensor<string, []>("op_659_cast_fp16")];
            tensor<bool, []> input_185_interleave_0 = const()[name = tensor<string, []>("input_185_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 384, 20, 20]> input_185_cast_fp16 = concat(axis = var_622, interleave = input_185_interleave_0, values = (var_637_cast_fp16_0, var_637_cast_fp16_1, var_659_cast_fp16))[name = tensor<string, []>("input_185_cast_fp16")];
            tensor<string, []> input_187_pad_type_0 = const()[name = tensor<string, []>("input_187_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> input_187_strides_0 = const()[name = tensor<string, []>("input_187_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> input_187_pad_0 = const()[name = tensor<string, []>("input_187_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> input_187_dilations_0 = const()[name = tensor<string, []>("input_187_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_187_groups_0 = const()[name = tensor<string, []>("input_187_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [256, 384, 1, 1]> model_21_cv2_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_21_cv2_conv_weight_to_fp16"), val = tensor<fp16, [256, 384, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4318912)))];
            tensor<fp16, [256]> model_21_cv2_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_21_cv2_conv_bias_to_fp16"), val = tensor<fp16, [256]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4515584)))];
            tensor<fp16, [1, 256, 20, 20]> input_187_cast_fp16 = conv(bias = model_21_cv2_conv_bias_to_fp16, dilations = input_187_dilations_0, groups = input_187_groups_0, pad = input_187_pad_0, pad_type = input_187_pad_type_0, strides = input_187_strides_0, weight = model_21_cv2_conv_weight_to_fp16, x = input_185_cast_fp16)[name = tensor<string, []>("input_187_cast_fp16")];
            tensor<fp16, [1, 256, 20, 20]> input_221_cast_fp16 = silu(x = input_187_cast_fp16)[name = tensor<string, []>("input_221_cast_fp16")];
            tensor<int32, []> var_677 = const()[name = tensor<string, []>("op_677"), val = tensor<int32, []>(2)];
            tensor<int32, []> var_680 = const()[name = tensor<string, []>("op_680"), val = tensor<int32, []>(1)];
            tensor<string, []> input_189_pad_type_0 = const()[name = tensor<string, []>("input_189_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_189_pad_0 = const()[name = tensor<string, []>("input_189_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_189_strides_0 = const()[name = tensor<string, []>("input_189_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_189_dilations_0 = const()[name = tensor<string, []>("input_189_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_189_groups_0 = const()[name = tensor<string, []>("input_189_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_22_cv2_0_0_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_0_0_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4516160)))];
            tensor<fp16, [64]> model_22_cv2_0_0_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_0_0_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4589952)))];
            tensor<fp16, [1, 64, 80, 80]> input_189_cast_fp16 = conv(bias = model_22_cv2_0_0_conv_bias_to_fp16, dilations = input_189_dilations_0, groups = input_189_groups_0, pad = input_189_pad_0, pad_type = input_189_pad_type_0, strides = input_189_strides_0, weight = model_22_cv2_0_0_conv_weight_to_fp16, x = input_149_cast_fp16)[name = tensor<string, []>("input_189_cast_fp16")];
            tensor<fp16, [1, 64, 80, 80]> input_191_cast_fp16 = silu(x = input_189_cast_fp16)[name = tensor<string, []>("input_191_cast_fp16")];
            tensor<string, []> input_193_pad_type_0 = const()[name = tensor<string, []>("input_193_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_193_pad_0 = const()[name = tensor<string, []>("input_193_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_193_strides_0 = const()[name = tensor<string, []>("input_193_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_193_dilations_0 = const()[name = tensor<string, []>("input_193_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_193_groups_0 = const()[name = tensor<string, []>("input_193_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_22_cv2_0_1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_0_1_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4590144)))];
            tensor<fp16, [64]> model_22_cv2_0_1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_0_1_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4663936)))];
            tensor<fp16, [1, 64, 80, 80]> input_193_cast_fp16 = conv(bias = model_22_cv2_0_1_conv_bias_to_fp16, dilations = input_193_dilations_0, groups = input_193_groups_0, pad = input_193_pad_0, pad_type = input_193_pad_type_0, strides = input_193_strides_0, weight = model_22_cv2_0_1_conv_weight_to_fp16, x = input_191_cast_fp16)[name = tensor<string, []>("input_193_cast_fp16")];
            tensor<fp16, [1, 64, 80, 80]> input_195_cast_fp16 = silu(x = input_193_cast_fp16)[name = tensor<string, []>("input_195_cast_fp16")];
            tensor<string, []> var_724_pad_type_0 = const()[name = tensor<string, []>("op_724_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> var_724_strides_0 = const()[name = tensor<string, []>("op_724_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> var_724_pad_0 = const()[name = tensor<string, []>("op_724_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> var_724_dilations_0 = const()[name = tensor<string, []>("op_724_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> var_724_groups_0 = const()[name = tensor<string, []>("op_724_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 1, 1]> model_22_cv2_0_2_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_0_2_weight_to_fp16"), val = tensor<fp16, [64, 64, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4664128)))];
            tensor<fp16, [64]> model_22_cv2_0_2_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_0_2_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4672384)))];
            tensor<fp16, [1, 64, 80, 80]> var_724_cast_fp16 = conv(bias = model_22_cv2_0_2_bias_to_fp16, dilations = var_724_dilations_0, groups = var_724_groups_0, pad = var_724_pad_0, pad_type = var_724_pad_type_0, strides = var_724_strides_0, weight = model_22_cv2_0_2_weight_to_fp16, x = input_195_cast_fp16)[name = tensor<string, []>("op_724_cast_fp16")];
            tensor<string, []> input_197_pad_type_0 = const()[name = tensor<string, []>("input_197_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_197_pad_0 = const()[name = tensor<string, []>("input_197_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_197_strides_0 = const()[name = tensor<string, []>("input_197_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_197_dilations_0 = const()[name = tensor<string, []>("input_197_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_197_groups_0 = const()[name = tensor<string, []>("input_197_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_22_cv3_0_0_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_0_0_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4672576)))];
            tensor<fp16, [64]> model_22_cv3_0_0_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_0_0_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4746368)))];
            tensor<fp16, [1, 64, 80, 80]> input_197_cast_fp16 = conv(bias = model_22_cv3_0_0_conv_bias_to_fp16, dilations = input_197_dilations_0, groups = input_197_groups_0, pad = input_197_pad_0, pad_type = input_197_pad_type_0, strides = input_197_strides_0, weight = model_22_cv3_0_0_conv_weight_to_fp16, x = input_149_cast_fp16)[name = tensor<string, []>("input_197_cast_fp16")];
            tensor<fp16, [1, 64, 80, 80]> input_199_cast_fp16 = silu(x = input_197_cast_fp16)[name = tensor<string, []>("input_199_cast_fp16")];
            tensor<string, []> input_201_pad_type_0 = const()[name = tensor<string, []>("input_201_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_201_pad_0 = const()[name = tensor<string, []>("input_201_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_201_strides_0 = const()[name = tensor<string, []>("input_201_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_201_dilations_0 = const()[name = tensor<string, []>("input_201_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_201_groups_0 = const()[name = tensor<string, []>("input_201_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_22_cv3_0_1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_0_1_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4746560)))];
            tensor<fp16, [64]> model_22_cv3_0_1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_0_1_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4820352)))];
            tensor<fp16, [1, 64, 80, 80]> input_201_cast_fp16 = conv(bias = model_22_cv3_0_1_conv_bias_to_fp16, dilations = input_201_dilations_0, groups = input_201_groups_0, pad = input_201_pad_0, pad_type = input_201_pad_type_0, strides = input_201_strides_0, weight = model_22_cv3_0_1_conv_weight_to_fp16, x = input_199_cast_fp16)[name = tensor<string, []>("input_201_cast_fp16")];
            tensor<fp16, [1, 64, 80, 80]> input_203_cast_fp16 = silu(x = input_201_cast_fp16)[name = tensor<string, []>("input_203_cast_fp16")];
            tensor<string, []> var_752_pad_type_0 = const()[name = tensor<string, []>("op_752_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> var_752_strides_0 = const()[name = tensor<string, []>("op_752_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> var_752_pad_0 = const()[name = tensor<string, []>("op_752_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> var_752_dilations_0 = const()[name = tensor<string, []>("op_752_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> var_752_groups_0 = const()[name = tensor<string, []>("op_752_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [2, 64, 1, 1]> model_22_cv3_0_2_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_0_2_weight_to_fp16"), val = tensor<fp16, [2, 64, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4820544)))];
            tensor<fp16, [2]> model_22_cv3_0_2_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_0_2_bias_to_fp16"), val = tensor<fp16, [2]>([-0x1.f6p+2, -0x1.13p+3])];
            tensor<fp16, [1, 2, 80, 80]> var_752_cast_fp16 = conv(bias = model_22_cv3_0_2_bias_to_fp16, dilations = var_752_dilations_0, groups = var_752_groups_0, pad = var_752_pad_0, pad_type = var_752_pad_type_0, strides = var_752_strides_0, weight = model_22_cv3_0_2_weight_to_fp16, x = input_203_cast_fp16)[name = tensor<string, []>("op_752_cast_fp16")];
            tensor<bool, []> xi_1_interleave_0 = const()[name = tensor<string, []>("xi_1_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 66, 80, 80]> xi_1_cast_fp16 = concat(axis = var_680, interleave = xi_1_interleave_0, values = (var_724_cast_fp16, var_752_cast_fp16))[name = tensor<string, []>("xi_1_cast_fp16")];
            tensor<string, []> input_205_pad_type_0 = const()[name = tensor<string, []>("input_205_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_205_pad_0 = const()[name = tensor<string, []>("input_205_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_205_strides_0 = const()[name = tensor<string, []>("input_205_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_205_dilations_0 = const()[name = tensor<string, []>("input_205_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_205_groups_0 = const()[name = tensor<string, []>("input_205_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 128, 3, 3]> model_22_cv2_1_0_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_1_0_conv_weight_to_fp16"), val = tensor<fp16, [64, 128, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4820864)))];
            tensor<fp16, [64]> model_22_cv2_1_0_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_1_0_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4968384)))];
            tensor<fp16, [1, 64, 40, 40]> input_205_cast_fp16 = conv(bias = model_22_cv2_1_0_conv_bias_to_fp16, dilations = input_205_dilations_0, groups = input_205_groups_0, pad = input_205_pad_0, pad_type = input_205_pad_type_0, strides = input_205_strides_0, weight = model_22_cv2_1_0_conv_weight_to_fp16, x = input_169_cast_fp16)[name = tensor<string, []>("input_205_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> input_207_cast_fp16 = silu(x = input_205_cast_fp16)[name = tensor<string, []>("input_207_cast_fp16")];
            tensor<string, []> input_209_pad_type_0 = const()[name = tensor<string, []>("input_209_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_209_pad_0 = const()[name = tensor<string, []>("input_209_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_209_strides_0 = const()[name = tensor<string, []>("input_209_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_209_dilations_0 = const()[name = tensor<string, []>("input_209_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_209_groups_0 = const()[name = tensor<string, []>("input_209_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_22_cv2_1_1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_1_1_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(4968576)))];
            tensor<fp16, [64]> model_22_cv2_1_1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_1_1_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5042368)))];
            tensor<fp16, [1, 64, 40, 40]> input_209_cast_fp16 = conv(bias = model_22_cv2_1_1_conv_bias_to_fp16, dilations = input_209_dilations_0, groups = input_209_groups_0, pad = input_209_pad_0, pad_type = input_209_pad_type_0, strides = input_209_strides_0, weight = model_22_cv2_1_1_conv_weight_to_fp16, x = input_207_cast_fp16)[name = tensor<string, []>("input_209_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> input_211_cast_fp16 = silu(x = input_209_cast_fp16)[name = tensor<string, []>("input_211_cast_fp16")];
            tensor<string, []> var_782_pad_type_0 = const()[name = tensor<string, []>("op_782_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> var_782_strides_0 = const()[name = tensor<string, []>("op_782_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> var_782_pad_0 = const()[name = tensor<string, []>("op_782_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> var_782_dilations_0 = const()[name = tensor<string, []>("op_782_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> var_782_groups_0 = const()[name = tensor<string, []>("op_782_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 1, 1]> model_22_cv2_1_2_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_1_2_weight_to_fp16"), val = tensor<fp16, [64, 64, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5042560)))];
            tensor<fp16, [64]> model_22_cv2_1_2_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_1_2_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5050816)))];
            tensor<fp16, [1, 64, 40, 40]> var_782_cast_fp16 = conv(bias = model_22_cv2_1_2_bias_to_fp16, dilations = var_782_dilations_0, groups = var_782_groups_0, pad = var_782_pad_0, pad_type = var_782_pad_type_0, strides = var_782_strides_0, weight = model_22_cv2_1_2_weight_to_fp16, x = input_211_cast_fp16)[name = tensor<string, []>("op_782_cast_fp16")];
            tensor<string, []> input_213_pad_type_0 = const()[name = tensor<string, []>("input_213_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_213_pad_0 = const()[name = tensor<string, []>("input_213_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_213_strides_0 = const()[name = tensor<string, []>("input_213_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_213_dilations_0 = const()[name = tensor<string, []>("input_213_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_213_groups_0 = const()[name = tensor<string, []>("input_213_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 128, 3, 3]> model_22_cv3_1_0_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_1_0_conv_weight_to_fp16"), val = tensor<fp16, [64, 128, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5051008)))];
            tensor<fp16, [64]> model_22_cv3_1_0_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_1_0_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5198528)))];
            tensor<fp16, [1, 64, 40, 40]> input_213_cast_fp16 = conv(bias = model_22_cv3_1_0_conv_bias_to_fp16, dilations = input_213_dilations_0, groups = input_213_groups_0, pad = input_213_pad_0, pad_type = input_213_pad_type_0, strides = input_213_strides_0, weight = model_22_cv3_1_0_conv_weight_to_fp16, x = input_169_cast_fp16)[name = tensor<string, []>("input_213_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> input_215_cast_fp16 = silu(x = input_213_cast_fp16)[name = tensor<string, []>("input_215_cast_fp16")];
            tensor<string, []> input_217_pad_type_0 = const()[name = tensor<string, []>("input_217_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_217_pad_0 = const()[name = tensor<string, []>("input_217_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_217_strides_0 = const()[name = tensor<string, []>("input_217_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_217_dilations_0 = const()[name = tensor<string, []>("input_217_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_217_groups_0 = const()[name = tensor<string, []>("input_217_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_22_cv3_1_1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_1_1_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5198720)))];
            tensor<fp16, [64]> model_22_cv3_1_1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_1_1_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5272512)))];
            tensor<fp16, [1, 64, 40, 40]> input_217_cast_fp16 = conv(bias = model_22_cv3_1_1_conv_bias_to_fp16, dilations = input_217_dilations_0, groups = input_217_groups_0, pad = input_217_pad_0, pad_type = input_217_pad_type_0, strides = input_217_strides_0, weight = model_22_cv3_1_1_conv_weight_to_fp16, x = input_215_cast_fp16)[name = tensor<string, []>("input_217_cast_fp16")];
            tensor<fp16, [1, 64, 40, 40]> input_219_cast_fp16 = silu(x = input_217_cast_fp16)[name = tensor<string, []>("input_219_cast_fp16")];
            tensor<string, []> var_810_pad_type_0 = const()[name = tensor<string, []>("op_810_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> var_810_strides_0 = const()[name = tensor<string, []>("op_810_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> var_810_pad_0 = const()[name = tensor<string, []>("op_810_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> var_810_dilations_0 = const()[name = tensor<string, []>("op_810_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> var_810_groups_0 = const()[name = tensor<string, []>("op_810_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [2, 64, 1, 1]> model_22_cv3_1_2_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_1_2_weight_to_fp16"), val = tensor<fp16, [2, 64, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5272704)))];
            tensor<fp16, [2]> model_22_cv3_1_2_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_1_2_bias_to_fp16"), val = tensor<fp16, [2]>([-0x1.cbp+2, -0x1.d8cp+2])];
            tensor<fp16, [1, 2, 40, 40]> var_810_cast_fp16 = conv(bias = model_22_cv3_1_2_bias_to_fp16, dilations = var_810_dilations_0, groups = var_810_groups_0, pad = var_810_pad_0, pad_type = var_810_pad_type_0, strides = var_810_strides_0, weight = model_22_cv3_1_2_weight_to_fp16, x = input_219_cast_fp16)[name = tensor<string, []>("op_810_cast_fp16")];
            tensor<bool, []> xi_3_interleave_0 = const()[name = tensor<string, []>("xi_3_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 66, 40, 40]> xi_3_cast_fp16 = concat(axis = var_680, interleave = xi_3_interleave_0, values = (var_782_cast_fp16, var_810_cast_fp16))[name = tensor<string, []>("xi_3_cast_fp16")];
            tensor<string, []> input_223_pad_type_0 = const()[name = tensor<string, []>("input_223_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_223_pad_0 = const()[name = tensor<string, []>("input_223_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_223_strides_0 = const()[name = tensor<string, []>("input_223_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_223_dilations_0 = const()[name = tensor<string, []>("input_223_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_223_groups_0 = const()[name = tensor<string, []>("input_223_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 256, 3, 3]> model_22_cv2_2_0_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_2_0_conv_weight_to_fp16"), val = tensor<fp16, [64, 256, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5273024)))];
            tensor<fp16, [64]> model_22_cv2_2_0_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_2_0_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5568000)))];
            tensor<fp16, [1, 64, 20, 20]> input_223_cast_fp16 = conv(bias = model_22_cv2_2_0_conv_bias_to_fp16, dilations = input_223_dilations_0, groups = input_223_groups_0, pad = input_223_pad_0, pad_type = input_223_pad_type_0, strides = input_223_strides_0, weight = model_22_cv2_2_0_conv_weight_to_fp16, x = input_221_cast_fp16)[name = tensor<string, []>("input_223_cast_fp16")];
            tensor<fp16, [1, 64, 20, 20]> input_225_cast_fp16 = silu(x = input_223_cast_fp16)[name = tensor<string, []>("input_225_cast_fp16")];
            tensor<string, []> input_227_pad_type_0 = const()[name = tensor<string, []>("input_227_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_227_pad_0 = const()[name = tensor<string, []>("input_227_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_227_strides_0 = const()[name = tensor<string, []>("input_227_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_227_dilations_0 = const()[name = tensor<string, []>("input_227_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_227_groups_0 = const()[name = tensor<string, []>("input_227_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_22_cv2_2_1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_2_1_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5568192)))];
            tensor<fp16, [64]> model_22_cv2_2_1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_2_1_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5641984)))];
            tensor<fp16, [1, 64, 20, 20]> input_227_cast_fp16 = conv(bias = model_22_cv2_2_1_conv_bias_to_fp16, dilations = input_227_dilations_0, groups = input_227_groups_0, pad = input_227_pad_0, pad_type = input_227_pad_type_0, strides = input_227_strides_0, weight = model_22_cv2_2_1_conv_weight_to_fp16, x = input_225_cast_fp16)[name = tensor<string, []>("input_227_cast_fp16")];
            tensor<fp16, [1, 64, 20, 20]> input_229_cast_fp16 = silu(x = input_227_cast_fp16)[name = tensor<string, []>("input_229_cast_fp16")];
            tensor<string, []> var_840_pad_type_0 = const()[name = tensor<string, []>("op_840_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> var_840_strides_0 = const()[name = tensor<string, []>("op_840_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> var_840_pad_0 = const()[name = tensor<string, []>("op_840_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> var_840_dilations_0 = const()[name = tensor<string, []>("op_840_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> var_840_groups_0 = const()[name = tensor<string, []>("op_840_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 1, 1]> model_22_cv2_2_2_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_2_2_weight_to_fp16"), val = tensor<fp16, [64, 64, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5642176)))];
            tensor<fp16, [64]> model_22_cv2_2_2_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv2_2_2_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5650432)))];
            tensor<fp16, [1, 64, 20, 20]> var_840_cast_fp16 = conv(bias = model_22_cv2_2_2_bias_to_fp16, dilations = var_840_dilations_0, groups = var_840_groups_0, pad = var_840_pad_0, pad_type = var_840_pad_type_0, strides = var_840_strides_0, weight = model_22_cv2_2_2_weight_to_fp16, x = input_229_cast_fp16)[name = tensor<string, []>("op_840_cast_fp16")];
            tensor<string, []> input_231_pad_type_0 = const()[name = tensor<string, []>("input_231_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_231_pad_0 = const()[name = tensor<string, []>("input_231_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_231_strides_0 = const()[name = tensor<string, []>("input_231_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_231_dilations_0 = const()[name = tensor<string, []>("input_231_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_231_groups_0 = const()[name = tensor<string, []>("input_231_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 256, 3, 3]> model_22_cv3_2_0_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_2_0_conv_weight_to_fp16"), val = tensor<fp16, [64, 256, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5650624)))];
            tensor<fp16, [64]> model_22_cv3_2_0_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_2_0_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5945600)))];
            tensor<fp16, [1, 64, 20, 20]> input_231_cast_fp16 = conv(bias = model_22_cv3_2_0_conv_bias_to_fp16, dilations = input_231_dilations_0, groups = input_231_groups_0, pad = input_231_pad_0, pad_type = input_231_pad_type_0, strides = input_231_strides_0, weight = model_22_cv3_2_0_conv_weight_to_fp16, x = input_221_cast_fp16)[name = tensor<string, []>("input_231_cast_fp16")];
            tensor<fp16, [1, 64, 20, 20]> input_233_cast_fp16 = silu(x = input_231_cast_fp16)[name = tensor<string, []>("input_233_cast_fp16")];
            tensor<string, []> input_235_pad_type_0 = const()[name = tensor<string, []>("input_235_pad_type_0"), val = tensor<string, []>("custom")];
            tensor<int32, [4]> input_235_pad_0 = const()[name = tensor<string, []>("input_235_pad_0"), val = tensor<int32, [4]>([1, 1, 1, 1])];
            tensor<int32, [2]> input_235_strides_0 = const()[name = tensor<string, []>("input_235_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [2]> input_235_dilations_0 = const()[name = tensor<string, []>("input_235_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> input_235_groups_0 = const()[name = tensor<string, []>("input_235_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [64, 64, 3, 3]> model_22_cv3_2_1_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_2_1_conv_weight_to_fp16"), val = tensor<fp16, [64, 64, 3, 3]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(5945792)))];
            tensor<fp16, [64]> model_22_cv3_2_1_conv_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_2_1_conv_bias_to_fp16"), val = tensor<fp16, [64]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(6019584)))];
            tensor<fp16, [1, 64, 20, 20]> input_235_cast_fp16 = conv(bias = model_22_cv3_2_1_conv_bias_to_fp16, dilations = input_235_dilations_0, groups = input_235_groups_0, pad = input_235_pad_0, pad_type = input_235_pad_type_0, strides = input_235_strides_0, weight = model_22_cv3_2_1_conv_weight_to_fp16, x = input_233_cast_fp16)[name = tensor<string, []>("input_235_cast_fp16")];
            tensor<fp16, [1, 64, 20, 20]> input_237_cast_fp16 = silu(x = input_235_cast_fp16)[name = tensor<string, []>("input_237_cast_fp16")];
            tensor<string, []> var_868_pad_type_0 = const()[name = tensor<string, []>("op_868_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> var_868_strides_0 = const()[name = tensor<string, []>("op_868_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> var_868_pad_0 = const()[name = tensor<string, []>("op_868_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> var_868_dilations_0 = const()[name = tensor<string, []>("op_868_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> var_868_groups_0 = const()[name = tensor<string, []>("op_868_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [2, 64, 1, 1]> model_22_cv3_2_2_weight_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_2_2_weight_to_fp16"), val = tensor<fp16, [2, 64, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(6019776)))];
            tensor<fp16, [2]> model_22_cv3_2_2_bias_to_fp16 = const()[name = tensor<string, []>("model_22_cv3_2_2_bias_to_fp16"), val = tensor<fp16, [2]>([-0x1.95p+2, -0x1.9ap+2])];
            tensor<fp16, [1, 2, 20, 20]> var_868_cast_fp16 = conv(bias = model_22_cv3_2_2_bias_to_fp16, dilations = var_868_dilations_0, groups = var_868_groups_0, pad = var_868_pad_0, pad_type = var_868_pad_type_0, strides = var_868_strides_0, weight = model_22_cv3_2_2_weight_to_fp16, x = input_237_cast_fp16)[name = tensor<string, []>("op_868_cast_fp16")];
            tensor<bool, []> xi_interleave_0 = const()[name = tensor<string, []>("xi_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 66, 20, 20]> xi_cast_fp16 = concat(axis = var_680, interleave = xi_interleave_0, values = (var_840_cast_fp16, var_868_cast_fp16))[name = tensor<string, []>("xi_cast_fp16")];
            tensor<int32, [3]> var_872 = const()[name = tensor<string, []>("op_872"), val = tensor<int32, [3]>([1, 66, -1])];
            tensor<fp16, [1, 66, 6400]> var_873_cast_fp16 = reshape(shape = var_872, x = xi_1_cast_fp16)[name = tensor<string, []>("op_873_cast_fp16")];
            tensor<int32, [3]> var_874 = const()[name = tensor<string, []>("op_874"), val = tensor<int32, [3]>([1, 66, -1])];
            tensor<fp16, [1, 66, 1600]> var_875_cast_fp16 = reshape(shape = var_874, x = xi_3_cast_fp16)[name = tensor<string, []>("op_875_cast_fp16")];
            tensor<int32, [3]> var_876 = const()[name = tensor<string, []>("op_876"), val = tensor<int32, [3]>([1, 66, -1])];
            tensor<fp16, [1, 66, 400]> var_877_cast_fp16 = reshape(shape = var_876, x = xi_cast_fp16)[name = tensor<string, []>("op_877_cast_fp16")];
            tensor<bool, []> var_879_interleave_0 = const()[name = tensor<string, []>("op_879_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 66, 8400]> var_879_cast_fp16 = concat(axis = var_677, interleave = var_879_interleave_0, values = (var_873_cast_fp16, var_875_cast_fp16, var_877_cast_fp16))[name = tensor<string, []>("op_879_cast_fp16")];
            tensor<int32, [2]> var_880 = const()[name = tensor<string, []>("op_880"), val = tensor<int32, [2]>([64, 2])];
            tensor<int32, []> var_881_axis_0 = const()[name = tensor<string, []>("op_881_axis_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [1, 64, 8400]> var_881_cast_fp16_0, tensor<fp16, [1, 2, 8400]> var_881_cast_fp16_1 = split(axis = var_881_axis_0, split_sizes = var_880, x = var_879_cast_fp16)[name = tensor<string, []>("op_881_cast_fp16")];
            tensor<int32, [4]> var_887 = const()[name = tensor<string, []>("op_887"), val = tensor<int32, [4]>([1, 4, 16, 8400])];
            tensor<fp16, [1, 4, 16, 8400]> var_888_cast_fp16 = reshape(shape = var_887, x = var_881_cast_fp16_0)[name = tensor<string, []>("op_888_cast_fp16")];
            tensor<int32, [4]> var_889_perm_0 = const()[name = tensor<string, []>("op_889_perm_0"), val = tensor<int32, [4]>([0, 2, 1, 3])];
            tensor<fp16, [1, 16, 4, 8400]> var_889_cast_fp16 = transpose(perm = var_889_perm_0, x = var_888_cast_fp16)[name = tensor<string, []>("transpose_0")];
            tensor<fp16, [1, 16, 4, 8400]> input_cast_fp16 = softmax(axis = var_680, x = var_889_cast_fp16)[name = tensor<string, []>("input_cast_fp16")];
            tensor<string, []> var_896_pad_type_0 = const()[name = tensor<string, []>("op_896_pad_type_0"), val = tensor<string, []>("valid")];
            tensor<int32, [2]> var_896_strides_0 = const()[name = tensor<string, []>("op_896_strides_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, [4]> var_896_pad_0 = const()[name = tensor<string, []>("op_896_pad_0"), val = tensor<int32, [4]>([0, 0, 0, 0])];
            tensor<int32, [2]> var_896_dilations_0 = const()[name = tensor<string, []>("op_896_dilations_0"), val = tensor<int32, [2]>([1, 1])];
            tensor<int32, []> var_896_groups_0 = const()[name = tensor<string, []>("op_896_groups_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [1, 16, 1, 1]> model_22_dfl_conv_weight_to_fp16 = const()[name = tensor<string, []>("model_22_dfl_conv_weight_to_fp16"), val = tensor<fp16, [1, 16, 1, 1]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(6020096)))];
            tensor<fp16, [1, 1, 4, 8400]> var_896_cast_fp16 = conv(dilations = var_896_dilations_0, groups = var_896_groups_0, pad = var_896_pad_0, pad_type = var_896_pad_type_0, strides = var_896_strides_0, weight = model_22_dfl_conv_weight_to_fp16, x = input_cast_fp16)[name = tensor<string, []>("op_896_cast_fp16")];
            tensor<int32, [3]> var_897 = const()[name = tensor<string, []>("op_897"), val = tensor<int32, [3]>([1, 4, 8400])];
            tensor<fp16, [1, 4, 8400]> distance_cast_fp16 = reshape(shape = var_897, x = var_896_cast_fp16)[name = tensor<string, []>("distance_cast_fp16")];
            tensor<int32, [2]> var_900_split_sizes_0 = const()[name = tensor<string, []>("op_900_split_sizes_0"), val = tensor<int32, [2]>([2, 2])];
            tensor<int32, []> var_900_axis_0 = const()[name = tensor<string, []>("op_900_axis_0"), val = tensor<int32, []>(1)];
            tensor<fp16, [1, 2, 8400]> var_900_cast_fp16_0, tensor<fp16, [1, 2, 8400]> var_900_cast_fp16_1 = split(axis = var_900_axis_0, split_sizes = var_900_split_sizes_0, x = distance_cast_fp16)[name = tensor<string, []>("op_900_cast_fp16")];
            tensor<fp16, [1, 2, 8400]> anchor_points_to_fp16 = const()[name = tensor<string, []>("anchor_points_to_fp16"), val = tensor<fp16, [1, 2, 8400]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(6020224)))];
            tensor<fp16, [1, 2, 8400]> x1y1_cast_fp16 = sub(x = anchor_points_to_fp16, y = var_900_cast_fp16_0)[name = tensor<string, []>("x1y1_cast_fp16")];
            tensor<fp16, [1, 2, 8400]> x2y2_cast_fp16 = add(x = anchor_points_to_fp16, y = var_900_cast_fp16_1)[name = tensor<string, []>("x2y2_cast_fp16")];
            tensor<fp16, [1, 2, 8400]> var_904_cast_fp16 = add(x = x1y1_cast_fp16, y = x2y2_cast_fp16)[name = tensor<string, []>("op_904_cast_fp16")];
            tensor<fp16, []> _inversed_c_xy_y_0_to_fp16 = const()[name = tensor<string, []>("_inversed_c_xy_y_0_to_fp16"), val = tensor<fp16, []>(0x1p-1)];
            tensor<fp16, [1, 2, 8400]> _inversed_c_xy_cast_fp16 = mul(x = var_904_cast_fp16, y = _inversed_c_xy_y_0_to_fp16)[name = tensor<string, []>("_inversed_c_xy_cast_fp16")];
            tensor<fp16, [1, 2, 8400]> wh_cast_fp16 = sub(x = x2y2_cast_fp16, y = x1y1_cast_fp16)[name = tensor<string, []>("wh_cast_fp16")];
            tensor<bool, []> var_909_interleave_0 = const()[name = tensor<string, []>("op_909_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 4, 8400]> var_909_cast_fp16 = concat(axis = var_680, interleave = var_909_interleave_0, values = (_inversed_c_xy_cast_fp16, wh_cast_fp16))[name = tensor<string, []>("op_909_cast_fp16")];
            tensor<fp16, [1, 8400]> var_671_to_fp16 = const()[name = tensor<string, []>("op_671_to_fp16"), val = tensor<fp16, [1, 8400]>(BLOBFILE(path = tensor<string, []>("@model_path/weights/weight.bin"), offset = tensor<uint64, []>(6053888)))];
            tensor<fp16, [1, 4, 8400]> dbox_cast_fp16 = mul(x = var_909_cast_fp16, y = var_671_to_fp16)[name = tensor<string, []>("dbox_cast_fp16")];
            tensor<fp16, [1, 2, 8400]> var_911_cast_fp16 = sigmoid(x = var_881_cast_fp16_1)[name = tensor<string, []>("op_911_cast_fp16")];
            tensor<bool, []> var_913_interleave_0 = const()[name = tensor<string, []>("op_913_interleave_0"), val = tensor<bool, []>(false)];
            tensor<fp16, [1, 6, 8400]> var_913_cast_fp16 = concat(axis = var_680, interleave = var_913_interleave_0, values = (dbox_cast_fp16, var_911_cast_fp16))[name = tensor<string, []>("op_913_cast_fp16")];
            tensor<string, []> var_913_cast_fp16_to_fp32_dtype_0 = const()[name = tensor<string, []>("op_913_cast_fp16_to_fp32_dtype_0"), val = tensor<string, []>("fp32")];
            tensor<fp32, [1, 6, 8400]> var_913 = cast(dtype = var_913_cast_fp16_to_fp32_dtype_0, x = var_913_cast_fp16)[name = tensor<string, []>("cast_7")];
        } -> (var_913);
}