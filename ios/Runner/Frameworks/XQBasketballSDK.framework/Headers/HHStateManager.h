//
//  HHStateManager.h
//  FaceRecognitionTracking
//
//  Created by 林漫钦 on 2025/9/18.
//

#import <Foundation/Foundation.h>
#import <XQBasketballSDK/StateEvent.h>

@protocol HHConnListener;

typedef void(^LimitStateCallBackBlock)(LimitState state);

@interface HHStateManager : NSObject

/// 监听回调对象（弱引用，避免循环引用）
@property (nonatomic, weak) id<HHConnListener> listener;
///初始化监听对象
- (instancetype)initWithListener:(id<HHConnListener>)listener;
///查询电量:当前电量时以格数: 3格-> 满电， 2格, 1格 -> 电量低
- (int)readBattery;
///查询云台固件版本
- (void)readGimbalVersion;
///立刻发送指令到云台读取限位状态后返回数据（准确性）
- (void)readCurrentLimitStatus:(LimitStateCallBackBlock) block;
///云台限位状态查询 (该方法是获取连接蓝牙后自动查询指令后缓存在state类里面数据的状态，需在连接蓝牙成功后延后大约1.5s执行)
- (LimitState)readLimitStatus;

@end

