//
//  TrackEvent.h
//  FaceRecognitionTracking
//
//  Created by 林漫钦 on 2025/9/18.
//

#import <Foundation/Foundation.h>

typedef enum {
    TrackEventType_Shot = 0,            //投篮
    TrackEventType_FieldGoalMade = 1,    //投篮命中
    TrackEventType_FieldGoalMissed = 2,  //投篮未命中
} TrackEventType;

typedef NSString * TrackEventName NS_EXTENSIBLE_STRING_ENUM;
FOUNDATION_EXPORT TrackEventName const TrackEventBasketballShoot;   // 投篮事件

@interface TrackEvent : NSObject

@property (nonatomic, strong) NSString *UUID; //事件唯一标志
@property (nonatomic, assign) int type; //类型
@property (nonatomic, assign) int score; //得分
@property (nonatomic, assign) long timestamp; //时间戳
@property (nonatomic, strong) NSString *desc; //可选，其他调试信息

@end

