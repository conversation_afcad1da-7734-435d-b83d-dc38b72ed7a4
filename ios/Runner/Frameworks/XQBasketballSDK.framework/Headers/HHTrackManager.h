//
//  HHTrackManager.h
//  FaceRecognitionTracking
//
//  Created by 林漫钦 on 2025/9/18.
//

#import <Foundation/Foundation.h>
#import <CoreVideo/CoreVideo.h>
#import <XQBasketballSDK/TrackEvent.h>


@protocol HHTracklistener <NSObject>
@optional
- (void)onTrackEvent:(TrackEvent *)event;
@end

@interface HHTrackManager : NSObject

@property (nonatomic, weak) id<HHTracklistener> listener;
@property (nonatomic, assign) BOOL isFrontCamera;

///初始化设置监听对象
- (instancetype)initWithListener:(id<HHTracklistener>)listener;
///开始追踪
- (void)startTrack;
///停止追踪
- (void)stopTrack;
///处理帧数据
- (void)handleFrame:(CVPixelBufferRef)pixelBuffer;
///清零分数
- (void)clearScore;

@end

