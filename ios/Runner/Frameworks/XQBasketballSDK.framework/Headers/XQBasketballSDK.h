//
//  XQBasketballSDK.h
//  XQBasketballSDK
//
//  Created by 林漫钦 on 2025/9/12.
//

#import <Foundation/Foundation.h>
#import <XQBasketballSDK/HHConnectManager.h>
#import <XQBasketballSDK/HHYunTaiDevice.h>
#import <XQBasketballSDK/HHTrackManager.h>
#import <XQBasketballSDK/HHStateManager.h>
#import <XQBasketballSDK/StateEvent.h>
#import <XQBasketballSDK/TrackEvent.h>
#import <XQBasketballSDK/BluetoothParamInfo.h>

//! Project version number for XQBasketballSDK.
FOUNDATION_EXPORT double XQBasketballSDKVersionNumber;

//! Project version string for XQBasketballSDK.
FOUNDATION_EXPORT const unsigned char XQBasketballSDKVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <XQBasketballSDK/PublicHeader.h>
