//
//  BluetoothParamInfo.h
//  XQBasketballSDK
//
//  Created by 林漫钦 on 2025/9/13.
//

#ifndef BluetoothParamInfo_h
#define BluetoothParamInfo_h

//限位状态
typedef NS_ENUM(NSInteger,HHTrackLimitState) {

    HHTrackLimitState_Unknow,
    HHTrackLimitState_LeftSide,
    HHTrackLimitState_Center,
    HHTrackLimitState_RightSide,
    HHTrackLimitState_ReachedLeft,
    HHTrackLimitState_ExitedLeft,
    HHTrackLimitState_ReachedRight,
    HHTrackLimitState_ExitedRight,
    HHTrackLimitState_ReachedUp,
    HHTrackLimitState_ExitedUp,
    HHTrackLimitState_ReachedDown,
    HHTrackLimitState_ExitedDown,
};


#endif /* BluetoothParamInfo_h */
