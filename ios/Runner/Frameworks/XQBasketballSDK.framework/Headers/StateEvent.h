//
//  StateEvent.h
//  FaceRecognitionTracking
//
//  Created by 林漫钦 on 2025/9/18.
//

#import <Foundation/Foundation.h>

typedef enum {
    DeviceStatus_Unknown = 0,
    DeviceStatus_LOW_BATTERY = 1,      // 设备电量低
    DeviceStatus_OVERLOAD = 2,         // 设备过载
    DeviceStatus_HARDWARE_ERROR = 3,   // 硬件错误
    DeviceStatus_LEFT_LIMIT = 4,       // 到达左限位
    DeviceStatus_RIGHT_LIMIT = 5       // 到达右限位
} DeviceStatus;


@interface StateEvent : NSObject

@property (nonatomic, assign) DeviceStatus type;
@property (nonatomic, assign) int errorCode;
@property (nonatomic, strong) NSString *desc;

@end

