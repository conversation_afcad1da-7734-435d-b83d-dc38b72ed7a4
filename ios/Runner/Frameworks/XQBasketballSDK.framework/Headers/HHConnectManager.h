//
//  HHConnectManager.h
//  FaceRecognitionTracking
//
//  Created by 林漫钦 on 2025/9/18.
//

#import <Foundation/Foundation.h>
#import <XQBasketballSDK/StateEvent.h>

@class ConnEvent,HHDevice;

typedef enum {
    ConnStatus_Unknown = 0,
    ConnStatus_ConnectSuccessful = 1, // 设备连接成功
    ConnStatus_ConnectFailure = 2,   // 设备连接失败
    ConnStatus_Disconnected = 3,   // 连接断开
    ConnStatus_BLEPoweredOff = 4,   // 蓝牙未打开
    ConnStatus_BLEUnauthorized = 5,   // 蓝牙未授权
} ConnStatus;

typedef enum {
    RotationlimitLeft = 1, //左边
    RotationlimitRight = 2, //右边
} Rotationlimit;

typedef enum {
    DirectionTypeLandscape = 1, //左边
    DirectionTypePortrait = 2, //右边
} DirectionType;

/*
 连接监听协议
 */
@protocol HHConnListener <NSObject>
/// 发现设备
- (void)onDeviceFound:(NSArray<HHDevice *> *)deviceList;
/// 连接状态变更
- (void)onConnChangeEvent:(ConnEvent *)event;
/// 其他设备状态变化
- (void)onDeviceStateChanged:(StateEvent *)event;

@optional
///查询设备参数返回
- (void)onDeviceInfoInquire:(InfoEvent *)event;
@end

/*
 连接设备模型
 */
@interface HHDevice : NSObject
@property (nonatomic, strong) NSString *macAddress; // 蓝⽛地址
@property (nonatomic, assign) int rssi; //信号强度 dBm（分贝毫瓦）数值越接近0表示信号越强 ‌-30 dBm‌：极强信号（设备靠近发射源）
@property (nonatomic, strong) NSString *name; //设备名
@property (nonatomic, strong) NSString *serial; //设备序列号
@end

/*
 连接事件
 */
@interface ConnEvent : NSObject
@property (nonatomic, assign) ConnStatus type;
@property (nonatomic, assign) int errorCode;
@property (nonatomic, strong) NSString *desc;
@end


/*
 连接模块
 */
@interface HHConnectManager : NSObject

/// 监听回调对象（弱引用，避免循环引用）
@property (nonatomic, weak) id<HHConnListener> listener;
///初始化监听对象
- (instancetype)initWithListener:(id<HHConnListener>)listener;
/// 扫描设备
- (void)startScan;
///停止扫描
- (void)stopScan;
/// 连接指定设备
- (void)connect:(NSString *)deviceName;
/// 断开连接
- (void)disconnect;
///Log信息打印使能
- (void)enableLogPrint:(BOOL)enable;
///设置左右限位
- (void)setRotationlimit:(Rotationlimit)rotation;
///设置旋转
- (void)setDirection:(DirectionType)direction;
///启用限位
- (void)enableRotationlimit:(BOOL)enable;

@end

