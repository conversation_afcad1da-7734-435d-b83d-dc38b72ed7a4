#version 300 es

precision highp float;
precision highp sampler2D;

uniform sampler2D luminanceTexture;
uniform sampler2D chrominanceTexture;
uniform int       enableXMirrorImg;
uniform int       enableYMirrorImg;
uniform float     rotateAngle;
in vec2 TextureCoordsVarying;
out vec4 glFragColor;

vec4 nv12ToRgb (vec2 textureCoord)
{
    mediump vec3 yuv;
    lowp vec3 rgb;
    
    yuv.x = texture(luminanceTexture, textureCoord).r - (16.0/255.0);
    yuv.yz = texture(chrominanceTexture, textureCoord).ra - vec2(0.5, 0.5);
   
    rgb = mat3( 1.164,    1.164,   1.164,
                0.0,     -0.213,   2.112,
               1.793,    -0.533,     0.0) * yuv;
    
    return vec4(rgb,1.0);
}

void main(){

    glFragColor.w = 1.0;
    
    vec2 uv = TextureCoordsVarying;
    
    if (rotateAngle == 90.0) {
        uv = vec2(TextureCoordsVarying.y,1.0 - TextureCoordsVarying.x);
    }
    else if (rotateAngle == 180.0){
        uv = vec2(1.0 - TextureCoordsVarying.x,1.0 -TextureCoordsVarying.y);
    }
    else if (rotateAngle == 270.0){
        uv = vec2(1.0 - TextureCoordsVarying.y,TextureCoordsVarying.x);
    }
    
    if (enableYMirrorImg == 1) {
        uv = vec2(uv.x, 1.0 - uv.y);
    }
    
    if (enableXMirrorImg == 1) {
        uv = vec2(1.0 - uv.x, uv.y);
    }
    
    glFragColor = nv12ToRgb(uv);
}
