#version 300 es

precision highp float;
precision highp int;
precision highp sampler2D;

uniform sampler2D fromTexture;
uniform int       enableXMirrorImg;
uniform int       enableYMirrorImg;
uniform float     rotateAngle;

in vec2 TextureCoordsVarying;
out vec4 glFragColor;


void main(){

    glFragColor.w = 1.0;
    
    vec2 uv = TextureCoordsVarying;
    
    if (rotateAngle == 90.0) {
        uv = vec2(TextureCoordsVarying.y,1.0 - TextureCoordsVarying.x);
    }
    else if (rotateAngle == 180.0){
        uv = vec2(1.0 - TextureCoordsVarying.x,1.0 -TextureCoordsVarying.y);
    }
    else if (rotateAngle == 270.0){
        uv = vec2(1.0 - TextureCoordsVarying.y,TextureCoordsVarying.x);
    }
    
    if (enableYMirrorImg == 1) {
        uv = vec2(uv.x, 1.0 - uv.y);
    }
    
    if (enableXMirrorImg == 1) {
        uv = vec2(1.0 - uv.x, uv.y);
    }
    
    glFragColor = texture(fromTexture, uv);
}
