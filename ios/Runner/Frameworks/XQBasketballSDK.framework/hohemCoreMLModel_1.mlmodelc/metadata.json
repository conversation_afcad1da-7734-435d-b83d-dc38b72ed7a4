[{"shortDescription": "Ultralytics best model trained on ./config/people.yaml", "metadataOutputVersion": "3.0", "outputSchema": [{"hasShapeFlexibility": "0", "isOptional": "0", "dataType": "Float32", "formattedType": "MultiArray (Float32 1 × 7 × 8400)", "shortDescription": "--", "shape": "[1, 7, 8400]", "name": "var_914", "type": "MultiArray"}], "version": "8.3.169", "modelParameters": [], "author": "Ultralytics", "specificationVersion": 6, "storagePrecision": "Float16", "license": "AGPL-3.0 License (https://ultralytics.com/license)", "mlProgramOperationTypeHistogram": {"Concat": 19, "Silu": 57, "Sub": 2, "Transpose": 1, "UpsampleNearestNeighbor": 2, "Softmax": 1, "Mul": 3, "Cast": 2, "Reshape": 5, "Add": 8, "MaxPool": 3, "Sigmoid": 1, "Split": 10, "Conv": 64}, "computePrecision": "Mixed (Float16, Float32, Int32)", "stateSchema": [], "isUpdatable": "0", "availability": {"macOS": "12.0", "tvOS": "15.0", "visionOS": "1.0", "watchOS": "8.0", "iOS": "15.0", "macCatalyst": "15.0"}, "modelType": {"name": "MLModelType_mlProgram"}, "inputSchema": [{"height": "640", "colorspace": "RGB", "isOptional": "0", "width": "640", "isColor": "1", "formattedType": "Image (Color 640 × 640)", "hasSizeFlexibility": "0", "type": "Image", "shortDescription": "--", "name": "image"}], "userDefinedMetadata": {"batch": "1", "com.github.apple.coremltools.version": "8.2", "docs": "https://docs.ultralytics.com", "imgsz": "[640, 640]", "names": "{0: 'person', 1: 'head', 2: 'face'}", "channels": "3", "stride": "32", "date": "2025-07-25T03:58:27.187827", "args": "{'batch': 1, 'half': False, 'int8': False, 'nms': False}", "com.github.apple.coremltools.source_dialect": "TorchScript", "com.github.apple.coremltools.source": "torch==2.5.1+cu121", "task": "detect"}, "generatedClassName": "hohemCoreMLModel_1", "method": "predict"}]