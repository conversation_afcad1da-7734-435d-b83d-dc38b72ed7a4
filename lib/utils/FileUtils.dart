import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';

class FileUtils {
  // static Future<void> deleteAllFile(List<ShotRecordModel> dataList) async {
  //   for (int i = 0; i < dataList.length; i++) {

  //   }
  // }
  /// 删除指定路径的文件
  static Future<void> deleteFile(String filePath,
      {BuildContext? context, bool isShow = true}) async {
    try {
      final file = File(filePath);

      // 检查文件是否存在
      if (!await file.exists()) {
        if (isShow) {
          _showFeedback(
            context,
            "文件不存在: $filePath",
            ToastType.error,
          );
        }
      } else {
        // 尝试删除文件
        await file.delete();

        // 验证文件是否删除成功
        if (isShow) {
          if (await file.exists()) {
            _showFeedback(
              context,
              "文件删除失败: 文件仍存在",
              ToastType.error,
            );
          }
        } else {
          // 删除成功通知
          // _showFeedback(
          //   context,
          //   "文件已成功删除: ${file.path.split('/').last}",
          //   ToastType.success,
          // );
        }
      }
    } catch (e, stackTrace) {
      debugPrint("文件删除失败: $e");
      debugPrint(stackTrace.toString());
      if (isShow) {
        _showFeedback(
          context,
          "文件删除出错: ${e.toString()}",
          ToastType.error,
        );
      }
    }
  }
  // /// 删除指定路径的文件
  // static Future<void> deleteFile(String filePath,
  //     {BuildContext? context}) async {
  //   try {
  //     final file = File(filePath);

  //     // 检查文件是否存在
  //     if (!await file.exists()) {
  //       _showFeedback(
  //         context,
  //         "文件不存在: $filePath",
  //         ToastType.error,
  //       );
  //       return;
  //     }

  //     // 尝试删除文件
  //     await file.delete();

  //     // 验证文件是否删除成功
  //     if (await file.exists()) {
  //       _showFeedback(
  //         context,
  //         "文件删除失败: 文件仍存在",
  //         ToastType.error,
  //       );
  //       return;
  //     }

  //     // 删除成功通知
  //     _showFeedback(
  //       context,
  //       "文件已成功删除: ${file.path.split('/').last}",
  //       ToastType.success,
  //     );
  //   } catch (e, stackTrace) {
  //     debugPrint("文件删除失败: $e");
  //     debugPrint(stackTrace.toString());

  //     _showFeedback(
  //       context,
  //       "文件删除出错: ${e.toString()}",
  //       ToastType.error,
  //     );
  //   }
  // }

  /// 删除整个目录（包含子目录和文件）
  static Future<void> deleteDirectory(String directoryPath,
      {BuildContext? context}) async {
    try {
      final directory = Directory(directoryPath);

      // 检查目录是否存在
      if (!await directory.exists()) {
        _showFeedback(
          context,
          "目录不存在: $directoryPath",
          ToastType.error,
        );
        return;
      }

      // 删除目录及其所有内容
      await directory.delete(recursive: true);

      // 验证目录是否删除成功
      if (await directory.exists()) {
        _showFeedback(
          context,
          "目录删除失败: 目录仍存在",
          ToastType.error,
        );
        return;
      }

      // 删除成功通知
      _showFeedback(
        context,
        "目录已成功删除: ${directory.path.split('/').last}",
        ToastType.success,
      );
    } catch (e, stackTrace) {
      debugPrint("目录删除失败: $e");
      debugPrint(stackTrace.toString());

      _showFeedback(
        context,
        "目录删除出错: ${e.toString()}",
        ToastType.error,
      );
    }
  }

  // 显示反馈信息
  static void _showFeedback(
      BuildContext? context, String message, ToastType type) {
    if (context != null) {
      // 如果上下文可用，使用 ScaffoldMessenger 显示 SnackBar
      final snackBar = SnackBar(
        content: Text(message),
        backgroundColor: type == ToastType.error
            ? Colors.red
            : (type == ToastType.success ? Colors.green : Colors.blue),
      );
      ScaffoldMessenger.of(context).showSnackBar(snackBar);
    } else {
      // 如果上下文不可用，使用 Toast 显示通知
      WxLoading.showToast(message);
    }
  }
}

enum ToastType { success, error, info }
