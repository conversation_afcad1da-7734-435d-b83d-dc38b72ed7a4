import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:shoot_z/network/model/venue_goal_model.dart';
import 'package:shoot_z/pages/login/user.dart';

class FileDownloadService {
  static final _instance = FileDownloadService._internal();
  factory FileDownloadService() => _instance;
  FileDownloadService._internal();

  /// 你的原始下载方法增强版
  Future<String?> downloadVideo({
    required String url,
    required String savePath,
    void Function(int received, int total)? onProgress,
    void Function(ApiError)? onFailure,
  }) async {
    try {
      log("getCanDownLoadVideoUrl=1 ${url}");
      // 检查目录存在
      final file = File(savePath);
      if (!await file.parent.exists()) {
        await file.parent.create(recursive: true);
      }
      log("getCanDownLoadVideoUrl=2 ${url}");
      // 调用你的原始下载方法
      final res = await Api().doDownload(
        url,
        savePath,
        failure: (error) {
          //  onFailure?.call(error);
          throw error; // 转换为异常
        },
        completion: () {
          //  onFailure?.call(error);
          log("getCanDownLoadVideoUrl=3 ${savePath}");
        },
        progress: (count, total) {
          log("getCanDownLoadVideoUrl=31 ${count}- ${total}");
          onProgress!(count, total);
        },
      );
      if (res?.statusCode == 200) {
        log("getCanDownLoadVideoUrl=32");
        return savePath;
      } else {
        log("getCanDownLoadVideoUrl=33");
        return null;
      }
    } on ApiError catch (e) {
      debugPrint('Download failed (API): ${e.message}');
      onFailure?.call(e);
      log("getCanDownLoadVideoUrl=4 ${url}");
      rethrow;
    } catch (e) {
      debugPrint('Download failed: $e');
      log("getCanDownLoadVideoUrl=5 ${url}");
      return null;
    }
  }

  /// 批量下载（适配你的业务）
  Future<List<VenueGoalModel>> batchDownload(
    List<VenueGoalModel> videos, {
    void Function(
      int current,
      int total,
      double p,
    )? onProgress,
  }) async {
    final results = <VenueGoalModel>[];
    int successCount = 0;
    var pathBackage = await getAppMoviesDir();
    if (!await pathBackage.exists()) await pathBackage.create(recursive: true);
    await Future.wait(videos.map((VenueGoalModel video) async {
      if (video.videoPath == "" || video.eventId == "") {
        return results.add(video);
      }

      var mp4url = path.join(pathBackage.path,
          "${UserManager.instance.user?.userId ?? ""}_${video.eventId}_${video.id}321.mp4");
      // 检查文件是否存在
      //UserManager.instance.changeFilePathInIOS(video.videoPath)

      log("batchDownload221=0");

      try {
        video.isDownloading.value = true;
        if (await File(mp4url).exists()) {
          video.localFilePath = mp4url;
          video.downloadProgress.value = 1.0;
          video.downloadStatus.value = "end";
          successCount++;
          log("getCanDownLoadVideoUrl=本地有-${mp4url}");
          onProgress?.call(successCount, videos.length, 1.0);
          //results.add(video)
          return;
        }
        log("getCanDownLoadVideoUrl=下载-${mp4url}");

        var path = await downloadVideo(
          url: video.videoPath,
          savePath: mp4url,
          onProgress: (received, total) {
            video.downloadProgress.value = total > 0 ? received / total : 0;
            var p2 = (((successCount) + video.downloadProgress.value) /
                (videos.length));
            onProgress?.call(successCount, videos.length, p2);
          },
          onFailure: (error) {
            log("getCanDownLoadVideoUrl=Failed ${video.id}: ${error}");
          },
        );

        video.localFilePath = mp4url;
        log("getCanDownLoadVideoUrl=3=${path}");
        successCount++;
      } catch (e) {
        video.localFilePath = "";
      } finally {
        video.isDownloading.value = false;
        results.add(video);
      }
    }));

    return results;
  }

  Future<Directory> getAppMoviesDir() async {
    if (Platform.isAndroid) {
      // Android专属目录（无需权限）
      final dir = await getExternalStorageDirectory();
      return Directory(path.join(dir!.path, 'Movies', 'download_videos'));
    } else {
      // iOS处理
      var dir = await getApplicationDocumentsDirectory();
      return Directory(path.join(dir.path, 'download_videos'));
    }
  }

  // 基础文件存在检查
  static Future<bool> fileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      print('检查文件存在时出错: $e');
      return false;
    }
  }
}

enum ApiErrorType {
  network,
  server,
  authentication,
  authorization,
  notFound,
  invalidData,
  timeout,
  unknown
}

class ApiError {
  final int statusCode;
  final String message;
  final ApiErrorType type;
  final dynamic details;

  ApiError({
    required this.statusCode,
    required this.message,
    required this.type,
    this.details,
  });

  // 根据状态码自动推断类型
  factory ApiError.auto({
    required int statusCode,
    String? message,
    dynamic details,
  }) {
    final type = _getTypeFromStatusCode(statusCode);
    return ApiError(
      statusCode: statusCode,
      message: message ?? _getDefaultMessage(type),
      type: type,
      details: details,
    );
  }

  static ApiErrorType _getTypeFromStatusCode(int code) {
    if (code >= 500) return ApiErrorType.server;
    switch (code) {
      case 401:
        return ApiErrorType.authentication;
      case 403:
        return ApiErrorType.authorization;
      case 404:
        return ApiErrorType.notFound;
      case 408:
        return ApiErrorType.timeout;
      case 400:
        return ApiErrorType.invalidData;
      default:
        return ApiErrorType.unknown;
    }
  }

  static String _getDefaultMessage(ApiErrorType type) {
    switch (type) {
      case ApiErrorType.network:
        return 'Network connection failed';
      case ApiErrorType.server:
        return 'Server internal error';
      case ApiErrorType.authentication:
        return 'Authentication required';
      case ApiErrorType.authorization:
        return 'Permission denied';
      case ApiErrorType.notFound:
        return 'Resource not found';
      case ApiErrorType.invalidData:
        return 'Invalid request data';
      case ApiErrorType.timeout:
        return 'Request timeout';
      default:
        return 'Unknown error occurred';
    }
  }
}
