import 'package:flutter/material.dart';

class ProgressIndicator extends StatelessWidget {
  final Stream<double> progressStream;

  const ProgressIndicator({super.key, required this.progressStream});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<double>(
      stream: progressStream,
      builder: (context, snapshot) {
        final progress = snapshot.data ?? 0;
        return Column(
          children: [
            LinearProgressIndicator(value: progress),
            Text('${(progress * 100).toStringAsFixed(1)}%'),
            if (snapshot.hasError)
              Text(snapshot.error.toString(),
                  style: TextStyle(color: Colors.red)),
          ],
        );
      },
    );
  }
}
