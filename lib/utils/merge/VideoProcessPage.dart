import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:math' as math;
import 'package:easy_video_editor/easy_video_editor.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/network/model/%20merge_video_model.dart';
import 'package:shoot_z/network/model/shot_record_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/utils/merge/SerialTaskQueue.dart';
import 'package:shoot_z/utils/merge/VideoProcessor.dart';
import 'package:path/path.dart' as path;
import 'package:ui_packages/ui_packages.dart';

class VideoMergePage extends StatefulWidget {
  @override
  _VideoMergePageState createState() => _VideoMergePageState();
}

class _VideoMergePageState extends State<VideoMergePage> {
  final List<MergeVideoModel> _finalVideos = [];
  var _progress = 0.0.obs;
  bool _isProcessing = false;
  final RxDouble _mergeProgress = 0.0.obs;
  String? _outputPath;
  var dataList = <ShotRecordModel>[].obs;
  var imgList2 = <String>[].obs;
  var index2 = 0.obs;
  var index3 = 0.obs;
  @override
  void initState() {
    super.initState();
    SerialTaskQueue.init();
    getData();
  }

  getData() async {
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final selfieShotDao = database.selfieShotDao;
    List<ShotRecordModel> filteredNumbers = await selfieShotDao.findAllShot(
        "1314", UserManager.instance.userInfo.value?.userId ?? "", "1");
    for (var item in filteredNumbers) {
      index3++;
      if ((item.filePath != "")) {
        final item1 = item.copyWith(videoLoadOK: "1", imgLoadOK: "$index3-1");
        final item2 = item.copyWith(videoLoadOK: "2", imgLoadOK: "$index3-2");
        final item3 = item.copyWith(videoLoadOK: "3", imgLoadOK: "$index3-3");
        // 严格按顺序插入
        dataList.addAll([item1, item2, item3]);
      }
    }
    log("VideoMergePage1=1331 ${filteredNumbers.length}");
    log("VideoMergePage2=${jsonEncode(dataList)}");
  }

  /// 入队（用户点击开始）
  Future<void> startAll(List<ShotRecordModel> list) async {
    if (_isProcessing) {
      Get.snackbar('警告', '已有任务进行中');
      return;
    }

    try {
      setState(() {
        _isProcessing = true;
        _progress.value = 0;
      });
      // List<String> imgl = dataList.map((element) {
      //   return element.filePath ?? "";
      // }).toList();
      // final outputPath = await _generateFinalOutputPath();
      // log("VideoMergePagemergedPath133391=${dataList.length}");
      // final editor = VideoEditorBuilder(videoPath: imgl.first ?? "")
      //   ..merge(otherVideoPaths: imgl.sublist(1))
      //   ..compress(resolution: VideoResolution.p720)
      //   ..removeAudio(); // 静音

      // log("VideoMergePagemergedPath133392=${jsonEncode(dataList)}");
      // try {
      //   await editor.export(
      //     outputPath: outputPath,
      //     onProgress: (p) => _mergeProgress.value = p,
      //   );
      // } catch (e) {
      //   await _cleanCorruptedFile(outputPath);
      //   rethrow;
      // }
      // log("VideoMergePagemergedPath133393=${outputPath}");
      // return;
      // 分阶段进度计算
      final totalVideos = list.length;

      final futures = list.map((item) async {
        try {
          final result = SerialTaskQueue.push(() => processOneVideo(
                item,
                onTotalProgress: (p) {
                  _progress.value =
                      (((_finalVideos.length) + p) / (dataList.length) * 0.9);
                  //     log('VideoMergePagemergedPath161=completedVideos${_finalVideos.length}-p=$p-totalVideos=$totalVideos total:${_progress.value}');
                },
              ));

          log('完成视频处理: ${item.filePath}');
          return result;
        } catch (e) {
          log('视频处理失败: ${item.filePath} - $e');
          return null; // 返回null而不是rethrow
        }
      }).toList();

// 等待所有任务完成（包括失败的）
      await Future.wait(futures);
      log('VideoMergePagemergedPath16');
      _outputPath = await mergeAllVideos();
      log('VideoMergePagemergedPath17 _outputPath=$_outputPath');
    } on PlatformException catch (e) {
      Get.snackbar('系统错误', e.message ?? '视频处理服务异常');
      log('VideoMergePagemergedPath181 ');
    } catch (e) {
      Get.snackbar('处理失败', e.toString());
      log('VideoMergePagemergedPath182');
    } finally {
      setState(() => _isProcessing = false);
      log('VideoMergePagemergedPath183 ');
    }
  }

  /// 处理单个视频：三段慢放 → 合并
  /// [onTotalProgress] 0~100 实时进度
  Future<String> processOneVideo(
    ShotRecordModel item, {
    required Function(double) onTotalProgress,
  }) async {
    index2++;
    if (item.filePath == null) throw Exception('视频路径为空');
    final tempDir = (await getTemporaryDirectory()).path;
    final results = <String>[];
    // 1. 取元数据
    final editor = VideoEditorBuilder(videoPath: item.filePath!);
    final meta = await editor.getVideoMetadata();
    final duration = meta.duration;

    log("VideoMergePagemergedPath10  index2=$index2 item=${item.videoLoadOK}");
    if (duration > 8000) {
      if (item.videoLoadOK == "1") {
        final outFile =
            generateNewFilePath(tempDir, item.filePath!, suffix: 'seg_1');
        await editor
            .trim(startTimeMs: 0, endTimeMs: duration - 5000)
            .compress(resolution: VideoResolution.p720) // 降分辨率提速
            .removeAudio() // 静音
            .export(
              outputPath: outFile,
              onProgress: (progress) {
                onTotalProgress(progress);
              },
            );

        if (outFile.isNotEmpty) results.add(outFile);
        log("VideoMergePagemergedPath11 i=1-${results.length}");
      } else if (item.videoLoadOK == "2") {
        final outFile =
            generateNewFilePath(tempDir, item.filePath!, suffix: 'seg_2');
        await editor
            .trim(startTimeMs: duration - 5000, endTimeMs: duration - 3000)
            .compress(resolution: VideoResolution.p720) // 降分辨率提速
            .speed(speed: 0.5)
            .removeAudio() // 静音
            .export(
              outputPath: outFile,
              onProgress: (progress) {
                onTotalProgress(progress);
              },
            );

        if (outFile.isNotEmpty) results.add(outFile);
        log("VideoMergePagemergedPath11 i=2-${results.length}");
      } else if (item.videoLoadOK == "3") {
        final outFile =
            generateNewFilePath(tempDir, item.filePath!, suffix: 'seg_3');
        await editor
            .trim(startTimeMs: duration - 3000, endTimeMs: duration)
            .compress(resolution: VideoResolution.p720) // 降分辨率提速
            .removeAudio() // 静音
            .export(
              outputPath: outFile,
              onProgress: (progress) {
                onTotalProgress(progress);
              },
            );

        if (outFile.isNotEmpty) results.add(outFile);
        log("VideoMergePagemergedPath11 i=3-${results.length}");
      }
    } else {
      onTotalProgress(100);
      if (item.videoLoadOK == "3") {
        results.add(item.filePath!);
      } else {
        MergeVideoModel mergeVideoModel = MergeVideoModel();
        mergeVideoModel.filePath = "";
        mergeVideoModel.mergePath = "";
        mergeVideoModel.index = item.videoLoadOK;
        _finalVideos.add(mergeVideoModel);
      }
    }

    log("VideoMergePagemergedPath121");

    for (final path in results) {
      if (!await File(path).exists()) {
        //  throw Exception('视频文件不存在: $path');
        log("VideoMergePagemergedPath124=${path}");
        MergeVideoModel mergeVideoModel = MergeVideoModel();
        mergeVideoModel.filePath = "";
        mergeVideoModel.mergePath = "";
        mergeVideoModel.index = item.videoLoadOK;
        _finalVideos.add(mergeVideoModel);
      } else {
        log("VideoMergePagemergedPath125=${path}");
        MergeVideoModel mergeVideoModel = MergeVideoModel();
        mergeVideoModel.filePath = item.filePath;
        mergeVideoModel.mergePath = path;
        mergeVideoModel.index = item.videoLoadOK;
        mergeVideoModel.eventId = item.eventId;

        _finalVideos.add(mergeVideoModel);
      }
    }
//VideoMergePagemergedPath125=/data/user/0/com.shootZ.app.shoot_z/final_merge_1757422012563_seg_2281.mp4

    log('VideoMergePagemergedPath1333 _outputPath=${_finalVideos.length}-${dataList.length}');

    if (dataList.length == _finalVideos.length) {
      log('VideoMergePagemergedPath1333 _finalVideos=${jsonEncode(_finalVideos)}');
      try {
        var _outputPath = await mergeAllVideos();
        log('VideoMergePagemergedPath13330 _outputPath=$_outputPath');
      } catch (e) {
        print(e);
        log('VideoMergePagemergedPath13331 _outputPath=$e');
      }
    }
// [log] VideoMergePagemergedPath125=/data/user/0/com.shootZ.app.shoot_z/final_merge_1757422012563_seg_1433.mp4
// [log] VideoMergePagemergedPath125=/data/user/0/com.shootZ.app.shoot_z/final_merge_1757422012563_seg_2094.mp4
// [log] VideoMergePagemergedPath125=/data/user/0/com.shootZ.app.shoot_z/final_merge_1757422012563_seg_3635.mp4

    return "";
  }

  Future<String?> mergeAllVideos() async {
    if (_finalVideos.isEmpty) return null;
    if (_finalVideos.length == 1) return _finalVideos.first.filePath;

    try {
      final outputPath = await _generateFinalOutputPath();
      await _executeFinalMerge(outputPath);
      return outputPath;
    } catch (e) {
      Get.snackbar('合并失败', e.toString());
      return null;
    }
  }

  (double, double) _calculateSegmentTime(int index, double totalDuration) {
    const minDuration = 1000.0; // 每个分段至少1秒

    return switch (index) {
      0 => (
          0,
          math.max(minDuration, totalDuration - 4000) // 第一段至少1秒
        ),
      1 => (
          math.max(0, totalDuration - 4000),
          math.max(minDuration, totalDuration - 3000)
        ),
      2 => (math.max(0, totalDuration - 3000), totalDuration),
      _ => throw ArgumentError('Invalid segment index')
    };
  }

  Future<void> _cleanCorruptedFile(String path) async {
    try {
      if (await File(path).exists()) {
        await File(path).delete();
      }
    } catch (e) {
      debugPrint('清理失败: $path - $e');
    }
  }

  Future<String> _generateFinalOutputPath() async {
    Directory dir2;
    if (Platform.isAndroid) {
      // Android专属目录（无需权限）
      final dir = await getExternalStorageDirectory();
      dir2 = Directory(path.join(dir!.path, 'Movies', 'merge_videos'));
    } else {
      // iOS处理
      var dir = await getApplicationDocumentsDirectory();
      dir2 = Directory(path.join(dir.path, 'videos'));
    }
    if (!await dir2.exists()) await dir2.create(recursive: true);
    return path.join(
        dir2.path, 'final_merge_${DateTime.now().millisecondsSinceEpoch}.mp4');
  }

  bool containsEventId(List<MergeVideoModel> videos, String eventId) {
    return videos.any((video) => video.eventId == eventId);
  }

  Future<String?> _executeFinalMerge(String outputPath) async {
    List<String> pathlist = [];
    List<MergeVideoModel> finalVideosList1 = []; //合成的列表
    List<MergeVideoModel> finalVideosList2 = []; //未合成的列表
    // 检查所有分段是否存在
    for (final path in _finalVideos) {
      if (!await File(path.mergePath ?? "").exists()) {
        //  print('文件不存在，需要重新生成');
        if (!await File(path.filePath ?? "").exists()) {
          //  print('文件不存在，需要重新生成');
        } else {
          if (!containsEventId(finalVideosList1, path.eventId ?? "")) {
            pathlist.add(path.filePath ?? "");
            finalVideosList2.add(path);
          }
        }
      } else {
        if (!containsEventId(finalVideosList2, path.eventId ?? "")) {
          pathlist.add(path.mergePath ?? "");
          finalVideosList1.add(path);
        }
      }
    }
    log("VideoMergePagemergedPath13339=${pathlist.length}");
    final editor = VideoEditorBuilder(videoPath: pathlist.first)
      ..merge(otherVideoPaths: pathlist.sublist(1))
      ..compress(resolution: VideoResolution.p720)
      ..removeAudio(); // 静音

    log("VideoMergePagemergedPath13339=${jsonEncode(pathlist)}");
    try {
      return await editor.export(
        outputPath: outputPath,
        onProgress: (p) => _mergeProgress.value = p,
      );
    } catch (e) {
      await _cleanCorruptedFile(outputPath);
      rethrow;
    }
  }

  String generateNewFilePath(String temporaryFilePath, String originalPath,
      {String? suffix}) {
    // 获取目录和文件名信息
    final dir = path.dirname(temporaryFilePath);
    final filename = path.basenameWithoutExtension(originalPath);
    final ext = path.extension(originalPath);
    // 生成新文件名（添加时间戳或自定义后缀）
    final newFilename =
        '${filename}_${suffix ?? ""}${DateTime.now().millisecondsSinceEpoch.toString().substring(10)}$ext';

    // 组合新路径
    return path.join(dir, newFilename);
  }

  Future<void> _cancelProcessing() async {
    try {
      await VideoProcessor.cancel();
      setState(() => _isProcessing = false);
      Get.snackbar('已取消', '操作已终止');
    } catch (e) {
      Get.snackbar('错误', '取消失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('视频合成')),
      body: Center(child: Obx(() {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            LinearProgressIndicator(value: _progress.value),
            Text(
              '${(_progress.value * 100).toStringAsFixed(1)}%',
              style: TextStyle(color: Colours.white),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _cancelProcessing,
              child: const Text('取消'),
              style: ElevatedButton.styleFrom(iconColor: Colors.red),
            ),
            ElevatedButton(
              onPressed: () {
                startAll(dataList);
              },
              child: const Text('开始合成'),
            ),
            if (_outputPath != null) Text(_outputPath!),
          ],
        );
      })),
    );
  }

  @override
  void dispose() {
    super.dispose();
    SerialTaskQueue.dispose();
  }
}
