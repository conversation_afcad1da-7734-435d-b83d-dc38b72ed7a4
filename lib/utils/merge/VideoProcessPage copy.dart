import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:easy_video_editor/easy_video_editor.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/network/model/shot_record_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/utils/merge/SerialTaskQueue.dart';
import 'package:shoot_z/utils/merge/VideoProcessor.dart';
import 'package:path/path.dart' as path;

class VideoMergePage extends StatefulWidget {
  @override
  _VideoMergePageState createState() => _VideoMergePageState();
}

class _VideoMergePageState extends State<VideoMergePage> {
  Stream<double>? _progressStream;
  RxDouble _progress = 0.0.obs;
  bool _isProcessing = false;
  String? _outputPath;
  var dataList = <ShotRecordModel>[].obs;
  var imgList2 = <String>[].obs;

  /// 全局串行队列
  final _queue = StreamController<ShotRecordModel>.broadcast();
  StreamSubscription? _queueSub;
  @override
  void initState() {
    super.initState();
    initVideoQueue();
    getData();
  }

  /// 初始化队列（main/initState 调一次）
  void initVideoQueue() {
    _queueSub = _queue.stream.asyncMap((item) async {
      await processOneVideo(item, onTotalProgress: (p) {
        if (mounted) setState(() => _progress.value = p);
      });
    }).listen((_) {});
  }

  getData() async {
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final selfieShotDao = database.selfieShotDao;
    List<ShotRecordModel> filteredNumbers = await selfieShotDao.findAllShot(
        "1463", UserManager.instance.userInfo.value?.userId ?? "", "1");
    dataList.assignAll(filteredNumbers);
    log("VideoMergePage1=1331 ${filteredNumbers.length}");
    log("VideoMergePage2=${jsonEncode(filteredNumbers)}");
  }

  /// 入队（用户点击开始）
  void startAll(List<ShotRecordModel> list) async {
    for (final item in list) {
      SerialTaskQueue.push(
        () => processOneVideo(item, onTotalProgress: (p) {
          _progress.value = p; // 实时刷新 UI
        }),
      );
    }
  }

  /// 处理单个视频：三段慢放 → 合并
  /// [onTotalProgress] 0~100 实时进度
  Future<String> processOneVideo(
    ShotRecordModel item, {
    required Function(double) onTotalProgress,
  }) async {
    if (item.filePath == null) throw Exception('视频路径为空');

    final tempDir = (await getTemporaryDirectory()).path;
    final results = <String>[];

    // 1. 取元数据
    final editor = VideoEditorBuilder(videoPath: item.filePath!);
    final meta = await editor.getVideoMetadata();
    final duration = meta.duration;

    // 2. 总段数 & 进度转换
    final totalSegments = duration > 8000 ? 3 : 1;
    double segmentToTotal(int done, double segProg) =>
        ((done + segProg) / totalSegments * 100).clamp(0, 100);

    // 3. 分段导出（串行）
    if (duration > 8000) {
      for (int i = 0; i < 3; i++) {
        final outFile =
            generateNewFilePath(tempDir, item.filePath!, suffix: 'seg_$i');

        // 计算起止时间
        final startMs = i == 0 ? 0 : (duration - 4000 + i * 1000).toInt();
        final endMs = i == 0
            ? duration - 4000
            : (duration - 4000 + (i + 1) * 1000).toInt();

        await editor
            .trim(startTimeMs: startMs, endTimeMs: endMs)
            .compress(resolution: VideoResolution.p720) // 降分辨率提速
            .removeAudio() // 静音
            .export(
              outputPath: outFile,
              onProgress: (p) => onTotalProgress(segmentToTotal(i, p)),
            );

        if (outFile.isNotEmpty) results.add(outFile);
      }
    } else {
      // 不足 8s 直接原文件
      results.add(item.filePath!);
      onTotalProgress(100);
    }

    // 4. 合并片段
    final mergedPath =
        generateNewFilePath(tempDir, item.filePath!, suffix: 'merged');
    await _mergeSegments(results, mergedPath,
        onProgress: (p) => onTotalProgress(100 + p * 0)); // 合并进度也折算到 100%

    return mergedPath;
  }

  Future<void> _mergeSegments(
    List<String> segments,
    String outputPath, {
    required Function(double) onProgress,
  }) async {
    final builder = VideoEditorBuilder(videoPath: segments.first)
      ..compress(resolution: VideoResolution.p720)
      ..merge(otherVideoPaths: segments.sublist(1));

    await builder.export(
      outputPath: outputPath,
      onProgress: onProgress,
    );
  }

  Future<void> startProcessing2() async {
    imgList2.clear();
    try {
      if (dataList.isEmpty) return;
      var time =
          "${UserManager.instance.userInfo.value?.userId}${DateTime.now().millisecondsSinceEpoch}";
      // 获取临时目录
      var temporaryFilePath1 = await getTemporaryFilePath(time);
      log("VideoMergePagezz01=${temporaryFilePath1}");

      for (var item in dataList) {
        var imgItem = await getImaglist(item, temporaryFilePath1);
        imgList2.addAll(imgItem);
      }
      if (imgList2.length < 2) {
        //TODO 无法合成
      } else {
        var outputPath = generateNewFilePath2(dataList.first.filePath ?? "");
        final editor2 = VideoEditorBuilder(videoPath: imgList2.first);
        editor2
            .compress(resolution: VideoResolution.p1080)
            .merge(otherVideoPaths: imgList2.sublist(1));

        final metadata = await editor2.getVideoMetadata();
        log('VideoMergePagezz3330=Duration: ${metadata.duration} ms');

        // Export the final video
        final outputPath2 = await editor2.export(
          outputPath: outputPath // Optional output path
          ,
          onProgress: (progress) {
            log('VideoMergePagezz3331=Creation date: ${progress}');
          },
        );
        log('VideoMergePagezz4=outputPath2: ${outputPath2} \n outputPath=${outputPath} ');
        log('VideoMergePagezz333=Duration: ${metadata.duration} ms');
        log('VideoMergePagezz333=Dimensions: ${metadata.width}x${metadata.height}');
        log('VideoMergePagezz333=Orientation: ${metadata.rotation}°');
        log('VideoMergePagezz333=File size: ${metadata.fileSize} bytes');
        log('VideoMergePagezz333=Creation date: ${metadata.date}');
      }
    } catch (e) {
      _handleError(e);
    }
  }

  Future<List<String>> getImaglist(
      ShotRecordModel item, String temporaryFilePath1) async {
    List<String> imaglist = [];
    if (item.filePath != null) {
      final editor = VideoEditorBuilder(videoPath: item.filePath ?? "");
      final metadata = await editor.getVideoMetadata();
      var duration2 = metadata.duration;
      // Trim video
      log('VideoMergePagezz1= duration2:$duration2-${duration2 > 8000}');
      if (duration2 > 8000) {
        for (int i = 0; i < 3; i++) {
          var filename = generateNewFilePath(
              temporaryFilePath1, item.filePath ?? "",
              suffix: "$i");
          if (i == 0) {
            editor
                .trim(startTimeMs: 0, endTimeMs: duration2 - 4000)
                .compress(resolution: VideoResolution.p1080)
                .removeAudio();
            final outputPath2 = await editor.export(
              outputPath: filename,
              // onProgress: (progress) {
              //   //   log('VideoMergePage3331=Creation date:$filename   ${progress}');
              // },
            );
            if (!(outputPath2 == null || outputPath2 == "")) {
              imaglist.add(outputPath2);
            }
          } else if (i == 1) {
            editor
                .trim(
                    startTimeMs: duration2 - 4000, endTimeMs: duration2 - 3000)
                .compress(resolution: VideoResolution.p1080)
                .removeAudio();
            final outputPath2 = await editor.export(
              outputPath: filename,
            );
            if (!(outputPath2 == null || outputPath2 == "")) {
              imaglist.add(outputPath2);
            }
          } else if (i == 2) {
            editor
                .trim(startTimeMs: duration2 - 3000, endTimeMs: duration2)
                .compress(resolution: VideoResolution.p1080)
                .removeAudio();
            final outputPath2 = await editor.export(
              outputPath: filename,
            );
            if (!(outputPath2 == null || outputPath2 == "")) {
              imaglist.add(outputPath2);
            }
          }
        }
      } else {
        log('VideoMergePagezz23= duration2:$duration2-${item}');
        imaglist.add(item.filePath ?? "");
      }

      return imaglist;
    }
    return imaglist;
  }

  String generateNewFilePath(String temporaryFilePath, String originalPath,
      {String? suffix}) {
    // 获取目录和文件名信息
    final dir = path.dirname(temporaryFilePath);
    final filename = path.basenameWithoutExtension(originalPath);
    final ext = path.extension(originalPath);

    // 生成新文件名（添加时间戳或自定义后缀）
    final newFilename =
        '${filename}_${suffix ?? ""}${DateTime.now().millisecondsSinceEpoch.toString().substring(10)}$ext';

    // 组合新路径
    return path.join(dir, newFilename);
  }

  String generateNewFilePath2(String originalPath, {String? suffix}) {
    // 获取目录和文件名信息
    final dir = path.dirname(originalPath);
    final filename = path.basenameWithoutExtension(originalPath);
    final ext = path.extension(originalPath);

    // 生成新文件名（添加时间戳或自定义后缀）
    final newFilename =
        '${filename}_${suffix ?? DateTime.now().millisecondsSinceEpoch.toString().substring(10)}$ext';

    // 组合新路径
    return path.join(dir, newFilename);
  }

  static Future<String> getTemporaryFilePath(String dataUrl) async {
    final tempDir = await getTemporaryDirectory();
    return '${tempDir.path}/${path.basename(dataUrl)}';
  }

  Future<void> _cancelProcessing() async {
    try {
      await VideoProcessor.cancel();
      setState(() => _isProcessing = false);
      Get.snackbar('已取消', '操作已终止');
    } catch (e) {
      Get.snackbar('错误', '取消失败: $e');
    }
  }

  void _handleError(dynamic e) {
    setState(() => _isProcessing = false);
    Get.snackbar('错误', e.toString());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('视频合成')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (_isProcessing) ...[
              LinearProgressIndicator(value: _progress.value),
              Text('${(_progress.value * 100).toStringAsFixed(1)}%'),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: _cancelProcessing,
                child: const Text('取消'),
                style: ElevatedButton.styleFrom(iconColor: Colors.red),
              ),
            ] else
              ElevatedButton(
                onPressed: () {
                  startAll(dataList);
                },
                child: const Text('开始合成'),
              ),
            if (_outputPath != null) Text(_outputPath!),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _progressStream?.drain();
    _queueSub?.cancel();
    _queue.close();
    super.dispose();
  }
}
