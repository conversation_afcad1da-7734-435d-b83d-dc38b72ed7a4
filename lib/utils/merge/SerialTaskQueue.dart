import 'dart:async';
import 'dart:collection';

import 'package:async/async.dart';

enum _QueueState { idle, running, cancelling, cancelled, disposed }

class SerialTaskQueue {
  // 状态管理增强
  static _QueueState _state = _QueueState.idle;

  // 控制器和队列实例
  static late StreamController<_Task> _controller;
  static late StreamQueue<_Task> _queue;

  // 任务存储
  static final Queue<_Task> _pendingTasks = Queue();
  static final Set<String> _activeTags = {};

  /// 安全初始化（幂等操作）
  static void init() {
    //if (_state != _QueueState.disposed) return;

    _controller = StreamController<_Task>.broadcast();
    _queue = StreamQueue(_controller.stream);
    _state = _QueueState.idle;
    _consume();
  }

  /// 带状态检查的重置方法
  static void reset() {
    // if (_state == _QueueState.disposed) {
    //   _initializeComponents();
    // } else if (_state == _QueueState.cancelled) {
    //   _controller = StreamController<_Task>.broadcast();
    //   _queue = StreamQueue(_controller.stream);
    // }
    // dispose();
    init();
    _state = _QueueState.idle;
    _pendingTasks.clear();
    _activeTags.clear();
    _consume();
  }

  /// 安全入队方法
  static Future<void> push(Future<void> Function() task, {String? tag}) async {
    _assertNotDisposed();

    if (_state == _QueueState.cancelled) {
      throw StateError('队列已取消，请先调用reset()');
    }

    if (tag != null && _activeTags.contains(tag)) {
      throw StateError('同标签任务已存在: $tag');
    }

    final taskObj = _Task(task, tag);
    _pendingTasks.add(taskObj);
    _controller.add(taskObj);
    if (tag != null) _activeTags.add(tag);
  }

  /// 增强型取消
  static void cancelAll() {
    if (_state == _QueueState.cancelled) return;

    _state = _QueueState.cancelling;
    _pendingTasks.clear();
    _activeTags.clear();
    _controller.addError(CancellationException());
    _state = _QueueState.cancelled;
  }

  /// 安全释放资源
  static void dispose() {
    if (_state == _QueueState.disposed) return;

    cancelAll();
    _controller.close();
    _state = _QueueState.disposed;
  }

  // 私有方法
  static void _initializeComponents() {
    _controller = StreamController<_Task>.broadcast();
    _queue = StreamQueue(_controller.stream);
  }

  static void _assertNotDisposed() {
    if (_state == _QueueState.disposed) {
      throw StateError('队列已销毁');
    }
  }

  static Future<void> _consume() async {
    _state = _QueueState.running;

    while (await _queue.hasNext && _state != _QueueState.cancelled) {
      final task = await _queue.next;

      try {
        await task.fn();
      } catch (e) {
        _controller.addError(e);
      } finally {
        _pendingTasks.remove(task);
        if (task.tag != null) _activeTags.remove(task.tag);
      }
    }

    if (_state != _QueueState.cancelled) {
      _state = _QueueState.idle;
    }
  }
}

/// 内部任务包装（不变）
class _Task {
  final Future<void> Function() fn;
  final String? tag;
  _Task(this.fn, this.tag);
}

// 自定义取消异常
class CancellationException implements Exception {
  final String message = '队列操作已取消';
}
