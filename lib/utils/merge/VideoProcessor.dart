import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

class VideoProcessor {
  static const _methodChannel = MethodChannel('com.example.video_processor');
  static const _eventChannel =
      EventChannel('com.example.video_processor/progress');

  /// 合并视频并返回进度流
  static Stream<double> mergeVideos({
    required List<String> inputPaths,
    double slowStart = 3.0,
    double slowEnd = 4.0,
    double slowFactor = 2.0,
  }) async* {
    // 确保输出目录存在
    final outputDir = await getApplicationDocumentsDirectory();
    final outputPath =
        '${outputDir.path}/merged_${DateTime.now().millisecondsSinceEpoch}.mp4';

    try {
      // 开始处理
      _methodChannel.invokeMethod('startMerge', {
        'inputPaths': inputPaths,
        'outputPath': outputPath,
        'slowMotionStart': slowStart,
        'slowMotionEnd': slowEnd,
        'slowFactor': slowFactor,
      });

      // 监听进度流
      yield* _eventChannel
          .receiveBroadcastStream()
          .map((p) => (p as num).toDouble())
          .handleError((e) => throw Exception('进度监听失败: $e'));
    } on PlatformException catch (e) {
      throw Exception('视频处理启动失败: ${e.message}');
    }
  }

  /// 取消当前处理
  static Future<void> cancel() async {
    try {
      await _methodChannel.invokeMethod('cancel');
    } on PlatformException catch (e) {
      throw Exception('取消失败: ${e.message}');
    }
  }
}
