import 'package:flutter/material.dart';

//进度带时间预估​
class EnhancedProgressIndicator extends StatelessWidget {
  // final Stream<double> progressStream;
  final DateTime startTime = DateTime.now();

  Widget build(BuildContext context) {
    return StreamBuilder<double>(
      builder: (context, snapshot) {
        final progress = snapshot.data ?? 0;
        final elapsed = DateTime.now().difference(startTime);
        final remaining = progress > 0
            ? Duration(
                seconds:
                    (elapsed.inSeconds / progress * (1 - progress)).toInt())
            : null;

        return Column(
          children: [
            LinearProgressIndicator(value: progress),
            Text('${(progress * 100).toStringAsFixed(1)}%'),
            if (remaining != null)
              Text('剩余: ${remaining.inMinutes}分${remaining.inSeconds % 60}秒'),
          ],
        );
      },
      stream: null,
    );
  }
}
