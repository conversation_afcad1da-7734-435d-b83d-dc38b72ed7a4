// ignore_for_file: avoid_print, unnecessary_brace_in_string_interps

import 'dart:developer';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:mime/mime.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:utils_package/utils_package.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../generated/l10n.dart';
import '../widgets/permission_dialog.dart';

void onKeyDismiss() {
  FocusManager.instance.primaryFocus?.unfocus();
}

class Utils {
  static Future<void> delDir(FileSystemEntity file) async {
    final storage = await WxPermissionUtils.storage();
    if (!storage) {
      return;
    }

    if (file is Directory && file.existsSync()) {
      if (kDebugMode) {
        print(file.path);
      }
      final List<FileSystemEntity> children =
          file.listSync(recursive: true, followLinks: true);
      for (final FileSystemEntity child in children) {
        await delDir(child);
      }
    }
    try {
      if (file.existsSync()) {
        await file.delete(recursive: true);
      }
    } catch (err) {
      print(err);
    }
  }

  static const MethodChannel _channel = const MethodChannel('performance_info');

  /// 获取当前渠道名称
  static Future<String> getChannel() async {
    try {
      final String? channel = await _channel.invokeMethod('getAppChannel');
      return channel ?? 'unknown';
    } on PlatformException catch (e) {
      print("Failed to get app channel: '${e.message}'.");
      return 'unknown';
    }
  }

//循环获取缓存大小
  static Future<double> getTotalSizeOfFilesInDir(
      final FileSystemEntity file) async {
//  File

    if (file is File && file.existsSync()) {
      int length = await file.length();
      return double.parse(length.toString());
    }
    if (file is Directory && file.existsSync()) {
      List children = file.listSync();
      double total = 0;
      if (children.isNotEmpty) {
        for (final FileSystemEntity child in children) {
          total += await getTotalSizeOfFilesInDir(child);
        }
      }
      return total;
    }
    return 0;
  }

//格式化文件大小
  static String renderSize(double value) {
    if (value == 0) {
      return '';
    }
    List<String> unitArr = []
      ..add('B')
      ..add('K')
      ..add('M')
      ..add('G');
    int index = 0;
    while (value > 1024) {
      index++;
      value = value / 1024;
    }
    String size = value.toStringAsFixed(2);
    return size + unitArr[index];
  }

  static bool isNumeric(String input) {
    final numericRegex = RegExp(r'^\d+$');
    return numericRegex.hasMatch(input);
  }

  static bool isValidPhoneNumber(String input) {
    final RegExp regex = RegExp(r'^\d{11}$');
    return regex.hasMatch(input);
  }

  static bool isValidIpPort(String input) {
    // 定义正则表达式
    final regex = RegExp(
      r'^((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9]):([0-9]{1,5})$',
    );

    // 使用正则表达式校验
    if (!regex.hasMatch(input)) {
      return false;
    }

    // 检查端口范围（0-65535）
    final port = int.tryParse(input.split(':')[1]);
    if (port == null || port < 0 || port > 65535) {
      return false;
    }

    return true;
  }

  // /// 调起拨号页
  static void phoneTelURL(String phone) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phone.trim(),
    );
    await launchUrl(launchUri);
    // if (await canLaunchUrl(launchUri)) {
    //   await launchUrl(launchUri);
    // } else {
    //   WxLoading.showToast('拨号失败！');
    // }
  }

  static String formatDateClean(DateTime date) {
    // return '${date.month}月${date.day}日' // 中文格式：7月14日
    //   .replaceAll('0', '')              // 去掉所有零
    //   .replaceAll('月', '/')            // 替换为斜杠格式
    //   .replaceAll('日', '');

    // 或者直接：
    return '${date.month}/${date.day}'.replaceAllMapped(RegExp(r'^0|/0'),
        (match) => match.group(0)!.startsWith('0') ? '' : '/');
  }

// 示例输出：
// "07/14" → "7/14"
// "10/05" → "10/5"
// "12/10" → "12/10" (保持不变)

  static Future<String> getTemporaryFilePath(String dataUrl) async {
    final tempDir = await getTemporaryDirectory();
    return '${tempDir.path}/${path.basename(dataUrl)}';
  }

  static Future<void> downloadAndSaveToPhotoAlbum(String dataUrl) async {
    print("时间快点过十几块绿豆糕上看见的后果$dataUrl");
    // 请求权限
    bool permission = false;
    if (Platform.isIOS) {
      permission = await WxPermissionUtils.addPhoto();
    } else {
      permission = await WxPermissionUtils.storage();
    }
    if (!permission) {
      if (Platform.isIOS) {
        showDownloadDialog();
      } else {
        WxLoading.showToast(S.current.no_storage_permission);
      }
      return;
    }
    var time =
        "${UserManager.instance.userInfo.value?.userId}${DateTime.now().millisecondsSinceEpoch}";
    // 获取临时目录
    var filePath = await getTemporaryFilePath(time);
    try {
      WxLoading.show(status: S.current.downloading);
      // 下载视频
      final res = await Api().doDownload(dataUrl, filePath, failure: (error) {
        WxLoading.showToast(error.message);
      });
      if (res == null) {
        return;
      }

      // 保存到相册
      // 读取文件的头部字节
      final file = File(filePath);
      final headerBytes = await file.readAsBytes();
      final mimeType =
          lookupMimeType(filePath, headerBytes: headerBytes)?.split('/').last ??
              'mp4';
      String newPath = path.setExtension(filePath, '.$mimeType');
      // 重命名文件
      final newFile = await file.rename(newPath);
      filePath = newFile.path;
      dynamic result = await ImageGallerySaverPlus.saveFile(filePath,
          isReturnPathOfIOS: true);
      WxLoading.dismiss();
      // log("filePath22=$filePath");
      if (result["isSuccess"] == true) {
        WxLoading.showToast(S.current.save_successfully);
      } else {
        WxLoading.showToast(S.current.save_failed);
      }
    } catch (e) {
      print("Error downloading or saving video: $e");
      WxLoading.dismiss();
      WxLoading.showToast(S.current.save_failed);
    } finally {
      // 删除临时文件
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        print('文件已成功删除');
      } else {
        print('文件不存在，无需删除');
      }
    }
  }
  //'${UserManager.instance.documentPathStr}/download_1756976916.6616511.mp4'

  static Future<void> localDownloadAndSaveToPhotoAlbum(
      List<ShotRecordModel> dataList) async {
    // 请求权限
    bool permission = false;
    if (Platform.isIOS) {
      permission = await WxPermissionUtils.addPhoto();
    } else {
      permission = await WxPermissionUtils.storage();
    }
    if (!permission) {
      if (Platform.isIOS) {
        showDownloadDialog();
      } else {
        WxLoading.showToast(S.current.no_storage_permission);
      }
      return;
    }

    // 获取临时目录
    var uploadSuccessfulList = [];
    var uploadFailedList = [];
    try {
      WxLoading.show(status: S.current.downloading);
      for (int i = 0; i < dataList.length; i++) {
        // 检查文件是否存在
        // var isHava = await fileExists(UserManager.instance
        //     .changeFilePathInIOS(dataList[i].filePath ?? ""));
        var isHava = await fileExists(await UserManager.instance
            .appendLocalPath(dataList[i].filePath ?? ""));

        log("uploadVideo3=${isHava}");
        if (isHava) {
          // 保存视频
          // final result = await ImageGallerySaverPlus.saveFile(UserManager
          //     .instance
          //     .changeFilePathInIOS(dataList[i].filePath ?? ""));
          final result = await ImageGallerySaverPlus.saveFile(await UserManager
              .instance
              .appendLocalPath(dataList[i].filePath ?? ""));
          if (dataList.length <= 1) {
            if (result["isSuccess"] == true) {
              WxLoading.showToast(S.current.save_successfully); //，名称${fileName}
            } else {
              WxLoading.showToast(S.current.save_failed);
            }
          } else {
            if (result["isSuccess"] == true) {
              uploadSuccessfulList.add(i);
              // WxLoading.showToast(
              //     "${S.current.save_successfully}（${i + 1}/${dataList.length}）"); //，名称${fileName}
            } else {
              uploadFailedList.add(i);
              // WxLoading.showToast(
              //     "${S.current.save_failed}（${i + 1}/${dataList.length}）");
            }
          }
        } else {
          WxLoading.showToast("文件不存在（${i}/${dataList.length}）");
        }
      }
      if (dataList.length > 1) {
        var sss = uploadSuccessfulList.join(",");
        var sss2 = uploadFailedList.join(",");
        if (uploadSuccessfulList.isEmpty) {
          WxLoading.showToast(S.current.save_failed); //，名称${fileName}
        } else if (uploadFailedList.isEmpty) {
          WxLoading.showToast(S.current.save_successfully); //，名称${fileName}
        } else {
          WxLoading.showToast(
              "${S.current.save_successfully}:$sss \n${S.current.save_failed}:$sss2"); //，名称${fileName}
        }
      }
    } catch (e) {
      print("Error downloading or saving video: $e");
      WxLoading.dismiss();
      WxLoading.showToast(S.current.save_failed);
    } finally {
      // 删除临时文件
      // final file = File(filePath);
      // if (await file.exists()) {
      //   await file.delete();
      //   print('文件已成功删除');
      // } else {
      //   print('文件不存在，无需删除');
      // }
    }
  }

  static Future<void> localDownloadAndSaveToSinglePhotoAlbum(
      String filePath) async {
    if (filePath == "") {
      WxLoading.showToast("该视频地址不存在");
      return;
    }
    // 请求权限
    bool permission = false;
    if (Platform.isIOS) {
      permission = await WxPermissionUtils.addPhoto();
    } else {
      permission = await WxPermissionUtils.storage();
    }
    if (!permission) {
      if (Platform.isIOS) {
        showDownloadDialog();
      } else {
        WxLoading.showToast(S.current.no_storage_permission);
      }
      return;
    }

    try {
      WxLoading.show(status: S.current.downloading);
      // 检查文件是否存在
      var isHava =
          await fileExists(UserManager.instance.changeFilePathInIOS(filePath));
      log("uploadVideo3=${isHava}");

      if (isHava) {
        // 保存视频
        final result = await ImageGallerySaverPlus.saveFile(
            UserManager.instance.changeFilePathInIOS(filePath));
        if (result["isSuccess"] == true) {
          WxLoading.showToast(S.current.save_successfully); //，名称${fileName}
        } else {
          WxLoading.showToast(S.current.save_failed);
        }
      } else {
        WxLoading.showToast("文件不存在（${filePath}）");
      }
      WxLoading.dismiss();
    } catch (e) {
      print("Error downloading or saving video: $e");
      WxLoading.dismiss();
      WxLoading.showToast(S.current.save_failed);
    } finally {
      // 删除临时文件
      // final file = File(filePath);
      // if (await file.exists()) {
      //   await file.delete();
      //   print('文件已成功删除');
      // } else {
      //   print('文件不存在，无需删除');
      // }
    }
  }

// 读取文件夹下的所有文件和子文件夹
  static Future<List<FileSystemEntity>> listDirectory(String path) async {
    try {
      final directory = Directory(path);
      return directory.list().toList();
    } catch (e) {
      print('读取目录失败: $e');
      return [];
    }
  }

// 仅获取文件（不包括文件夹）
  static Future<List<File>> listFiles(String path) async {
    final entities = await listDirectory(path);
    return entities.whereType<File>().toList();
  }

// 仅获取文件夹
  Future<List<Directory>> listSubdirectories(String path) async {
    final entities = await listDirectory(path);
    return entities.whereType<Directory>().toList();
  }

// 基础文件存在检查
  static Future<bool> fileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      print('检查文件存在时出错: $e');
      return false;
    }
  }

  // 判断两个日期相差是否小于8天
  static bool areDatesWithinEightDays(DateTime date1, DateTime date2) {
    // 归一化日期，忽略时间部分
    date1 = normalizeDate(date1);
    date2 = normalizeDate(date2);

    // 计算两个日期之间的差异
    Duration difference = date1.difference(date2).abs();
    //log("getDaoSql41=${difference.inDays}=${date1.toLocal()}=${date2.toLocal()}");
    // 检查差异是否小于8天
    return difference.inDays < 8; //8
  }

  static DateTime normalizeDate(DateTime dateTime) {
    return DateTime(dateTime.year, dateTime.month, dateTime.day);
  }

  static String formatToPercentage(double value) {
    // 将小数乘以 100，变为百分比形式
    double percentage = value * 100;

    // 判断小数部分是否为 0
    if (percentage % 1 == 0) {
      // 如果小数部分为 0，只保留整数部分
      return "${percentage.toInt()}%";
    } else {
      // 如果小数部分不为 0，保留 1 位小数
      return "${percentage.toStringAsFixed(1)}%";
    }
  }

  static Future<File> assetToFile(String assetPath, String fileName) async {
    // 1. 从 asset 中读取字节数组
    final ByteData data = await rootBundle.load(assetPath);
    final Uint8List bytes = data.buffer.asUint8List();

    // 2. 获取应用文档目录（或临时目录）
    final Directory tempDir = await getApplicationDocumentsDirectory();
    final File file = File('${tempDir.path}/$fileName');

    // 3. 将字节写入文件
    await file.writeAsBytes(bytes);

    return file; // 返回真正的 File 对象
  }

  static bool isToLogin() {
    if (UserManager.instance.isLogin) {
      log("getisLogin1=${UserManager.instance.isLogin}");
      return true;
    } else {
      AppPage.to(Routes.login).then((onValue) async {
        await Future.delayed(const Duration(milliseconds: 500));
        log("getisLogin12=$onValue-${UserManager.instance.isLogin}");
        if (UserManager.instance.isLogin) {
          return true;
        } else {
          log("getisLogin13=$onValue-${UserManager.instance.isLogin}");
          WxLoading.showToast(S.current.please_login);
        }
      });
    }

    return false;
  }

  static bool isPhoneNumber(String input) {
    // 定义一个正则表达式，用于匹配中国大陆的手机号码
    final phoneRegex = RegExp(r'^1[1-9]\d{9}$');

    // 使用正则表达式的hasMatch方法判断输入字符串是否匹配
    return phoneRegex.hasMatch(input);
  }

  static String formatDaysAndHms(int milliseconds) {
    final duration = Duration(seconds: milliseconds);
    var list = <int>[];
    if (duration.inHours > 0) {
      list.add(duration.inHours);
      list.add(duration.inMinutes.remainder(60));
      list.add(duration.inSeconds.remainder(60));
    } else {
      list.add(duration.inMinutes.remainder(60));
      list.add(duration.inSeconds.remainder(60));
    }
    return list.map((part) => part.toString().padLeft(2, '0')).join(':');
  }

  //截取字符串倒数第四位到倒数第八位的部分
  static String extractString(String input) {
    if (input.length < 8) {
      return "";
    }
    return input.substring(input.length - 8, input.length - 3);
  }
}
