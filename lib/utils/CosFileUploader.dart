import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:shoot_z/network/model/tencent_cos_model.dart';
import 'package:tencentcloud_cos_sdk_plugin/cos.dart';
import 'package:tencentcloud_cos_sdk_plugin/cos_transfer_manger.dart';
import 'package:tencentcloud_cos_sdk_plugin/pigeon.dart' as pigeon;
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

class CosFileUploader {
  // 配置常量
  static const int _maxRetryCount = 2;
  static const int _initialRetryDelayMs = 1000;
  static const int _maxRetryDelayMs = 8000;
  static const int _sliceSize = 1048576; // 1MB
  static const int _divisionSize = 2097152; // 2MB

  // 状态管理
  final ValueNotifier<double> progressNotifier = ValueNotifier(0);
  final ValueNotifier<bool> isUploading = ValueNotifier(false);
  final Map<String, FileUploadStatus> _statusMap = {};
  CosTransferManger? _transferManager;

  /// 执行文件上传（自动获取最新票据）
  /// 执行文件上传（自动获取最新票据）
  Future<void> uploadFiles({
    required List<String> filePaths,
    required String remotePrefix,
    required Function(bool isAllSuccess, Map<String, String> results)
        onComplete,
  }) async {
    isUploading.value = true;
    progressNotifier.value = 0;
    _resetStatus(filePaths);

    try {
      // 每次上传获取新票据
      final credentials = await _fetchCredentials();
      await _initializeWithCredentials(credentials);

      await _executeParallelUploads(
        filePaths: filePaths,
        bucketName: credentials.bucketName ?? 'default-bucket',
        remotePrefix: remotePrefix,
      );

      // 收集上传结果
      final results = <String, String>{};
      for (final entry in _statusMap.entries) {
        if (entry.value.status == UploadStatus.success) {
          results[entry.key] = '上传成功';
        } else {
          results[entry.key] = '上传失败: ${entry.value.lastError}';
        }
      }

      // 执行完成回调
      final isAllSuccess = _statusMap.values
          .every((status) => status.status == UploadStatus.success);
      onComplete(isAllSuccess, results);
    } catch (e) {
      debugPrint('上传失败: $e');
      // 失败时也触发回调
      onComplete(false, {'error': e.toString()});
      rethrow;
    } finally {
      isUploading.value = false;
      _showCompletionDialog();
    }
  }

  Future<void> _executeParallelUploads({
    required List<String> filePaths,
    required String bucketName,
    required String remotePrefix,
  }) async {
    final futures = filePaths.map((path) => _uploadWithRetry(
          filePath: path,
          bucketName: bucketName,
          remotePrefix: remotePrefix,
        ));

    await Future.wait(futures);
  }

  Future<void> _uploadWithRetry({
    required String filePath,
    required String bucketName,
    required String remotePrefix,
  }) async {
    if (_isAlreadyUploaded(filePath)) return;

    final status = _getOrCreateStatus(filePath);
    int attempt = 0;
    final random = Random();

    while (attempt <= _maxRetryCount) {
      attempt++;
      status
        ..retryCount = attempt
        ..status = UploadStatus.uploading
        ..lastError = '';
      _updateProgress();

      try {
        if (!await _validateFile(filePath)) {
          throw Exception('文件无效或不存在');
        }

        final remoteKey = _generateRemoteKey(filePath, remotePrefix);
        await _executeSingleUpload(
          filePath: filePath,
          bucketName: bucketName,
          remoteKey: remoteKey,
        );

        status
          ..status = UploadStatus.success
          ..retryCount = 0;
        return;
      } catch (e) {
        status.lastError = e.toString();
        if (attempt >= _maxRetryCount) {
          status.status = UploadStatus.failed;
          continue;
        }
        await Future.delayed(_calcRetryDelay(attempt, random));
      } finally {
        _updateProgress();
      }
    }
  }

  Future<void> _executeSingleUpload({
    required String filePath,
    required String bucketName,
    required String remoteKey,
  }) async {
    final completer = Completer<pigeon.CosXmlResult>();

    await _transferManager!.upload(
      bucketName,
      remoteKey,
      filePath: filePath,
      resultListener: ResultListener(
        (headers, result) => completer.complete(result),
        (clientEx, serviceEx) => completer.completeError(
          clientEx ?? serviceEx ?? Exception('上传失败'),
        ),
      ),
    );

    await completer.future;
  }

  // 初始化COS服务（每次使用新票据）
  Future<void> _initializeWithCredentials(TencentCosModel credentials) async {
    try {
      await Cos().forceInvalidationCredential();

      final serviceConfig = pigeon.CosXmlServiceConfig(
        region: credentials.region ?? 'ap-guangzhou',
        isDebuggable: kDebugMode,
        isHttps: true,
      );

      Cos().registerDefaultService(serviceConfig);

      final transferConfig = pigeon.TransferConfig(
        forceSimpleUpload: false,
        enableVerification: true,
        divisionForUpload: _divisionSize,
        sliceSizeForUpload: _sliceSize,
      );

      await Cos().registerDefaultTransferManger(serviceConfig, transferConfig);
      _transferManager = Cos().getDefaultTransferManger();
    } catch (e) {
      throw Exception('COS初始化失败: $e');
    }
  }

  // 获取临时凭证（完全按照您提供的实现）
  Future<TencentCosModel> _fetchCredentials() async {
    final client = HttpClient();
    try {
      final request = await client
          .getUrl(Uri.parse('https://i.shootz.tech/mgr-api/common/sts-data'));
      final response = await request.close();

      if (response.statusCode != 200) {
        throw Exception('凭证获取失败: ${response.statusCode}');
      }

      final json = await response.transform(utf8.decoder).join();
      return TencentCosModel.fromJson(jsonDecode(json));
    } finally {
      client.close();
    }
  }

  // 辅助方法
  bool _isAlreadyUploaded(String path) =>
      _statusMap[path]?.status == UploadStatus.success;

  FileUploadStatus _getOrCreateStatus(String path) =>
      _statusMap.putIfAbsent(path, () => FileUploadStatus(path: path));

  Future<bool> _validateFile(String path) async {
    try {
      final file = File(path);
      return await file.exists() && await file.length() > 0;
    } catch (e) {
      return false;
    }
  }

  String _generateRemoteKey(String path, String prefix) {
    final ext = path.substring(path.lastIndexOf('.'));
    return '$prefix${DateTime.now().millisecondsSinceEpoch}$ext';
  }

  Duration _calcRetryDelay(int attempt, Random random) {
    final baseDelay = _initialRetryDelayMs * pow(2, attempt - 1);
    final jitter = random.nextInt(500);
    final delayMs = min(baseDelay + jitter, _maxRetryDelayMs);
    return Duration(milliseconds: delayMs.toInt());
  }

  void _updateProgress() {
    final total = _statusMap.length;
    final completed =
        _statusMap.values.where((s) => s.status == UploadStatus.success).length;
    progressNotifier.value = total > 0 ? completed / total : 0;
  }

  void _resetStatus(List<String> paths) {
    _statusMap.clear();
    for (final path in paths) {
      _statusMap[path] = FileUploadStatus(path: path);
    }
  }

  void _showCompletionDialog() {
    final isAllSuccess =
        _statusMap.values.every((s) => s.status == UploadStatus.success);

    Get.dialog(
      AlertDialog(
        title: Text(isAllSuccess ? '上传成功' : '上传完成'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(isAllSuccess ? '所有文件已成功上传' : '部分文件上传失败'),
            const SizedBox(height: 16),
            ..._statusMap.values.map((status) => ListTile(
                  leading: Icon(
                    status.status == UploadStatus.success
                        ? Icons.check_circle
                        : Icons.error,
                    color: status.status == UploadStatus.success
                        ? Colors.green
                        : Colors.red,
                  ),
                  title: Text(File(status.path).uri.pathSegments.last),
                  subtitle: status.status == UploadStatus.failed
                      ? Text('失败: ${status.lastError}')
                      : null,
                )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: Get.back,
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}

// // 数据模型
// class TencentCosModel {
//   final String? region;
//   final String? bucketName;
//   final String? secretId;
//   final String? secretKey;
//   final String? sessionToken;
//   final int? expiredTime;

//   TencentCosModel({
//     this.region,
//     this.bucketName,
//     this.secretId,
//     this.secretKey,
//     this.sessionToken,
//     this.expiredTime,
//   });

//   factory TencentCosModel.fromJson(Map<String, dynamic> json) {
//     return TencentCosModel(
//       region: json['region'],
//       bucketName: json['bucketName'],
//       secretId: json['secretId'],
//       secretKey: json['secretKey'],
//       sessionToken: json['sessionToken'],
//       expiredTime: json['expiredTime'],
//     );
//   }
// }

enum UploadStatus { pending, uploading, success, failed }

class FileUploadStatus {
  final String path;
  UploadStatus status;
  int retryCount;
  String lastError;

  FileUploadStatus({
    required this.path,
    this.status = UploadStatus.pending,
    this.retryCount = 0,
    this.lastError = '',
  });
}
