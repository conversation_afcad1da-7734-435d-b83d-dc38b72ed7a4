class SimpleBluetoothService {
  // 检查蓝牙状态
  // static Future<Future<BluetoothAdapterState>> getBluetoothState() async {
  //   return FlutterBluePlus.adapterState.first;
  // }

  // 开启蓝牙
  static Future<void> enableBluetooth() async {
    // try {
    //   await FlutterBluePlus.turnOn();
    // } catch (e) {
    //   print('开启蓝牙失败: $e');
    //   rethrow;
    // }
  }

  // // 监听蓝牙状态变化
  // static Stream<BluetoothAdapterState> get onStateChanged {
  //   return FlutterBluePlus.adapterState;
  // }
}

// // 使用示例
// class BluetoothStatusWidget extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return StreamBuilder<BluetoothState>(
//       stream: SimpleBluetoothService.onStateChanged,
//       builder: (context, snapshot) {
//         final state = snapshot.data ?? BluetoothState.unknown;

//         return ListTile(
//           leading: Icon(
//             state == BluetoothState.on
//                 ? Icons.bluetooth_connected
//                 : Icons.bluetooth_disabled,
//             color: state == BluetoothState.on ? Colors.blue : Colors.grey,
//           ),
//           title: Text('蓝牙状态: ${_getStateText(state)}'),
//           trailing: state == BluetoothState.off
//               ? ElevatedButton(
//                   onPressed: () => SimpleBluetoothService.enableBluetooth(),
//                   child: Text('开启'),
//                 )
//               : null,
//         );
//       },
//     );
//   }

//   String _getStateText(BluetoothState state) {
//     switch (state) {
//       case BluetoothState.on:
//         return '已开启';
//       case BluetoothState.off:
//         return '已关闭';
//       case BluetoothState.turningOn:
//         return '正在开启';
//       case BluetoothState.turningOff:
//         return '正在关闭';
//       default:
//         return '未知';
//     }
//   }
//}
