///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
// ignore_for_file: unused_element

class PlayerReportModelShootLocation {
/*
{
  "id": "402474094",
  "x": 773,
  "y": 311,
  "hit": true
} 
*/

  String? id;
  int? x;
  int? y;
  bool? hit;

  PlayerReportModelShootLocation({
    this.id,
    this.x,
    this.y,
    this.hit,
  });
  PlayerReportModelShootLocation.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    x = json['x']?.toInt();
    y = json['y']?.toInt();
    hit = json['hit'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['x'] = x;
    data['y'] = y;
    data['hit'] = hit;
    return data;
  }
}

class PlayerReportModelVideos {
/*
{
  "duration": 0,
  "marked": true,
  "markedText": "string",
  "shootResult": 0,
  "shootType": 0,
  "videoCover": "string",
  "videoId": "0",
  "videoPath": "string"
} 
*/

  int? duration;
  bool? marked;
  String? markedText;
  int? shootResult;
  int? shootType;
  String? videoCover;
  String? videoId;
  String? videoPath;

  PlayerReportModelVideos({
    this.duration,
    this.marked,
    this.markedText,
    this.shootResult,
    this.shootType,
    this.videoCover,
    this.videoId,
    this.videoPath,
  });
  PlayerReportModelVideos.fromJson(Map<String, dynamic> json) {
    duration = json['duration']?.toInt();
    marked = json['marked'];
    markedText = json['markedText']?.toString();
    shootResult = json['shootResult']?.toInt();
    shootType = json['shootType']?.toInt();
    videoCover = json['videoCover']?.toString();
    videoId = json['videoId']?.toString();
    videoPath = json['videoPath']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['duration'] = duration;
    data['marked'] = marked;
    data['markedText'] = markedText;
    data['shootResult'] = shootResult;
    data['shootType'] = shootType;
    data['videoCover'] = videoCover;
    data['videoId'] = videoId;
    data['videoPath'] = videoPath;
    return data;
  }
}

class PlayerReportModelSinglePlayerDetail {
/*
{
  "assistCount": 0,
  "defensiveReboundCount": 0,
  "dunkShootCount": 0,
  "dunkShootFormat": "string",
  "dunkShootHit": 0,
  "dunkShootRate": "string",
  "efg": "string",
  "freeThrowShootContribution": 0,
  "freeThrowShootCount": 0,
  "freeThrowShootFormat": "string",
  "freeThrowShootHit": 0,
  "freeThrowShootRate": "string",
  "layupShootCount": 0,
  "layupShootFormat": "string",
  "layupShootHit": 0,
  "layupShootRate": "string",
  "midRangeShootCount": 0,
  "midRangeShootFormat": "string",
  "midRangeShootHit": 0,
  "midRangeShootRate": "string",
  "number": "string",
  "offensiveReboundCount": 0,
  "photo": "string",
  "playerId": "0",
  "reboundCount": 0,
  "score": 0,
  "shootCount": 0,
  "shootFormat": "string",
  "shootHit": 0,
  "shootRate": "string",
  "threePointShootCount": 0,
  "threePointShootFormat": "string",
  "threePointShootHit": 0,
  "threePointShootRate": "string",
  "ts": "string",
  "twoPointShootCount": 0,
  "twoPointShootFormat": "string",
  "twoPointShootHit": 0,
  "twoPointShootRate": "string"
} 
*/

  int? assistCount;
  int? defensiveReboundCount;
  int? dunkShootCount;
  String? dunkShootFormat;
  int? dunkShootHit;
  String? dunkShootRate;
  String? efg;
  int? freeThrowShootContribution;
  int? freeThrowShootCount;
  String? freeThrowShootFormat;
  int? freeThrowShootHit;
  String? freeThrowShootRate;
  int? layupShootCount;
  String? layupShootFormat;
  int? layupShootHit;
  String? layupShootRate;
  int? midRangeShootCount;
  String? midRangeShootFormat;
  int? midRangeShootHit;
  String? midRangeShootRate;
  String? number;
  int? offensiveReboundCount;
  String? photo;
  String? playerId;
  int? reboundCount;
  int? score;
  int? shootCount;
  String? shootFormat;
  int? shootHit;
  String? shootRate;
  int? threePointShootCount;
  String? threePointShootFormat;
  int? threePointShootHit;
  String? threePointShootRate;
  String? ts;
  int? twoPointShootCount;
  String? twoPointShootFormat;
  int? twoPointShootHit;
  String? twoPointShootRate;

  PlayerReportModelSinglePlayerDetail({
    this.assistCount,
    this.defensiveReboundCount,
    this.dunkShootCount,
    this.dunkShootFormat,
    this.dunkShootHit,
    this.dunkShootRate,
    this.efg,
    this.freeThrowShootContribution,
    this.freeThrowShootCount,
    this.freeThrowShootFormat,
    this.freeThrowShootHit,
    this.freeThrowShootRate,
    this.layupShootCount,
    this.layupShootFormat,
    this.layupShootHit,
    this.layupShootRate,
    this.midRangeShootCount,
    this.midRangeShootFormat,
    this.midRangeShootHit,
    this.midRangeShootRate,
    this.number,
    this.offensiveReboundCount,
    this.photo,
    this.playerId,
    this.reboundCount,
    this.score,
    this.shootCount,
    this.shootFormat,
    this.shootHit,
    this.shootRate,
    this.threePointShootCount,
    this.threePointShootFormat,
    this.threePointShootHit,
    this.threePointShootRate,
    this.ts,
    this.twoPointShootCount,
    this.twoPointShootFormat,
    this.twoPointShootHit,
    this.twoPointShootRate,
  });
  PlayerReportModelSinglePlayerDetail.fromJson(Map<String, dynamic> json) {
    assistCount = json['assistCount']?.toInt();
    defensiveReboundCount = json['defensiveReboundCount']?.toInt();
    dunkShootCount = json['dunkShootCount']?.toInt();
    dunkShootFormat = json['dunkShootFormat']?.toString();
    dunkShootHit = json['dunkShootHit']?.toInt();
    dunkShootRate = json['dunkShootRate']?.toString();
    efg = json['efg']?.toString();
    freeThrowShootContribution = json['freeThrowShootContribution']?.toInt();
    freeThrowShootCount = json['freeThrowShootCount']?.toInt();
    freeThrowShootFormat = json['freeThrowShootFormat']?.toString();
    freeThrowShootHit = json['freeThrowShootHit']?.toInt();
    freeThrowShootRate = json['freeThrowShootRate']?.toString();
    layupShootCount = json['layupShootCount']?.toInt();
    layupShootFormat = json['layupShootFormat']?.toString();
    layupShootHit = json['layupShootHit']?.toInt();
    layupShootRate = json['layupShootRate']?.toString();
    midRangeShootCount = json['midRangeShootCount']?.toInt();
    midRangeShootFormat = json['midRangeShootFormat']?.toString();
    midRangeShootHit = json['midRangeShootHit']?.toInt();
    midRangeShootRate = json['midRangeShootRate']?.toString();
    number = json['number']?.toString();
    offensiveReboundCount = json['offensiveReboundCount']?.toInt();
    photo = json['photo']?.toString();
    playerId = json['playerId']?.toString();
    reboundCount = json['reboundCount']?.toInt();
    score = json['score']?.toInt();
    shootCount = json['shootCount']?.toInt();
    shootFormat = json['shootFormat']?.toString();
    shootHit = json['shootHit']?.toInt();
    shootRate = json['shootRate']?.toString();
    threePointShootCount = json['threePointShootCount']?.toInt();
    threePointShootFormat = json['threePointShootFormat']?.toString();
    threePointShootHit = json['threePointShootHit']?.toInt();
    threePointShootRate = json['threePointShootRate']?.toString();
    ts = json['ts']?.toString();
    twoPointShootCount = json['twoPointShootCount']?.toInt();
    twoPointShootFormat = json['twoPointShootFormat']?.toString();
    twoPointShootHit = json['twoPointShootHit']?.toInt();
    twoPointShootRate = json['twoPointShootRate']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['assistCount'] = assistCount;
    data['defensiveReboundCount'] = defensiveReboundCount;
    data['dunkShootCount'] = dunkShootCount;
    data['dunkShootFormat'] = dunkShootFormat;
    data['dunkShootHit'] = dunkShootHit;
    data['dunkShootRate'] = dunkShootRate;
    data['efg'] = efg;
    data['freeThrowShootContribution'] = freeThrowShootContribution;
    data['freeThrowShootCount'] = freeThrowShootCount;
    data['freeThrowShootFormat'] = freeThrowShootFormat;
    data['freeThrowShootHit'] = freeThrowShootHit;
    data['freeThrowShootRate'] = freeThrowShootRate;
    data['layupShootCount'] = layupShootCount;
    data['layupShootFormat'] = layupShootFormat;
    data['layupShootHit'] = layupShootHit;
    data['layupShootRate'] = layupShootRate;
    data['midRangeShootCount'] = midRangeShootCount;
    data['midRangeShootFormat'] = midRangeShootFormat;
    data['midRangeShootHit'] = midRangeShootHit;
    data['midRangeShootRate'] = midRangeShootRate;
    data['number'] = number;
    data['offensiveReboundCount'] = offensiveReboundCount;
    data['photo'] = photo;
    data['playerId'] = playerId;
    data['reboundCount'] = reboundCount;
    data['score'] = score;
    data['shootCount'] = shootCount;
    data['shootFormat'] = shootFormat;
    data['shootHit'] = shootHit;
    data['shootRate'] = shootRate;
    data['threePointShootCount'] = threePointShootCount;
    data['threePointShootFormat'] = threePointShootFormat;
    data['threePointShootHit'] = threePointShootHit;
    data['threePointShootRate'] = threePointShootRate;
    data['ts'] = ts;
    data['twoPointShootCount'] = twoPointShootCount;
    data['twoPointShootFormat'] = twoPointShootFormat;
    data['twoPointShootHit'] = twoPointShootHit;
    data['twoPointShootRate'] = twoPointShootRate;
    return data;
  }
}

class PlayerReportModelSectionsScore {
/*
{
  "secScore1": 0,
  "secScore2": 0,
  "secScore3": 0,
  "secScore4": 0,
  "teamId": "0",
  "teamLogo": "string",
  "teamName": "string",
  "totalScore": 0
} 
*/

  int? secScore1;
  int? secScore2;
  int? secScore3;
  int? secScore4;
  String? teamId;
  String? teamLogo;
  String? teamName;
  int? totalScore;

  PlayerReportModelSectionsScore({
    this.secScore1,
    this.secScore2,
    this.secScore3,
    this.secScore4,
    this.teamId,
    this.teamLogo,
    this.teamName,
    this.totalScore,
  });
  PlayerReportModelSectionsScore.fromJson(Map<String, dynamic> json) {
    secScore1 = json['secScore1']?.toInt();
    secScore2 = json['secScore2']?.toInt();
    secScore3 = json['secScore3']?.toInt();
    secScore4 = json['secScore4']?.toInt();
    teamId = json['teamId']?.toString();
    teamLogo = json['teamLogo']?.toString();
    teamName = json['teamName']?.toString();
    totalScore = json['totalScore']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['secScore1'] = secScore1;
    data['secScore2'] = secScore2;
    data['secScore3'] = secScore3;
    data['secScore4'] = secScore4;
    data['teamId'] = teamId;
    data['teamLogo'] = teamLogo;
    data['teamName'] = teamName;
    data['totalScore'] = totalScore;
    return data;
  }
}

class PlayerReportModel {
/*
{
  "bindable": true,
  "boundUserAvatar": "string",
  "boundUserId": "0",
  "boundUserName": "string",
  "boundUserPhoto": "string",
  "matchStartTime": "string",
  "mvp": true,
  "number": "string",
  "photo": "string",
  "playerId": "0",
  "posterId": 0,
  "rate": "string",
  "score": 0,
  "sectionsScore": [
    {
      "secScore1": 0,
      "secScore2": 0,
      "secScore3": 0,
      "secScore4": 0,
      "teamId": "0",
      "teamLogo": "string",
      "teamName": "string",
      "totalScore": 0
    }
  ],
  "shootCount": 0,
  "shootHit": 0,
  "singlePlayerDetail": {
    "assistCount": 0,
    "defensiveReboundCount": 0,
    "dunkShootCount": 0,
    "dunkShootFormat": "string",
    "dunkShootHit": 0,
    "dunkShootRate": "string",
    "efg": "string",
    "freeThrowShootContribution": 0,
    "freeThrowShootCount": 0,
    "freeThrowShootFormat": "string",
    "freeThrowShootHit": 0,
    "freeThrowShootRate": "string",
    "layupShootCount": 0,
    "layupShootFormat": "string",
    "layupShootHit": 0,
    "layupShootRate": "string",
    "midRangeShootCount": 0,
    "midRangeShootFormat": "string",
    "midRangeShootHit": 0,
    "midRangeShootRate": "string",
    "number": "string",
    "offensiveReboundCount": 0,
    "photo": "string",
    "playerId": "0",
    "reboundCount": 0,
    "score": 0,
    "shootCount": 0,
    "shootFormat": "string",
    "shootHit": 0,
    "shootRate": "string",
    "threePointShootCount": 0,
    "threePointShootFormat": "string",
    "threePointShootHit": 0,
    "threePointShootRate": "string",
    "ts": "string",
    "twoPointShootCount": 0,
    "twoPointShootFormat": "string",
    "twoPointShootHit": 0,
    "twoPointShootRate": "string"
  },
  "teamJoined": true,
  "teamLogo": "string",
  "teamName": "string",
  "title": [
    "string"
  ],
  "videoCover": "string",
  "videoPath": "string",
  "videoStatus": 0,
  "videoMergeId": "2",
  "videos": [
    {
      "duration": 0,
      "marked": true,
      "markedText": "string",
      "shootResult": 0,
      "shootType": 0,
      "videoCover": "string",
      "videoId": "0",
      "videoPath": "string"
    }
  ],
  "locked": 0,
  "unlockBy": "269694"
} 
*/

  bool? bindable;
  String? boundUserAvatar;
  String? boundUserId;
  String? boundUserName;
  String? boundUserPhoto;
  String? matchStartTime;
  bool? mvp;
  String? number;
  String? photo;
  String? playerId;
  int? posterId;
  String? rate;
  int? score;
  List<PlayerReportModelSectionsScore?>? sectionsScore;
  int? shootCount;
  int? shootHit;
  PlayerReportModelSinglePlayerDetail? singlePlayerDetail;
  bool? teamJoined;
  String? teamLogo;
  String? teamName;
  List<String?>? title;
  String? videoCover;
  String? videoPath;
  int? videoStatus;
  String? videoMergeId;
  List<PlayerReportModelVideos?>? videos;
  int? locked;
  String? unlockBy;
  int? contributionValue;
  List<PlayerReportModelShootLocation?>? shootLocation;

  PlayerReportModel({
    this.bindable,
    this.boundUserAvatar,
    this.boundUserId,
    this.boundUserName,
    this.boundUserPhoto,
    this.matchStartTime,
    this.mvp,
    this.number,
    this.photo,
    this.playerId,
    this.posterId,
    this.rate,
    this.score,
    this.sectionsScore,
    this.shootCount,
    this.shootHit,
    this.singlePlayerDetail,
    this.teamJoined,
    this.teamLogo,
    this.teamName,
    this.title,
    this.videoCover,
    this.videoPath,
    this.videoStatus,
    this.videoMergeId,
    this.videos,
    this.locked,
    this.unlockBy,
    this.contributionValue,
    this.shootLocation,
  });
  PlayerReportModel.fromJson(Map<String, dynamic> json) {
    bindable = json['bindable'];
    boundUserAvatar = json['boundUserAvatar']?.toString();
    boundUserId = json['boundUserId']?.toString();
    boundUserName = json['boundUserName']?.toString();
    boundUserPhoto = json['boundUserPhoto']?.toString();
    matchStartTime = json['matchStartTime']?.toString();
    mvp = json['mvp'];
    number = json['number']?.toString();
    photo = json['photo']?.toString();
    playerId = json['playerId']?.toString();
    posterId = json['posterId']?.toInt();
    rate = json['rate']?.toString();
    score = json['score']?.toInt();
    if (json['sectionsScore'] != null) {
      final v = json['sectionsScore'];
      final arr0 = <PlayerReportModelSectionsScore>[];
      v.forEach((v) {
        arr0.add(PlayerReportModelSectionsScore.fromJson(v));
      });
      sectionsScore = arr0;
    }
    shootCount = json['shootCount']?.toInt();
    shootHit = json['shootHit']?.toInt();
    singlePlayerDetail = (json['singlePlayerDetail'] != null)
        ? PlayerReportModelSinglePlayerDetail.fromJson(
            json['singlePlayerDetail'])
        : null;
    teamJoined = json['teamJoined'];
    teamLogo = json['teamLogo']?.toString();
    teamName = json['teamName']?.toString();
    if (json['shootLocation'] != null) {
      final v = json['shootLocation'];
      final arr0 = <PlayerReportModelShootLocation>[];
      v.forEach((v) {
        arr0.add(PlayerReportModelShootLocation.fromJson(v));
      });
      shootLocation = arr0;
    }

    if (json['title'] != null) {
      final v = json['title'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      title = arr0;
    }
    videoCover = json['videoCover']?.toString();
    videoPath = json['videoPath']?.toString();
    videoStatus = json['videoStatus']?.toInt();
    videoMergeId = json['videoMergeId']?.toString();
    if (json['videos'] != null) {
      final v = json['videos'];
      final arr0 = <PlayerReportModelVideos>[];
      v.forEach((v) {
        arr0.add(PlayerReportModelVideos.fromJson(v));
      });
      videos = arr0;
    }
    locked = json['locked']?.toInt();
    contributionValue = json['contributionValue']?.toInt();
    unlockBy = json['unlockBy']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['bindable'] = bindable;
    data['boundUserAvatar'] = boundUserAvatar;
    data['boundUserId'] = boundUserId;
    data['boundUserName'] = boundUserName;
    data['boundUserPhoto'] = boundUserPhoto;
    data['matchStartTime'] = matchStartTime;
    data['mvp'] = mvp;
    data['number'] = number;
    data['photo'] = photo;
    data['playerId'] = playerId;
    data['posterId'] = posterId;
    data['rate'] = rate;
    data['score'] = score;
    data['contributionValue'] = contributionValue;
    Map<String, dynamic> toJson() {
      final data = <String, dynamic>{};
      if (shootLocation != null) {
        final v = shootLocation;
        final arr0 = [];
        v!.forEach((v) {
          arr0.add(v!.toJson());
        });
        data['shootLocation'] = arr0;
      }
      return data;
    }

    if (sectionsScore != null) {
      final v = sectionsScore;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['sectionsScore'] = arr0;
    }
    data['shootCount'] = shootCount;
    data['shootHit'] = shootHit;
    if (singlePlayerDetail != null) {
      data['singlePlayerDetail'] = singlePlayerDetail!.toJson();
    }
    data['teamJoined'] = teamJoined;
    data['teamLogo'] = teamLogo;
    data['teamName'] = teamName;
    if (title != null) {
      final v = title;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['title'] = arr0;
    }
    data['videoCover'] = videoCover;
    data['videoPath'] = videoPath;
    data['videoStatus'] = videoStatus;
    data['videoMergeId'] = videoMergeId;
    if (videos != null) {
      final v = videos;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['videos'] = arr0;
    }
    data['locked'] = locked;
    data['unlockBy'] = unlockBy;
    return data;
  }
}
