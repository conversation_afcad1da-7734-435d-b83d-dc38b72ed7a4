// ignore_for_file: prefer_function_declarations_over_variables, avoid_print, unnecessary_brace_in_string_interps

import 'dart:developer';
import 'dart:io';
import 'package:ffmpeg_kit_flutter_new/ffprobe_kit.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit_config.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';

class VideoEditorService {
  final void Function(double progress, String message, {bool isError})
      onProgressBack;
  // final void Function(String mergeUrl, String coverUrl) onCompletedBack;

  VideoEditorService({required this.onProgressBack});
  static String bestVideoCodec = '';
  static bool isSlow = false; //是否慢动作 进度计算
  static bool watermark = false; //是否水印 进度计算
  static bool isBgm = false; //是否bgm 进度计算
  static double isSlowProgress = 0.0; //是否慢动作 进度计算
  static double watermarkProgress = 0.0; //是否水印 进度计算
  static double isBgmProgress = 0.0; //是否bgm 进度计算
  static double mergeProgress = 0.0; //合成 进度计算

  /// 测试编解码器是否能实际进行编码
  static Future<bool> _testCodecEncoding(String codecName) async {
    try {
      // 获取测试视频文件路径
      final testVideoPath = await _getTestVideoPath();
      if (testVideoPath == null) {
        print('Test video not found, skipping codec test for $codecName');
        return true; // 如果测试文件不存在，假设编解码器可用
      }

      // 创建临时输出文件路径
      final tempDir = await getTemporaryDirectory();
      final outputPath =
          '${tempDir.path}/test_${codecName}_${DateTime.now().millisecondsSinceEpoch}.mp4';
      final videoQualityParams = _getVideoQualityParams2(codecName);
      print(
          'Testing codec $codecName with command: -y -i "$testVideoPath" -c:v $codecName -c:a aac  "$outputPath"');
      // 使用同步执行方式，避免异步问题
      final session = await FFmpegKit.execute(
          '-y -i "$testVideoPath" -c:v $codecName $videoQualityParams -c:a aac -avoid_negative_ts make_zero "$outputPath"');

      final returnCode = await session.getReturnCode();
      final logs = await session.getLogs();

      // 打印所有日志以便调试
      for (var log in logs) {
        String logMessage = log.getMessage();
        print('FFmpeg log: $logMessage');
      }

      bool isSuccess = ReturnCode.isSuccess(returnCode);

      // 检查输出文件是否生成
      final outputFile = File(outputPath);
      bool fileExists = await outputFile.exists();
      if (fileExists) {
        final fileSize = await outputFile.length();
        print('Output file created: $fileSize bytes');
      } else {
        print('Output file not created');
      }

      // 清理临时文件
      try {
        if (await outputFile.exists()) {
          await outputFile.delete();
        }
      } catch (e) {
        print('Error cleaning up test file: $e');
      }

      print(
          'Codec $codecName test result: $isSuccess (return code: $returnCode, file exists: $fileExists)');

      // 如果返回码为null或失败，但文件存在，说明编解码器可能可用
      if (!isSuccess && fileExists) {
        print('Codec $codecName appears to work despite non-zero return code');
        return true;
      }
      //await FFmpegKit.cancel();
      return isSuccess;
    } catch (e) {
      print('Error testing codec $codecName: $e');
      return false;
    }
  }

  /// 获取测试视频文件路径
  static Future<String?> _getTestVideoPath() async {
    try {
      // 首先尝试从assets中复制文件到临时目录
      final tempDir = await getTemporaryDirectory();
      final tempVideoPath = '${tempDir.path}/test_video.mp4';

      // 检查临时文件是否已存在
      final tempFile = File(tempVideoPath);
      if (await tempFile.exists()) {
        print('Using cached test video at: $tempVideoPath');
        return tempVideoPath;
      }

      // 尝试从assets复制文件
      try {
        // 使用Flutter的rootBundle来访问assets
        final byteData = await rootBundle.load('assets/videos/test.mp4');
        await tempFile.writeAsBytes(byteData.buffer.asUint8List());
        print('Copied test video from assets to: $tempVideoPath');
        return tempVideoPath;
      } catch (e) {
        print('Failed to load from assets: $e');
      }

      // 如果assets加载失败，尝试直接文件系统访问（开发环境）
      final possiblePaths = [
        'assets/videos/test.mp4',
        'assets/test.mp4',
        'test.mp4',
      ];

      for (String path in possiblePaths) {
        final file = File(path);
        if (await file.exists()) {
          print('Found test video at: ${file.absolute.path}');
          return file.absolute.path;
        }
      }

      print('Test video file not found in any expected location');
      return null;
    } catch (e) {
      print('Error finding test video: $e');
      return null;
    }
  }

  /// 获取最佳可用的视频编解码器
  static Future<String> _getVideoCodec() async {
    if (bestVideoCodec.isNotEmpty) {
      return bestVideoCodec;
    }
    try {
      bestVideoCodec = 'libx264';
      if (Platform.isAndroid) {
        bool available = await _testCodecEncoding('h264_mediacodec');
        if (available) {
          bestVideoCodec = 'h264_mediacodec';
        }
      } else if (Platform.isIOS) {
        bool available = await _testCodecEncoding('h264_videotoolbox');
        if (available) {
          bestVideoCodec = 'h264_videotoolbox';
        }
      }
      print('最佳编解码器: $bestVideoCodec');
      return bestVideoCodec;
    } catch (e) {
      print('编解码器检查失败，使用默认值: $e');
      return 'libx264';
    }
  }

  /// 获取平台特定的音频编解码器
  String _getAudioCodec() {
    // 所有平台都使用 aac
    return 'aac';
  }

  /// 获取平台特定的视频质量参数
  String _getVideoQualityParams(codec) {
    if (codec == 'h264_mediacodec') {
      // Android MediaCodec 质量参数
      return '-b:v 8M -maxrate 10M -bufsize 16M';
    } else if (codec == 'h264_videotoolbox') {
      // iOS VideoToolbox 质量参数
      // 对于VideoToolbox，推荐使用以下参数：
      // -b:v: 比特率控制 (有效)
      // -allow_sw 1: 允许软件回退 (推荐)
      // -q:v 对VideoToolbox无效，应该使用-b:v或-maxrate
      return '-b:v 8M -maxrate 10M -allow_sw 1';
    } else {
      // 其他平台 libx264 质量参数
      return '-crf 23 -preset medium -b:v 8M';
    }
  }

  static String _getVideoQualityParams2(codec) {
    if (codec == 'h264_mediacodec') {
      // Android MediaCodec 质量参数
      return '-b:v 8M -maxrate 10M -bufsize 16M';
    } else if (codec == 'h264_videotoolbox') {
      // iOS VideoToolbox 质量参数
      // 对于VideoToolbox，推荐使用以下参数：
      // -b:v: 比特率控制 (有效)
      // -allow_sw 1: 允许软件回退 (推荐)
      // -q:v 对VideoToolbox无效，应该使用-b:v或-maxrate
      return '-b:v 8M -maxrate 10M -allow_sw 1';
    } else {
      // 其他平台 libx264 质量参数
      return '-crf 23 -preset medium -b:v 8M';
    }
  }

  /// 获取平台特定的音频质量参数
  String _getAudioQualityParams() {
    // 高质量 AAC 编码参数
    return '-b:a 256k -ar 48000';
  }

  /// 将文件路径转换为FFmpeg可用的路径（处理Android SAF）
  Future<String> _getSafePath(String originalPath) async {
    if (Platform.isAndroid) {
      // 如果是Android并且路径看起来像content URI
      print('getMinimumResolution _getSafePath: originalPath: $originalPath');
      if (originalPath.startsWith('content://')) {
        print('_getSafePath: 检测到 Android content:// URI: $originalPath');
        try {
          final safUrl =
              await FFmpegKitConfig.getSafParameterForRead(originalPath);
          if (safUrl != null) {
            print('getMinimumResolution _getSafePath: SAF转换成功: $safUrl');
            return safUrl;
          } else {
            print('getMinimumResolution _getSafePath: SAF转换失败，使用原路径');
            return originalPath;
          }
        } catch (e) {
          print('_getSafePath: SAF转换异常: $e');
          return originalPath;
        }
      }
    }
    return originalPath;
  }

  Future<Map?> processVideos({
    required List<File> videoFiles,
    File? watermarkFile,
    File? bgmFile,
    bool enableSlowMotion = true,
    double slowMotionFactor = 2.0,
    double watermarkOpacity = 0.8,
    String watermarkPosition = 'top-right',
    bool removeOriginalAudio = false,
    double bgmVolume = 0.5,
  }) async {
    isSlow = enableSlowMotion; //是否慢动作 进度计算
    watermark = watermarkFile != null; //是否水印 进度计算
    isBgm = bgmFile != null; //是否bgm 进度计算
    isSlowProgress = 0.0; //是否慢动作 进度计算
    watermarkProgress = 0.0; //是否水印 进度计算
    isBgmProgress = 0.0; //是否bgm 进度计算
    mergeProgress = 0.0; //合成 进度计算
    try {
      print('=== 开始视频处理 ===');
      print('输入视频数量: ${videoFiles.length}');
      for (int i = 0; i < videoFiles.length; i++) {
        print('视频 $i: ${videoFiles[i].path}');
        print('视频 $i 文件存在: ${videoFiles[i].existsSync()}');
        if (videoFiles[i].existsSync()) {
          print('视频 $i 文件大小: ${videoFiles[i].lengthSync()} bytes');
        }
      }
      print('启用慢动作: $enableSlowMotion');
      print('慢动作倍数: $slowMotionFactor');
      print('去除原声: $removeOriginalAudio');
      print('水印文件: ${watermarkFile?.path ?? 'null'}');
      print('BGM文件: ${bgmFile?.path ?? 'null'}');

      // 预检查视频分辨率并显示信息
      print('=== 预检查视频分辨率 ===');
      for (int i = 0; i < videoFiles.length; i++) {
        final String safPath = await _getSafePath(videoFiles[i].path);
        final Map<String, int>? resolution = await _getVideoResolution(safPath);
        if (resolution != null) {
          print('视频33 $i 分辨率: ${resolution['width']}x${resolution['height']}');
        } else {
          print('视频 $i: 无法获取分辨率');
        }
      }

      final Directory tempDir = await getTemporaryDirectory();
      print('临时目录: ${tempDir.path}');
      final String outputPath =
          '${tempDir.path}/merged_video_${DateTime.now().millisecondsSinceEpoch}.mp4';
      print('输出路径: $outputPath');

      // 清理可能存在的临时文件
      await cleanupTempFiles();

      // 第一步：如果启用慢动作，先为每个视频添加慢动作效果
      List<String> processedVideos = [];

      if (enableSlowMotion) {
        print('=== 开始慢动作处理 ===');
        for (int i = 0; i < videoFiles.length; i++) {
          final String slowMotionPath = '${tempDir.path}/slow_motion_$i.mp4';
          print('处理视频 $i 慢动作: ${videoFiles[i].path} -> $slowMotionPath');

          // 转换为SAF路径（如果需要）
          final String safInputPath = await _getSafePath(videoFiles[i].path);
          print('转换后的输入路径: $safInputPath');

          final bool success = await _addSlowMotionEffect(
            safInputPath,
            slowMotionPath,
            slowMotionFactor,
          );

          if (success) {
            print('视频 $i 慢动作处理成功');
            processedVideos.add(slowMotionPath);
          } else {
            print('视频 $i 慢动作处理失败，使用原视频的SAF路径');
            // 如果慢动作处理失败，使用原视频的SAF路径
            processedVideos.add(safInputPath);
          }
          getProgressBack(
              type: 1,
              progress: processedVideos.length / videoFiles.length,
              error: "");
          // onProgress(0.0, processedVideos.length, videoFiles.length, '',
          //     isError: false);
        }
      } else {
        print('=== 跳过慢动作处理 ===');
        // 即使跳过慢动作处理，也需要转换SAF路径
        processedVideos = [];
        for (File file in videoFiles) {
          final String safPath = await _getSafePath(file.path);
          processedVideos.add(safPath);
          print('原始路径: ${file.path} -> SAF路径: $safPath');
        }
      }

      print('=== 开始视频合并 ===');
      print('待合并视频列表:');
      for (int i = 0; i < processedVideos.length; i++) {
        print('  $i: ${processedVideos[i]}');
      }

      // 第二步：合并视频
      final String mergedPath = '${tempDir.path}/merged_base.mp4';
      print('合并输出路径: $mergedPath');
      final bool mergeSuccess = await _mergeVideos(processedVideos, mergedPath,
          removeOriginalAudio: removeOriginalAudio);

      if (!mergeSuccess) {
        print('视频合并失败！');
        getProgressBack(type: 2, progress: 1.0, error: "视频合并失败");
        throw Exception('视频合并失败');
      }
      print('视频合并成功！');
      // onProgress(0.0, videoFiles.length, videoFiles.length, '', isError: false);
      String currentPath = mergedPath;

      // 第三步：添加水印（如果提供）
      if (watermarkFile != null) {
        print('=== 开始添加水印 ===');
        final String watermarkedPath = '${tempDir.path}/watermarked.mp4';
        final String safWatermarkPath = await _getSafePath(watermarkFile.path);
        print('水印文件SAF路径: $safWatermarkPath');

        final bool watermarkSuccess = await _addWatermark(
          currentPath,
          safWatermarkPath,
          watermarkedPath,
          watermarkOpacity,
          watermarkPosition,
        );

        if (watermarkSuccess) {
          currentPath = watermarkedPath;
        }
      }
      final String sliceDir =
          '${tempDir.path}/video_slices_${DateTime.now().millisecondsSinceEpoch}';
      await Directory(sliceDir).create(recursive: true);
      final String thumbnailPath =
          '$sliceDir/slice_${outputPath.hashCode}_thumbnail.jpg';
      // 第四步：添加背景音乐（如果提供）
      if (bgmFile != null) {
        print('=== 开始添加背景音乐 ===');
        final String finalPath = outputPath;
        final String safBgmPath = await _getSafePath(bgmFile.path);
        print('BGM文件SAF路径: $safBgmPath');

        final bool bgmSuccess = await _addBackgroundMusic(
          currentPath,
          safBgmPath,
          finalPath,
          bgmVolume,
        );

        if (bgmSuccess) {
          // 生成缩略图（从片段的第1秒截取）
          final bool thumbnailSuccess = await _generateThumbnail(
            finalPath,
            thumbnailPath,
            1.0, // 从第1秒截取
          );

          if (thumbnailSuccess) {
            return {
              'video': finalPath,
              'thumbnail': thumbnailPath,
            };
          } else {
            return {
              'video': finalPath,
              'thumbnail': "",
            };
          }
        }
      } else {
        // 如果没有BGM，直接复制到输出路径
        await File(currentPath).copy(outputPath);
        // 生成缩略图（从片段的第1秒截取）
        final bool thumbnailSuccess = await _generateThumbnail(
          outputPath,
          thumbnailPath,
          1.0, // 从第1秒截取
        );

        if (thumbnailSuccess) {
          return {
            'video': outputPath,
            'thumbnail': thumbnailPath,
          };
        } else {
          return {
            'video': outputPath,
            'thumbnail': "",
          };
        }
      }

      return null;
    } catch (e) {
      print('视频处理错误: $e');
      return null;
    }
  }

  Future<int> calculateGopSize(String inputPath) async {
    try {
      // 获取输入视频的帧率
      final session = await FFmpegKit.execute('-i "$inputPath"');
      final output = await session.getOutput();

      if (output != null) {
        final fpsMatch = RegExp(r'(\d+(\.\d+)?)\s*fps').firstMatch(output);
        if (fpsMatch != null) {
          double fps = double.parse(fpsMatch.group(1) ?? "30.0");
          return (fps * 2).round(); // GOP 大小为帧率的 2 倍
        }
      }
    } catch (e) {
      print('计算 GOP 大小时出错: $e');
    }

    return 60; // 默认值
  }

  /// 为视频添加慢动作效果（第2秒到第4秒，慢速一倍）
  Future<bool> _addSlowMotionEffect(
    String inputPath,
    String outputPath,
    double slowFactor,
  ) async {
    try {
      print('_addSlowMotionEffect: 开始处理');
      print('_addSlowMotionEffect: 输入文件: $inputPath');
      print('_addSlowMotionEffect: 输出文件: $outputPath');
      print('_addSlowMotionEffect: 慢动作倍数: $slowFactor');

      // 检查输入文件
      if (!File(inputPath).existsSync()) {
        print('_addSlowMotionEffect: 输入文件不存在！');
        return false;
      }
      print(
          '_addSlowMotionEffect: 输入文件大小: ${File(inputPath).lengthSync()} bytes');

      // 检测视频是否有音频流
      final bool hasAudio = await _hasAudioStream(inputPath);
      print('_addSlowMotionEffect: 检测到音频流: $hasAudio');

      // 创建复杂滤镜：第2秒到第4秒添加慢动作效果（简化版本）
      // 使用更简单的方法：分三段处理
      final videoCodec = await _getVideoCodec();
      final audioCodec = _getAudioCodec();
      final videoQualityParams = _getVideoQualityParams(videoCodec);
      final audioQualityParams = _getAudioQualityParams();
      print('_addSlowMotionEffect: 使用编解码器 - 视频: $videoCodec, 音频: $audioCodec');
      print(
          '_addSlowMotionEffect: 质量参数 - 视频: $videoQualityParams, 音频: $audioQualityParams');

      String command;
      if (hasAudio) {
        // 有音频：处理视频和音频
        command = '-y -i "$inputPath" '
            '-filter_complex "'
            '[0:v]split=3[v1][v2][v3];'
            '[0:a]asplit=3[a1][a2][a3];'
            '[v1]trim=end=7,setpts=PTS-STARTPTS[seg1v];'
            '[a1]atrim=end=7,asetpts=PTS-STARTPTS[seg1a];'
            '[v2]trim=start=7:end=9,setpts=${slowFactor}*(PTS-STARTPTS)[seg2v];'
            '[a2]atrim=start=7:end=9,asetpts=PTS-STARTPTS,atempo=${1 / slowFactor}[seg2a];'
            '[v3]trim=start=9,setpts=PTS-STARTPTS[seg3v];'
            '[a3]atrim=start=9,asetpts=PTS-STARTPTS[seg3a];'
            '[seg1v][seg1a][seg2v][seg2a][seg3v][seg3a]concat=n=3:v=1:a=1[outv][outa]" '
            '-map "[outv]" -map "[outa]" -c:v $videoCodec -c:a $audioCodec $videoQualityParams $audioQualityParams -avoid_negative_ts make_zero "$outputPath"';
        print('_addSlowMotionEffect: 使用音频+视频模式');
      } else {
        // // 无音频：只处理视频
        command = '-y -i "$inputPath" '
            '-filter_complex "'
            '[0:v]split=3[v1][v2][v3];'
            '[v1]trim=end=7,setpts=PTS-STARTPTS[seg1v];'
            '[v2]trim=start=7:end=9,setpts=${slowFactor}*(PTS-STARTPTS)[seg2v];'
            '[v3]trim=start=9,setpts=PTS-STARTPTS[seg3v];'
            '[seg1v][seg2v][seg3v]concat=n=3:v=1:a=0[outv]" '
            '-map "[outv]" -c:v $videoCodec $videoQualityParams -avoid_negative_ts make_zero "$outputPath"';
        print('_addSlowMotionEffect: 使用仅视频模式');
      }
      print('_addSlowMotionEffect: FFmpeg 命令: $command');

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();
      final String? output = await session.getOutput();
      final String? failureStackTrace = await session.getFailStackTrace();
      final logs = await session.getLogs();

      print('_addSlowMotionEffect: FFmpeg 返回码: $returnCode');

      // 输出完整的日志信息
      print('_addSlowMotionEffect: FFmpeg 日志数量: ${logs.length}');
      for (int i = 0; i < logs.length; i++) {
        final log = logs[i];
        print('_addSlowMotionEffect: 日志 $i: ${log.getMessage()}');
      }

      if (output != null && output.isNotEmpty) {
        print('_addSlowMotionEffect: FFmpeg 完整输出:');
        print(output);
      }

      if (failureStackTrace != null) {
        print('_addSlowMotionEffect: FFmpeg 错误堆栈: $failureStackTrace');
      }

      // 检查输出文件
      if (File(outputPath).existsSync()) {
        print(
            '_addSlowMotionEffect: 输出文件生成成功，大小: ${File(outputPath).lengthSync()} bytes');
      } else {
        print('_addSlowMotionEffect: 输出文件未生成！');
      }

      final bool success = ReturnCode.isSuccess(returnCode);
      print('_addSlowMotionEffect: 处理结果: $success');
      return success;
    } catch (e) {
      print('慢动作处理错误: $e');
      return false;
    }
  }

  /// 合并多个视频文件（支持分辨率适配）
  Future<bool> _mergeVideos(List<String> videoPaths, String outputPath,
      {bool removeOriginalAudio = false}) async {
    try {
      print('_mergeVideos: 开始合并 ${videoPaths.length} 个视频');
      print('_mergeVideos: 输出路径: $outputPath');
      print('_mergeVideos: 去除原声: $removeOriginalAudio');
      if (videoPaths.length == 1) {
        print('_mergeVideos: 只有一个视频，直接复制');
        // 如果只有一个视频，直接复制
        await File(videoPaths.first).copy(outputPath);
        print('_mergeVideos: 复制完成');
        return true;
      }

      // 获取最小分辨率
      final Map<String, int>? minResolution =
          await _getMinimumResolution(videoPaths);
      if (minResolution == null) {
        print('_mergeVideos22: 无法获取视频分辨率，使用传统合并方式');
        return await _mergeVideosLegacy(videoPaths, outputPath,
            removeOriginalAudio: removeOriginalAudio);
      }

      final int targetWidth = minResolution['width']!;
      final int targetHeight = minResolution['height']!;
      print('_mergeVideos: 目标分辨率: ${targetWidth}x${targetHeight}');

      // 验证目标分辨率的有效性
      if (targetWidth <= 0 ||
          targetHeight <= 0 ||
          targetWidth > 7680 ||
          targetHeight > 4320) {
        print('_mergeVideos: 错误 - 无效的目标分辨率: ${targetWidth}x${targetHeight}');
        print('_mergeVideos221: 回退到传统合并方式');
        return await _mergeVideosLegacy(videoPaths, outputPath,
            removeOriginalAudio: removeOriginalAudio);
      }

      // 检测第一个视频是否有音频流来决定合并策略
      final bool hasAudio =
          await _hasAudioStream(videoPaths.first) && !removeOriginalAudio;
      print('_mergeVideos: 检测到音频流: $hasAudio (去除原声: $removeOriginalAudio)');

      // 进一步验证所有视频的音频流状态
      for (int i = 0; i < videoPaths.length; i++) {
        final bool videoHasAudio = await _hasAudioStream(videoPaths[i]);
        print('_mergeVideos: 视频 $i 音频流: $videoHasAudio');
      }

      final videoCodec = await _getVideoCodec();
      final audioCodec = _getAudioCodec();
      final videoQualityParams = _getVideoQualityParams(videoCodec);
      final audioQualityParams = _getAudioQualityParams();
      print('_mergeVideos: 使用编解码器 - 视频: $videoCodec, 音频: $audioCodec');
      print(
          '_mergeVideos: 质量参数 - 视频: $videoQualityParams, 音频: $audioQualityParams');

      // 构建输入参数
      StringBuffer inputParams = StringBuffer();
      for (String videoPath in videoPaths) {
        inputParams.write('-i "$videoPath" ');
      }

      // 构建filter_complex滤镜
      StringBuffer filterComplex = StringBuffer();

      // 为每个输入视频创建缩放滤镜
      for (int i = 0; i < videoPaths.length; i++) {
        // 使用更安全的缩放方法：
        // 1. scale: 先缩放到合适尺寸，保持宽高比
        // 2. pad: 再填充到目标尺寸，居中显示
        // 3. setsar: 设置像素宽高比为1:1
        filterComplex.write(
            '[$i:v]scale=w=$targetWidth:h=$targetHeight:force_original_aspect_ratio=decrease,pad=width=$targetWidth:height=$targetHeight:x=(ow-iw)/2:y=(oh-ih)/2:color=black,setsar=1[v$i];');

        if (hasAudio) {
          filterComplex.write('[$i:a]aresample=48000[a$i];');
        }
      }

      // 构建concat滤镜
      for (int i = 0; i < videoPaths.length; i++) {
        filterComplex.write('[v$i]');
        if (hasAudio) {
          filterComplex.write('[a$i]');
        }
      }

      if (hasAudio) {
        filterComplex
            .write('concat=n=${videoPaths.length}:v=1:a=1[outv][outa]');
      } else {
        filterComplex.write('concat=n=${videoPaths.length}:v=1:a=0[outv]');
      }

      final String filterComplexStr = filterComplex.toString();
      print('_mergeVideos: Filter Complex: $filterComplexStr');

      // 验证filter_complex格式
      if (filterComplexStr.isEmpty) {
        print('_mergeVideos: 错误 - Filter Complex为空！');
        return false;
      }

      // 示例期望格式（2个无音频视频）：
      // [0:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,setsar=1[v0];[1:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,setsar=1[v1];[v0][v1]concat=n=2:v=1:a=0[outv]
      print('_mergeVideos: 预期格式验证 - 视频数量: ${videoPaths.length}, 音频: $hasAudio');

      // 构建完整命令
      String command;
      if (hasAudio) {
        command = '-y ${inputParams.toString()}'
            '-filter_complex "$filterComplexStr" '
            '-map "[outv]" -map "[outa]" '
            '-c:v $videoCodec -c:a $audioCodec $videoQualityParams $audioQualityParams '
            '-avoid_negative_ts make_zero "$outputPath"';
        print('_mergeVideos: 使用分辨率适配+音频模式');
      } else {
        command = '-y ${inputParams.toString()}'
            '-filter_complex "$filterComplexStr" '
            '-map "[outv]" '
            '-c:v $videoCodec $videoQualityParams '
            '-avoid_negative_ts make_zero "$outputPath"';
        print('_mergeVideos: 使用分辨率适配+仅视频模式');
      }
      print('_mergeVideos: FFmpeg 命令: $command');

      // final session = await FFmpegKit.execute(command);
      // 🔥 新增：获取总时长（用于计算进度）
      // final totalDuration = await _getTotalDuration(videoPaths);
      // if (totalDuration == null || totalDuration.inSeconds == 0) {
      //   print('_mergeVideoszzz1: 无法获取总时长，进度将不可靠');
      // }

      // // 🔥 新增：定义 LogCallback 来监听日志
      // double lastProgress = 0.0;
      // final logCallback = (Log log) {
      //   final message = log.getMessage();
      //   // 查找 time=00:00:05.123 格式
      //   final timeMatch =
      //       RegExp(r'time=([0-9]{2}:[0-9]{2}:[0-9]{2}(?:\.[0-9]{2,3})?)')
      //           .firstMatch(message);
      //   if (timeMatch != null && totalDuration != null) {
      //     final timeStr = timeMatch.group(1)!;
      //     final currentTime = _parseTimeToDuration(timeStr);
      //     final progress =
      //         (currentTime.inMilliseconds / totalDuration.inMilliseconds)
      //             .clamp(0.0, 1.0);

      //     // 避免频繁更新 UI
      //     if ((progress - lastProgress).abs() > 0.01) {
      //       lastProgress = progress;
      //       //   onProgress?.call(progress); // 调用回调

      //       getProgressBack(type: 2, progress: progress, error: "");
      //       print(
      //           '_mergeVideoszzz1: 当前进度: ${(progress * 100).toStringAsFixed(1)}%');
      //     }
      //   }
      // };

      final session = await FFmpegKit.execute(
        command,
        // // 可选：completeCallback 用于监听执行结束
        // (Session session) async {
        //   final returnCode = await session.getReturnCode();
        //   print('_mergeVideoszzz1: FFmpeg 执行完成，返回码: $returnCode');
        //   getProgressBack(type: 2, progress: 1.0, error: "");
        // },
        // logCallback,
      );
      final returnCode = await session.getReturnCode();
      final String? output = await session.getOutput();
      final String? failureStackTrace = await session.getFailStackTrace();
      final logs = await session.getLogs();

      print('_mergeVideos: FFmpeg 返回码: $returnCode');

      // 输出完整的日志信息
      print('_mergeVideos: FFmpeg 日志数量: ${logs.length}');
      for (int i = 0; i < logs.length; i++) {
        final log = logs[i];
        print('_mergeVideos: 日志 $i: ${log.getMessage()}');
      }

      if (output != null && output.isNotEmpty) {
        print('_mergeVideos: FFmpeg 完整输出:');
        print(output);
      }

      if (failureStackTrace != null) {
        print('_mergeVideos: FFmpeg 错误堆栈: $failureStackTrace');
      }

      // 检查输出文件是否生成
      if (File(outputPath).existsSync()) {
        print(
            '_mergeVideos: 输出文件生成成功，大小: ${File(outputPath).lengthSync()} bytes');
      } else {
        print('_mergeVideos: 输出文件未生成！');
      }

      final bool success = ReturnCode.isSuccess(returnCode);
      print('_mergeVideos: 合并结果: $success');
      getProgressBack(type: 2, progress: 1.0, error: "");
      return success;
    } catch (e) {
      getProgressBack(type: 2, progress: 1.0, error: e.toString());
      print('视频合并错误: $e');
      return false;
    }
  }

  getProgressBack({int type = 1, double progress = 0.0, String error = ""}) {
    //type 1慢动作进度   2合成进度 3水印进度  4bgm进度
    // log("_mergeVideoszzz=progress:${progress} errorMsg:${errorMsg} isError:${isError}");
    switch (type) {
      case 1:
        isSlowProgress = progress;
        break;
      case 2:
        mergeProgress = progress;
        break;
      case 3:
        watermarkProgress = progress;
        break;
      case 4:
        isBgmProgress = progress;
        break;
    }

    //     static bool isSlow = false; //是否慢动作 进度计算
    // static bool watermark = false; //是否水印 进度计算
    // static bool isBgm = false; //是否bgm 进度计算
    // static double isSlowProgress = 0.0; //是否慢动作 进度计算
    // static double watermarkProgress = 0.0; //是否水印 进度计算
    // static double isBgmProgress = 0.0; //是否bgm 进度计算
    //步骤数量
    int count = (isSlow ? 1 : 0) + (isBgm ? 1 : 0) + (watermark ? 1 : 0) + 1;
    var sumProgress2 = 0.0;
    sumProgress2 = isSlowProgress / count +
        mergeProgress / count +
        watermarkProgress / count +
        isBgmProgress / count;
    onProgressBack(sumProgress2, error, isError: error != "");
  }

  /// 传统合并方式（作为后备）
  Future<bool> _mergeVideosLegacy(List<String> videoPaths, String outputPath,
      {bool removeOriginalAudio = false}) async {
    try {
      print('_mergeVideosLegacy: 使用传统方式合并视频');

      // 创建视频列表文件
      final Directory tempDir = await getTemporaryDirectory();
      final String listFilePath = '${tempDir.path}/video_list.txt';
      print('_mergeVideosLegacy: 创建视频列表文件: $listFilePath');

      final StringBuffer listContent = StringBuffer();
      for (String videoPath in videoPaths) {
        print('_mergeVideosLegacy: 检查视频文件: $videoPath');
        print('_mergeVideosLegacy: 文件存在: ${File(videoPath).existsSync()}');
        if (File(videoPath).existsSync()) {
          print(
              '_mergeVideosLegacy: 文件大小: ${File(videoPath).lengthSync()} bytes');
        }
        listContent.writeln("file '$videoPath'");
      }

      print('_mergeVideosLegacy: 视频列表内容:');
      print(listContent.toString());

      await File(listFilePath).writeAsString(listContent.toString());
      print('_mergeVideosLegacy: 视频列表文件写入完成');

      // 检测第一个视频是否有音频流来决定合并策略
      // 检测第一个视频是否有音频流来决定合并策略
      final bool hasAudio =
          await _hasAudioStream(videoPaths.first) && !removeOriginalAudio;
      print(
          '_mergeVideosLegacy: 检测到音频流: $hasAudio (去除原声: $removeOriginalAudio)');

      final videoCodec = await _getVideoCodec();
      final audioCodec = _getAudioCodec();
      final videoQualityParams = _getVideoQualityParams(videoCodec);
      final audioQualityParams = _getAudioQualityParams();

      String command;
      if (hasAudio) {
        // 有音频流：正常合并
        command =
            '-y -f concat -safe 0 -i "$listFilePath" -c:v $videoCodec -c:a $audioCodec $videoQualityParams $audioQualityParams -avoid_negative_ts make_zero "$outputPath"';
        print('_mergeVideosLegacy: 使用音频合并模式');
      } else {
        // 无音频流：只处理视频流
        command =
            '-y -f concat -safe 0 -i "$listFilePath" -c:v $videoCodec $videoQualityParams -avoid_negative_ts make_zero "$outputPath"';
        print('_mergeVideosLegacy: 使用仅视频合并模式');
      }
      print('_mergeVideosLegacy: FFmpeg 命令: $command');

      // //final session = await FFmpegKit.execute(command);
      // // 🔥 新增：获取总时长（用于计算进度）
      // final totalDuration = await _getTotalDuration(videoPaths);
      // if (totalDuration == null || totalDuration.inSeconds == 0) {
      //   print('_mergeVideos: 无法获取总时长，进度将不可靠');
      // }

      // // 🔥 新增：定义 LogCallback 来监听日志
      // double lastProgress = 0.0;
      // final logCallback = (Log log) {
      //   final message = log.getMessage();
      //   // 查找 time=00:00:05.123 格式
      //   final timeMatch =
      //       RegExp(r'time=([0-9]{2}:[0-9]{2}:[0-9]{2}(?:\.[0-9]{2,3})?)')
      //           .firstMatch(message);
      //   if (timeMatch != null && totalDuration != null) {
      //     final timeStr = timeMatch.group(1)!;
      //     final currentTime = _parseTimeToDuration(timeStr);
      //     final progress =
      //         (currentTime.inMilliseconds / totalDuration.inMilliseconds)
      //             .clamp(0.0, 1.0);

      //     // 避免频繁更新 UI
      //     if ((progress - lastProgress).abs() > 0.01) {
      //       lastProgress = progress;
      //       //   onProgress?.call(progress); // 调用回调
      //       getProgressBack(type: 2, progress: progress, error: "");
      //       print(
      //           '_mergeVideoszzz2: 当前进度: ${(progress * 100).toStringAsFixed(1)}%');
      //     }
      //   }
      // };

      final session = await FFmpegKit.execute(
        command,
        // 可选：completeCallback 用于监听执行结束
        // (Session session) {
        //   final returnCode = session.getReturnCode();
        //   print('_mergeVideoszzz2: FFmpeg 执行完成，返回码: $returnCode');
        //   getProgressBack(type: 2, progress: 1.0, error: "");
        // },
        // logCallback,
      );
      final returnCode = await session.getReturnCode();
      final String? output = await session.getOutput();
      final String? failureStackTrace = await session.getFailStackTrace();
      final logs = await session.getLogs();
      print('_mergeVideosLegacy: FFmpeg 返回码: $returnCode');

      // 输出完整的日志信息
      print('_mergeVideosLegacy: FFmpeg 日志数量: ${logs.length}');
      for (int i = 0; i < logs.length; i++) {
        final log = logs[i];
        print('_mergeVideosLegacy: 日志 $i: ${log.getMessage()}');
      }

      if (output != null && output.isNotEmpty) {
        print('_mergeVideosLegacy: FFmpeg 完整输出:');
        print(output);
      }

      if (failureStackTrace != null) {
        print('_mergeVideosLegacy: FFmpeg 错误堆栈: $failureStackTrace');
      }

      // 检查输出文件是否生成
      if (File(outputPath).existsSync()) {
        print(
            '_mergeVideosLegacy: 输出文件生成成功，大小: ${File(outputPath).lengthSync()} bytes');
      } else {
        print('_mergeVideosLegacy: 输出文件未生成！');
      }

      // 清理临时文件
      try {
        await File(listFilePath).delete();
        print('_mergeVideosLegacy: 临时列表文件清理完成');
      } catch (e) {
        print('_mergeVideosLegacy: 清理临时文件失败: $e');
      }

      final bool success = ReturnCode.isSuccess(returnCode);
      print('_mergeVideosLegacy: 合并结果: $success');
      getProgressBack(type: 2, progress: 1.0, error: "");
      return success;
    } catch (e) {
      print('传统视频合并错误: $e');
      getProgressBack(type: 2, progress: 1.0, error: e.toString());
      return false;
    }
  }

  // Duration _parseTimeToDuration(String timeStr) {
  //   try {
  //     final parts = timeStr.split(':');
  //     if (parts.length != 3) return Duration.zero;

  //     final hours = int.parse(parts[0]);
  //     final minutes = int.parse(parts[1]);
  //     final secondsAndMs = parts[2].split('.');
  //     final seconds = int.parse(secondsAndMs[0]);
  //     final milliseconds = secondsAndMs.length > 1
  //         ? int.parse((secondsAndMs[1].padRight(3, '0')).substring(0, 3))
  //         : 0;

  //     return Duration(
  //         hours: hours,
  //         minutes: minutes,
  //         seconds: seconds,
  //         milliseconds: milliseconds);
  //   } catch (e) {
  //     return Duration.zero;
  //   }
  // }

  // Future<Duration?> _getTotalDuration(List<String> videoPaths) async {
  //   Duration total = Duration.zero;
  //   for (final path in videoPaths) {
  //     final duration = await _getSingleVideoDuration(path);
  //     if (duration == null) return null;
  //     total += duration;
  //   }
  //   return total.inSeconds > 0 ? total : null;
  // }

  // Future<Duration?> _getSingleVideoDuration(String videoPath) async {
  //   try {
  //     // 使用 ffprobe 获取时长（秒）
  //     final session = await FFprobeKit.execute(
  //         '-v error -show_entries format=duration -of default=nw=1:nk=1 "$videoPath"');
  //     final output = await session.getOutput();
  //     final durationStr = output!.trim();
  //     if (durationStr.isNotEmpty && durationStr != 'N/A') {
  //       final seconds = double.tryParse(durationStr) ?? 0.0;
  //       return Duration(milliseconds: (seconds * 1000).round());
  //     }
  //   } catch (e) {
  //     print('_getSingleVideoDuration error: $e');
  //   }
  //   return null;
  // }

  Future<void> cancelAll() async {
    isSlow = false; //是否慢动作 进度计算
    watermark = false; //是否水印 进度计算
    isBgm = false; //是否bgm 进度计算
    isSlowProgress = 0.0; //是否慢动作 进度计算
    watermarkProgress = 0.0; //是否水印 进度计算
    isBgmProgress = 0.0; //是否bgm 进度计算
    mergeProgress = 0.0; //合成 进度计算
    try {
// Start an operation
      await FFmpegKit.cancel();
      // Cancel the operation
      // isProcessing = false;
      //  Get.snackbar('已取消', '操作已终止');
    } catch (e) {
      Get.snackbar('错误', '取消失败: $e');
    }
  }

  /// 添加水印
  Future<bool> _addWatermark(
    String videoPath,
    String watermarkPath,
    String outputPath,
    double opacity,
    String position,
  ) async {
    try {
      // 根据位置确定水印坐标
      String overlayPosition;
      switch (position) {
        case 'top-left':
          overlayPosition = '10:10';
          break;
        case 'top-right':
          overlayPosition = 'main_w-overlay_w-20:20';
          break;
        case 'bottom-left':
          overlayPosition = '10:main_h-overlay_h-10';
          break;
        case 'bottom-right':
          overlayPosition = 'main_w-overlay_w-10:main_h-overlay_h-10';
          break;
        case 'center':
          overlayPosition = '(main_w-overlay_w)/2:(main_h-overlay_h)/2';
          break;
        default:
          overlayPosition = 'main_w-overlay_w-10:10';
      }

      // 创建水印叠加命令
      // 添加 -y 参数自动覆盖已存在的文件
      final videoCodec = await _getVideoCodec();
      final audioCodec = _getAudioCodec();
      final videoQualityParams = _getVideoQualityParams(videoCodec);
      final audioQualityParams = _getAudioQualityParams();
      final String command = '-y -i "$videoPath" -i "$watermarkPath" '
          '-filter_complex "[1:v]format=rgba,colorchannelmixer=aa=$opacity[watermark];[0:v][watermark]overlay=$overlayPosition" '
          '-c:v $videoCodec -c:a $audioCodec $videoQualityParams $audioQualityParams "$outputPath"';

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      return ReturnCode.isSuccess(returnCode);
    } catch (e) {
      print('水印添加错误: $e');
      return false;
    }
  }

  /// 检测视频是否包含音频流
  Future<bool> _hasAudioStream(String videoPath) async {
    try {
      final String command = '-i "$videoPath" -hide_banner';
      final session = await FFprobeKit.execute(command);
      final String? output = await session.getAllLogsAsString(3000);

      if (output != null) {
        print('_hasAudioStream: 检查文件: $videoPath');
        print('_hasAudioStream: FFmpeg输出: $output');

        // 更精确的音频流检测：查找"Stream #X:Y: Audio"模式
        final bool hasAudioStream =
            RegExp(r'Stream\s+#\d+:\d+.*?:\s*Audio').hasMatch(output);
        print('_hasAudioStream: 检测结果: $hasAudioStream');
        return hasAudioStream;
      }
      return false;
    } catch (e) {
      print('检测音频流错误: $e');
      return false;
    }
  }

  /// 获取视频分辨率
  Future<Map<String, int>?> _getVideoResolution(String videoPath) async {
    try {
      print('getMinimumResolution videoPath: $videoPath');
      final String command = '-i "$videoPath" -hide_banner';
      final session = await FFprobeKit.execute(command);
      final String? output = await session.getAllLogsAsString(3000);

      if (output != null) {
        print('getMinimumResolution 视频分辨率: $output');
        // 解析视频流信息，格式类似：Stream #0:0: Video: h264, yuv420p, 1920x1080 [SAR 1:1 DAR 16:9], ...
        final RegExp resolutionRegex = RegExp(r'Stream.*Video.*?(\d+)x(\d+),');
        final Match? match = resolutionRegex.firstMatch(output);

        if (match != null) {
          final int width = int.parse(match.group(1)!);
          final int height = int.parse(match.group(2)!);
          print(
              'getMinimumResolution 视频分辨率: ${videoPath} -> ${width}x${height}');
          return {'width': width, 'height': height};
        }
      }
      return null;
    } catch (e) {
      print('getMinimumResolution 获取视频分辨率错误: $e');
      return null;
    }
  }

  /// 获取所有视频的最佳统一分辨率
  Future<Map<String, int>?> _getMinimumResolution(
      List<String> videoPaths) async {
    try {
      print('getMinimumResolution=== 检测视频分辨率 ===');
      List<Map<String, int>> resolutions = [];

      for (String videoPath in videoPaths) {
        final Map<String, int>? resolution =
            await _getVideoResolution(videoPath);
        if (resolution != null) {
          resolutions.add(resolution);
          final int width = resolution['width']!;
          final int height = resolution['height']!;
          print('getMinimumResolution 视频: $videoPath');
          print('getMinimumResolution  分辨率: ${width}x${height}');
        } else {
          print('getMinimumResolution 警告: 无法获取视频分辨率: $videoPath');
        }
      }

      if (resolutions.isEmpty) {
        return null;
      }

      // 计算所有视频的最小像素总数，选择最小的分辨率作为目标
      Map<String, int> targetResolution = resolutions.first;
      int minPixels = targetResolution['width']! * targetResolution['height']!;

      for (Map<String, int> resolution in resolutions) {
        final int width = resolution['width']!;
        final int height = resolution['height']!;
        final int pixels = width * height;

        if (pixels < minPixels) {
          minPixels = pixels;
          targetResolution = resolution;
        }
      }

      int targetWidth = targetResolution['width']!;
      int targetHeight = targetResolution['height']!;

      // 确保分辨率是偶数（H.264编码要求）
      if (targetWidth % 2 != 0) targetWidth--;
      if (targetHeight % 2 != 0) targetHeight--;

      print('=== 选择目标分辨率: ${targetWidth}x${targetHeight} (${minPixels}像素) ===');

      // 检查是否存在横竖屏混合的情况
      bool hasLandscape = false;
      bool hasPortrait = false;

      for (Map<String, int> resolution in resolutions) {
        final int width = resolution['width']!;
        final int height = resolution['height']!;

        if (width > height) {
          hasLandscape = true;
        } else if (height > width) {
          hasPortrait = true;
        }
      }

      if (hasLandscape && hasPortrait) {
        print('警告: 检测到横屏和竖屏视频混合，将统一处理为: ${targetWidth}x${targetHeight}');
        print('建议: 横屏视频将有上下黑边，竖屏视频将有左右黑边');

        // 对于横竖屏混合的情况，选择一个合理的统一分辨率
        // 优先选择较小的标准分辨率以避免质量损失
        final List<Map<String, int>> standardResolutions = [
          {'width': 720, 'height': 1280}, // 竖屏 720p
          {'width': 1080, 'height': 1920}, // 竖屏 1080p
          {'width': 1280, 'height': 720}, // 横屏 720p
          {'width': 1920, 'height': 1080}, // 横屏 1080p
        ];

        for (Map<String, int> stdRes in standardResolutions) {
          final int stdPixels = stdRes['width']! * stdRes['height']!;
          if (stdPixels <= minPixels) {
            targetWidth = stdRes['width']!;
            targetHeight = stdRes['height']!;
            print('选择标准分辨率: ${targetWidth}x${targetHeight}');
            break;
          }
        }
      }

      return {'width': targetWidth, 'height': targetHeight};
    } catch (e) {
      print('获取目标分辨率错误: $e');
      return null;
    }
  }

  /// 添加背景音乐
  Future<bool> _addBackgroundMusic(
    String videoPath,
    String bgmPath,
    String outputPath,
    double bgmVolume,
  ) async {
    try {
      print('=== 开始添加背景音乐 ===');
      print('视频路径: $videoPath');
      print('BGM路径: $bgmPath');
      print('输出路径: $outputPath');
      print('BGM音量: $bgmVolume');

      // 检测视频是否有音频流
      final bool hasAudio = await _hasAudioStream(videoPath);
      print('视频是否包含音频流: $hasAudio');

      final videoCodec = await _getVideoCodec();
      final audioCodec = _getAudioCodec();
      final videoQualityParams = _getVideoQualityParams(videoCodec);
      final audioQualityParams = _getAudioQualityParams();

      String command;
      if (hasAudio) {
        // 视频有音频：混合原音频和BGM
        command = '-y -i "$videoPath" -i "$bgmPath" '
            '-filter_complex "[0:a]volume=1.0[a0];[1:a]volume=$bgmVolume[a1];[a0][a1]amix=inputs=2:duration=first[aout]" '
            '-map 0:v -map "[aout]" -c:v $videoCodec -c:a $audioCodec $videoQualityParams $audioQualityParams "$outputPath"';
        print('使用混音模式（视频有音频）');
      } else {
        // 视频无音频：直接使用BGM作为音频轨道
        command = '-y -i "$videoPath" -i "$bgmPath" '
            '-filter_complex "[1:a]volume=$bgmVolume[aout]" '
            '-map 0:v -map "[aout]" -c:v $videoCodec -c:a $audioCodec $videoQualityParams $audioQualityParams -shortest "$outputPath"';
        print('使用替换模式（视频无音频）');
      }

      print('FFmpeg命令: $command');
      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();
      final String? output = await session.getOutput();

      if (output != null && output.isNotEmpty) {
        print('FFmpeg输出: $output');
      }

      final bool success = ReturnCode.isSuccess(returnCode);
      print('背景音乐添加结果: $success');
      return success;
    } catch (e) {
      print('背景音乐添加错误: $e');
      return false;
    }
  }

  /// 获取视频信息
  Future<Map<String, dynamic>?> getVideoInfo(String videoPath) async {
    try {
      final String command = '-i "$videoPath" -hide_banner';
      final session = await FFmpegKit.execute(command);
      final String? output = await session.getOutput();

      // 解析 FFmpeg 输出获取视频信息
      // 这里可以根据需要解析duration、resolution等信息
      return {'path': videoPath, 'output': output};
    } catch (e) {
      print('获取视频信息错误: $e');
      return null;
    }
  }

  /// 清理临时文件
  Future<void> cleanupTempFiles() async {
    try {
      final Directory tempDir = await getTemporaryDirectory();
      final List<FileSystemEntity> files = tempDir.listSync();

      for (FileSystemEntity file in files) {
        if (file is File &&
            (file.path.contains('slow_motion_') ||
                file.path.contains('merged_') ||
                file.path.contains('watermarked') ||
                file.path.contains('video_list.txt'))) {
          await file.delete();
        }
      }
    } catch (e) {
      print('清理临时文件错误: $e');
    }
  }

  /// 获取视频时长（秒）
  Future<double?> getVideoDuration(String videoPath) async {
    try {
      print('_getVideoDuration: 获取视频时长: $videoPath');

      // 使用更高效的方式获取时长，无需解码任何帧
      final String command =
          '-v quiet -show_entries format=duration -of csv="p=0" -i "$videoPath"';
      final session = await FFmpegKit.execute(command);
      final String? output = await session.getOutput();

      if (output != null && output.trim().isNotEmpty) {
        try {
          final double duration = double.parse(output.trim());
          print('_getVideoDuration: 视频时长: ${duration}秒（使用ffprobe方式）');
          return duration;
        } catch (e) {
          print('_getVideoDuration: 解析时长失败: $e');
        }
      }

      // 如果ffprobe方式失败，回退到原来的方式
      print('_getVideoDuration: ffprobe方式失败，回退到传统方式');
      final String fallbackCommand = '-i "$videoPath" -hide_banner -f null -';
      final fallbackSession = await FFmpegKit.execute(fallbackCommand);
      final String? fallbackOutput = await fallbackSession.getOutput();

      if (fallbackOutput != null) {
        // 从FFmpeg输出中解析时长，格式类似: Duration: 00:01:23.45
        final RegExp durationRegex =
            RegExp(r'Duration: (\d{2}):(\d{2}):(\d{2})\.(\d+)');
        final Match? match = durationRegex.firstMatch(fallbackOutput);

        if (match != null) {
          final int hours = int.parse(match.group(1)!);
          final int minutes = int.parse(match.group(2)!);
          final int seconds = int.parse(match.group(3)!);
          final int milliseconds =
              int.parse(match.group(4)!.padRight(3, '0').substring(0, 3));

          final double totalSeconds =
              hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0;
          print('_getVideoDuration: 视频时长: ${totalSeconds}秒（传统方式）');
          return totalSeconds;
        }
      }

      print('_getVideoDuration: 无法解析视频时长');
      return null;
    } catch (e) {
      print('获取视频时长错误: $e');
      return null;
    }
  }

  /// 切片长视频功能
  /// 每10秒切一个片段，最后一个片段可能不够10秒
  /// 同时为每个片段生成缩略图封面
  Future<List<Map<String, String>>?> sliceVideo({
    required File videoFile,
    int segmentDuration = 10, // 每个片段的时长（秒）
  }) async {
    try {
      print('=== 开始视频切片 ===');
      print('输入视频: ${videoFile.path}');
      print('片段时长: ${segmentDuration}秒');

      if (!videoFile.existsSync()) {
        print('视频文件不存在: ${videoFile.path}');
        return null;
      }

      // 转换为SAF路径（如果需要）
      final String safInputPath = await _getSafePath(videoFile.path);
      print('转换后的输入路径: $safInputPath');

      // 获取视频总时长
      final double? totalDuration = await getVideoDuration(safInputPath);
      if (totalDuration == null) {
        print('无法获取视频时长');
        return null;
      }

      print('视频总时长: ${totalDuration}秒');

      // 计算需要切多少个片段
      final int totalSegments = (totalDuration / segmentDuration).ceil();
      print('总共需要切 $totalSegments 个片段');

      final Directory tempDir = await getTemporaryDirectory();
      final String sliceDir =
          '${tempDir.path}/video_slices_${DateTime.now().millisecondsSinceEpoch}';
      await Directory(sliceDir).create(recursive: true);
      print('切片输出目录: $sliceDir');

      List<Map<String, String>> sliceResults = [];

      for (int i = 0; i < totalSegments; i++) {
        final double startTime = i * segmentDuration.toDouble();
        final double remainingTime = totalDuration - startTime;
        final double actualDuration = remainingTime > segmentDuration
            ? segmentDuration.toDouble()
            : remainingTime;

        print(
            '处理第 ${i + 1} 个片段: ${startTime}s - ${startTime + actualDuration}s');

        final String sliceVideoPath = '$sliceDir/slice_${i + 1}.mp4';

        final String thumbnailPath = '$sliceDir/slice_${i + 1}_thumbnail.jpg';

        // 切片视频
        final bool sliceSuccess = await _createVideoSlice(
          safInputPath,
          sliceVideoPath,
          startTime,
          actualDuration,
        );

        if (!sliceSuccess) {
          print('第 ${i + 1} 个片段切片失败');
          continue;
        }

        // 生成缩略图（从片段的第1秒截取）
        final bool thumbnailSuccess = await _generateThumbnail(
          sliceVideoPath,
          thumbnailPath,
          1.0, // 从第1秒截取
        );

        if (thumbnailSuccess) {
          sliceResults.add({
            'video': sliceVideoPath,
            'thumbnail': thumbnailPath,
            'segment': '${i + 1}',
            'startTime': startTime.toStringAsFixed(1),
            'duration': actualDuration.toStringAsFixed(1),
          });
          print('第 ${i + 1} 个片段处理完成');
        } else {
          print('第 ${i + 1} 个片段缩略图生成失败');
        }
      }

      print('=== 视频切片完成 ===');
      print('成功处理 ${sliceResults.length} 个片段');
      return sliceResults;
    } catch (e) {
      print('视频切片错误: $e');
      return null;
    }
  }

  /// 创建视频切片
  Future<bool> _createVideoSlice(
    String inputPath,
    String outputPath,
    double startTime,
    double duration,
  ) async {
    try {
      print('_createVideoSlice: 切片 $inputPath -> $outputPath');
      print('_createVideoSlice: 开始时间: ${startTime}s, 时长: ${duration}s');

      // 使用 -c copy 直接复制流，无需重新编解码，大大提高速度并保持原始质量
      final String command = '-y -i "$inputPath" -ss $startTime -t $duration '
          '-c copy -avoid_negative_ts make_zero "$outputPath"';

      print('_createVideoSlice: FFmpeg命令（使用copy模式）: $command');

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();
      final String? output = await session.getOutput();
      final logs = await session.getLogs();

      print('_createVideoSlice: 返回码: $returnCode');

      // 输出日志
      for (final log in logs) {
        print('_createVideoSlice: ${log.getMessage()}');
      }

      if (output != null && output.isNotEmpty) {
        print('_createVideoSlice: FFmpeg输出: $output');
      }

      // 检查输出文件
      final bool success =
          ReturnCode.isSuccess(returnCode) && File(outputPath).existsSync();
      if (success) {
        print(
            '_createVideoSlice: 切片成功（copy模式），文件大小: ${File(outputPath).lengthSync()} bytes');
      } else {
        print('_createVideoSlice: 切片失败');
      }

      return success;
    } catch (e) {
      print('创建视频切片错误: $e');
      return false;
    }
  }

  /// 生成视频缩略图
  Future<bool> _generateThumbnail(
    String videoPath,
    String thumbnailPath,
    double timeInSeconds,
  ) async {
    try {
      print('_generateThumbnail: 生成缩略图 $videoPath -> $thumbnailPath');
      print('_generateThumbnail: 截取时间: ${timeInSeconds}s');

      // 优化缩略图生成：使用更快的解码和合适的质量参数
      // -ss 放在 -i 前面可以更快地定位到指定时间
      final String command = '-y -ss $timeInSeconds -i "$videoPath" -vframes 1 '
          '-q:v 2 -vf "scale=320:240:force_original_aspect_ratio=decrease,pad=320:240:(ow-iw)/2:(oh-ih)/2" '
          '"$thumbnailPath"';

      print('_generateThumbnail: FFmpeg命令（优化版）: $command');

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();
      final String? output = await session.getOutput();
      final logs = await session.getLogs();

      print('_generateThumbnail: 返回码: $returnCode');

      // 输出日志
      for (final log in logs) {
        print('_generateThumbnail: ${log.getMessage()}');
      }

      if (output != null && output.isNotEmpty) {
        print('_generateThumbnail: FFmpeg输出: $output');
      }

      // 检查输出文件
      final bool success =
          ReturnCode.isSuccess(returnCode) && File(thumbnailPath).existsSync();
      if (success) {
        print(
            '_generateThumbnail: 缩略图生成成功（优化版），文件大小: ${File(thumbnailPath).lengthSync()} bytes');
      } else {
        print('_generateThumbnail: 缩略图生成失败');
      }

      return success;
    } catch (e) {
      print('生成缩略图错误: $e');
      return false;
    }
  }

  /// 获取FFmpeg支持的编解码器信息
  Future<Map<String, String>> getSupportedCodecs() async {
    try {
      print('=== 开始获取FFmpeg编解码器信息 ===');

      final Map<String, String> codecInfo = {};

      // 获取编解码器列表
      final codecsSession = await FFmpegKit.execute('-codecs');
      final codecsOutput = await codecsSession.getOutput();
      codecInfo['编解码器'] = codecsOutput ?? '无法获取编解码器信息';
      print('=== 支持的编解码器 ===');
      print(codecsOutput ?? '无法获取编解码器信息');

      // 获取编码器列表
      final encodersSession = await FFmpegKit.execute('-encoders');
      final encodersOutput = await encodersSession.getOutput();
      codecInfo['编码器'] = encodersOutput ?? '无法获取编码器信息';
      print('\n=== 支持的编码器 ===');
      print(encodersOutput ?? '无法获取编码器信息');

      // 获取解码器列表
      final decodersSession = await FFmpegKit.execute('-decoders');
      final decodersOutput = await decodersSession.getOutput();
      codecInfo['解码器'] = decodersOutput ?? '无法获取解码器信息';
      print('\n=== 支持的解码器 ===');
      print(decodersOutput ?? '无法获取解码器信息');

      // 获取格式列表
      final formatsSession = await FFmpegKit.execute('-formats');
      final formatsOutput = await formatsSession.getOutput();
      codecInfo['格式'] = formatsOutput ?? '无法获取格式信息';
      print('\n=== 支持的格式 ===');
      print(formatsOutput ?? '无法获取格式信息');

      print('=== FFmpeg信息获取完成 ===');
      return codecInfo;
    } catch (e) {
      print('获取FFmpeg编解码器信息错误: $e');
      return {'错误': '获取FFmpeg编解码器信息错误: $e'};
    }
  }
}
