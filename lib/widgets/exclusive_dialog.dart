import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ExclusiveDialog {
  static Completer<void>? _currentDialogCompleter;
  static bool _isDialogShowing = false;

  /// 显示独占式弹窗（自动防止重复）
  static Future<void> show({
    required BuildContext context,
    required Widget Function(BuildContext) builder,
    bool barrierDismissible = true,
  }) async {
    // 如果已有弹窗，先等待关闭
    if (_isDialogShowing) {
      await _currentDialogCompleter?.future;
      await Future.delayed(Duration(milliseconds: 100));
    }

    _currentDialogCompleter = Completer<void>();
    _isDialogShowing = true;

    await showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) {
        return WillPopScope(
          onWillPop: () async {
            _complete();
            return barrierDismissible;
          },
          child: builder(context),
        );
      },
    ).then((_) => _complete());
  }

  /// 强制关闭当前弹窗
  static void forceClose() {
    if (_isDialogShowing) {
      _complete();
      Navigator.of(Get.context!, rootNavigator: true).pop();
    }
  }

  /// 检查弹窗是否显示
  static bool get isShowing => _isDialogShowing;

  /// 内部完成方法
  static void _complete() {
    _currentDialogCompleter?.complete();
    _currentDialogCompleter = null;
    _isDialogShowing = false;
  }
}
