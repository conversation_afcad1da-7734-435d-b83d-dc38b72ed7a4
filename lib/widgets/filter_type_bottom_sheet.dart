import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ui_packages/ui_packages.dart';

/// 筛选类型选择底部弹窗
class FilterTypeBottomSheet extends StatelessWidget {
  final List<Map<String, dynamic>> filterList;
  final Function(int index)? onItemSelected;

  const FilterTypeBottomSheet({
    super.key,
    required this.filterList,
    this.onItemSelected,
  });

  /// 显示筛选类型选择底部弹窗
  static Future<T?> show<T>(
    BuildContext context, {
    required List<Map<String, dynamic>> filterList,
    Function(int index)? onItemSelected,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return FilterTypeBottomSheet(
          filterList: filterList,
          onItemSelected: onItemSelected,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colours.color191921,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部指示条
          Container(
            width: 38.w,
            height: 3.w,
            margin: EdgeInsets.only(top: 6.w, bottom: 10.w),
            decoration: BoxDecoration(
              color: Colours.color1AD8D8D8,
              borderRadius: BorderRadius.circular(2.5.r),
            ),
          ),

          // 动态生成选项列表
          ...filterList.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isLast = index == filterList.length - 1;

            return Column(
              children: [
                _buildFilterOption(
                  context,
                  title: item["title"] ?? '',
                  onTap: () {
                    Navigator.pop(context);
                    onItemSelected?.call(index);
                  },
                ),
                // 添加分割线，最后一项不添加
                if (!isLast)
                  Container(
                    height: 1,
                    width: ScreenUtil().screenWidth - 30.w,
                    color: Colours.color2F2F3B,
                  ),
              ],
            );
          }),

          // 底部安全区域
          SizedBox(height: ScreenUtil().bottomBarHeight + 20.w),
        ],
      ),
    );
  }

  Widget _buildFilterOption(
    BuildContext context, {
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20.w),
        child: Center(
          child: Text(
            title,
            style: TextStyles.semiBold14,
          ),
        ),
      ),
    );
  }
}
