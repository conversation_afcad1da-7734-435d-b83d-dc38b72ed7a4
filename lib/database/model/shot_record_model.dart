/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
import 'package:floor/floor.dart';
import 'dart:convert';

/*
{
  "player_confidence": 0.6875,
  "file_path": "file:///private/var/mobile/Containers/Data/Application/97011915-C6B7-499C-B3E6-00F9028AC085/tmp/cache_20250514_140954.mp4",
  "start_time": "2025-05-14 14:09:43",
  "shoot_time": 1747202999.8294492,
  "training_id": "5",
  "event_id": "5",
  "is_goal": true,
  "player_image_path": "55CB9EC6-8ABE-420E-9B70-F356DACE8DED.jpg",
  "goal_time": 1747203001.192927,
  "shoot_coord": [
    -39.64937687647534
  ],
  "imgLoadOK": "0",
  "videoLoadOK": "0",
  "created_at": 768895802.796386
} 
*/
@Entity(tableName: 'ShotRecordModel')
class ShotRecordModel {
  @ColumnInfo(name: 'player_confidence')
  double? playerConfidence;
  @ColumnInfo(name: 'file_path')
  String? filePath;
  @ColumnInfo(name: 'newwork_file_path')
  String? newworkFilePath;
  @ColumnInfo(name: 'start_time')
  String? startTime;
  @ColumnInfo(name: 'shoot_time')
  double? shootTime;
  @ColumnInfo(name: 'training_id')
  String? trainingId;
  @ColumnInfo(name: 'user_id')
  String? userId;
  @primaryKey
  @ColumnInfo(name: 'id')
  String? eventId;
  @ColumnInfo(name: 'is_goal')
  bool? isGoal;
  @ColumnInfo(name: 'player_image_path')
  String? playerImagePath;
  @ColumnInfo(name: 'goal_time')
  double? goalTime;
  @ColumnInfo(name: 'shoot_coord')
  @TypeConverters([DoubleListConverter])
  List<double?>? shootCoord;
  @ColumnInfo(name: 'img_load_ok')
  String? imgLoadOK;
  @ColumnInfo(name: 'video_load_ok')
  String? videoLoadOK;
  @ColumnInfo(name: 'created_at')
  double? createdAt;
  @ColumnInfo(name: 'type')
  String? type; //0单人 1多人
  @ColumnInfo(name: 'is_check')
  String? isCheck;
  @ColumnInfo(name: 'venue_id')
  int? venueId;
  @ColumnInfo(name: 'venue_name')
  String? venueName;

  ShotRecordModel(
      {this.playerConfidence,
      this.filePath,
      this.startTime,
      this.shootTime,
      this.trainingId,
      this.eventId,
      this.isGoal,
      this.type,
      this.playerImagePath,
      this.goalTime,
      this.shootCoord,
      this.imgLoadOK,
      this.videoLoadOK,
      this.newworkFilePath,
      this.createdAt,
      this.isCheck,
      this.venueName,
      this.venueId,
      this.userId});
  ShotRecordModel.fromJson(Map<String, dynamic> json) {
    playerConfidence = json['player_confidence']?.toDouble();
    filePath = json['file_path']?.toString();
    userId = json['user_id']?.toString();
    startTime = json['start_time']?.toString();
    newworkFilePath = json['newwork_file_path']?.toString();
    shootTime = json['shoot_time']?.toDouble();
    trainingId = json['training_id']?.toString();
    eventId = json['id']?.toString();
    isGoal = json['is_goal'];
    isCheck = json['is_check'];
    playerImagePath = json['player_image_path']?.toString();
    venueName = json['venue_name']?.toString();
    venueId = json['venue_id']?.toInt();
    goalTime = json['goal_time']?.toDouble();
    if (json['shoot_coord'] != null) {
      final v = json['shoot_coord'];
      final arr0 = <double>[];
      v.forEach((v) {
        arr0.add(v.toDouble());
      });
      shootCoord = arr0;
    }
    imgLoadOK = json['img_load_ok']?.toString();
    type = json['type']?.toString();
    videoLoadOK = json['video_load_ok']?.toString();
    createdAt = json['created_at']?.toDouble();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['player_confidence'] = playerConfidence;
    data['file_path'] = filePath;
    data['venue_id'] = venueId;
    data['venue_name'] = venueName;
    data['start_time'] = startTime;
    data['shoot_time'] = shootTime;
    data['training_id'] = trainingId;
    data['newwork_file_path'] = newworkFilePath;
    data['id'] = eventId;
    data['is_check'] = isCheck;
    data['is_goal'] = isGoal;
    data['type'] = type;
    data['user_id'] = userId;
    data['player_image_path'] = playerImagePath;
    data['goal_time'] = goalTime;
    if (shootCoord != null) {
      final v = shootCoord;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['shoot_coord'] = arr0;
    }
    data['img_load_ok'] = imgLoadOK;
    data['video_load_ok'] = videoLoadOK;
    data['created_at'] = createdAt;
    return data;
  }

  ShotRecordModel copyWith({
    double? playerConfidence,
    String? filePath,
    String? newworkFilePath,
    String? startTime,
    double? shootTime,
    String? trainingId,
    String? userId,
    String? eventId,
    bool? isGoal,
    String? playerImagePath,
    double? goalTime,
    List<double?>? shootCoord,
    String? imgLoadOK,
    String? videoLoadOK,
    double? createdAt,
    String? type,
    String? isCheck,
    int? venueId,
    String? venueName,
  }) {
    return ShotRecordModel(
      playerConfidence: playerConfidence ?? this.playerConfidence,
      filePath: filePath ?? this.filePath,
      newworkFilePath: newworkFilePath ?? this.newworkFilePath,
      startTime: startTime ?? this.startTime,
      shootTime: shootTime ?? this.shootTime,
      trainingId: trainingId ?? this.trainingId,
      userId: userId ?? this.userId,
      eventId: eventId ?? this.eventId,
      isGoal: isGoal ?? this.isGoal,
      playerImagePath: playerImagePath ?? this.playerImagePath,
      goalTime: goalTime ?? this.goalTime,
      shootCoord: shootCoord ??
          (this.shootCoord != null
              ? List<double?>.from(this.shootCoord!)
              : null),
      imgLoadOK: imgLoadOK ?? this.imgLoadOK,
      videoLoadOK: videoLoadOK ?? this.videoLoadOK,
      createdAt: createdAt ?? this.createdAt,
      type: type ?? this.type,
      isCheck: isCheck ?? this.isCheck,
      venueId: venueId ?? this.venueId,
      venueName: venueName ?? this.venueName,
    );
  }
}

class DoubleListConverter extends TypeConverter<List<double?>?, String?> {
  @override
  List<double?>? decode(String? databaseValue) {
    if (databaseValue == null) return null;

    final list = jsonDecode(databaseValue) as List<dynamic>;
    return list.map((e) {
      if (e == null) return null;
      return (e as num).toDouble();
    }).toList();
  }

  @override
  String? encode(List<double?>? value) {
    if (value == null) return null;
    return jsonEncode(value);
  }
}
