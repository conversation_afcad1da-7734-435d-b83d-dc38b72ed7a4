import 'package:floor/floor.dart';

/// 云台录制事件记录模型
///
/// 对应数据库表名: 'YuntaiRecordEventModel'
///
/// 用于存储通过云台摄像头自动识别并录制的训练片段，
/// 包括视频路径、时间范围、识别类型（如动作类型）、创建时间等。
///
/// 📌 注意：
/// - 所有数据库字段必须通过 getter 显式声明（即使有 backing field）
/// - 使用 @ColumnInfo 映射数据库列名（snake_case → camelCase）
/// - 主键 `id` 支持自增，插入时可设为 null
///
@Entity(tableName: 'YuntaiRecordModel')
class YuntaiRecordModel {
  /// 主键 ID，自增。
  /// 插入数据库时可设为 null，由数据库自动生成。
  @PrimaryKey(autoGenerate: true)
  final int? id;

  /// 训练类型（动作类型）置信度或分类 ID
  /// 对应数据库列: train_type
  @ColumnInfo(name: 'train_type')
  final int? trainType;

  /// 比赛/训练会话 ID
  /// 对应数据库列: match_id
  @ColumnInfo(name: 'match_id')
  final String? matchId;

  /// 远程视频路径（如服务器 URL 或 NAS 路径）
  /// 对应数据库列: video_path
  @ColumnInfo(name: 'video_path')
  final String? videoPath;

  /// 本地缓存路径（如 /cache/xxx.mp4）
  /// 对应数据库列: local_path
  @ColumnInfo(name: 'local_path')
  final String? localPath;

  /// 视频片段开始时间（ISO8601 格式字符串 或 时间戳）
  /// 对应数据库列: start_time
  @ColumnInfo(name: 'start_time')
  final String? startTime;

  /// 视频片段结束时间
  /// 对应数据库列: end_time
  @ColumnInfo(name: 'end_time')
  final String? endTime;

  /// 记录创建时间（本地时间）
  /// 对应数据库列: create_time
  @ColumnInfo(name: 'create_time')
  final String? createTime;

  /// 备注信息（如用户添加的标签、描述）
  /// 对应数据库列: remark
  final String? remark;

  /// 构造函数
  YuntaiRecordModel({
    this.id,
    this.trainType,
    this.matchId,
    this.videoPath,
    this.localPath,
    this.startTime,
    this.endTime,
    this.createTime,
    this.remark,
  });

  /// 从 JSON 反序列化
  /// 通常用于网络请求返回数据 → 数据库模型
  factory YuntaiRecordModel.fromJson(Map<String, dynamic> json) {
    return YuntaiRecordModel(
      id: json['id']?.toInt(),
      trainType: json['train_type']?.toInt(), // 注意：JSON 中是 train_type
      matchId: json['match_id']?.toString(),
      videoPath: json['video_path']?.toString(),
      localPath: json['local_path']?.toString(),
      startTime: json['start_time']?.toString(),
      endTime: json['end_time']?.toString(),
      createTime: json['create_time']?.toString(),
      remark: json['remark']?.toString(),
    );
  }

  /// 序列化为 JSON
  /// 通常用于上传数据或调试
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['train_type'] = trainType; // 注意：JSON 中是 train_type
    data['match_id'] = matchId;
    data['video_path'] = videoPath;
    data['local_path'] = localPath;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    data['create_time'] = createTime;
    data['remark'] = remark;
    return data;
  }

  /// 重写 toString 方法，便于调试
  @override
  String toString() {
    return 'YuntaiRecordModel{id: $id, trainType: $trainType, matchId: $matchId, '
        'videoPath: $videoPath, localPath: $localPath, startTime: $startTime, '
        'endTime: $endTime, createTime: $createTime, remark: $remark}';
  }

  /// 重写 == 操作符（可选，用于比较）
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is YuntaiRecordModel && other.id == id;
  }

  /// 重写 hashCode（如果重写了 ==，必须重写 hashCode）
  @override
  int get hashCode => id.hashCode;
}
