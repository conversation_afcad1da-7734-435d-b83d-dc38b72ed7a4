import 'package:floor/floor.dart';
import 'package:shoot_z/converter/OptionGoalModelCamerasConverter.dart';
import 'package:shoot_z/converter/OptionGoalModelOtherVideosListConverter.dart';

class OptionGoalModelCameras {
/*
{
  "camerasIndex": 0,
  "camerasName": "侧面"
} 
*/

  int? cameraIndex;
  String? cameraName;

  OptionGoalModelCameras({
    this.cameraIndex,
    this.cameraName,
  });
  OptionGoalModelCameras.fromJson(Map<String, dynamic> json) {
    cameraIndex = json['cameraIndex']?.toInt();
    cameraName = json['cameraName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['cameraIndex'] = cameraIndex;
    data['cameraName'] = cameraName;
    return data;
  }
}

class OptionGoalModelOtherVideos {
/*
{
  "pid": "0",
  "id": "379855502",
  "videoPath": "https://cdn.shootz.tech/202412181611/c58d61d0f92c799c756abed983a02560/algo_prod/highlights/200/2024-12-15/21-30-00/goal-video_200_2024-12-15_21-54-15_0.mp4",
  "videoTime": "21:54:15",
  "videoDate": "2024-12-15T00:00:00+08:00",
  "videoDateTime": "2024-12-15T00:00:00+08:00 21:54:15",
  "cameraIndex": 0,
  "selected": false,
  "duration": 10
} 
*/

  String? pid;
  String? id;
  String? videoPath;
  String? videoTime;
  String? videoDate;
  String? videoDateTime;
  int? cameraIndex;
  bool? selected;
  int? duration;

  OptionGoalModelOtherVideos({
    this.pid,
    this.id,
    this.videoPath,
    this.videoTime,
    this.videoDate,
    this.videoDateTime,
    this.cameraIndex,
    this.selected,
    this.duration,
  });
  OptionGoalModelOtherVideos.fromJson(Map<String, dynamic> json) {
    pid = json['pid']?.toString();
    id = json['id']?.toString();
    videoPath = json['videoPath']?.toString();
    videoTime = json['videoTime']?.toString();
    videoDate = json['videoDate']?.toString();
    videoDateTime = json['videoDateTime']?.toString();
    cameraIndex = json['cameraIndex']?.toInt();
    selected = json['selected'];
    duration = json['duration']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['pid'] = pid;
    data['id'] = id;
    data['videoPath'] = videoPath;
    data['videoTime'] = videoTime;
    data['videoDate'] = videoDate;
    data['videoDateTime'] = videoDateTime;
    data['cameraIndex'] = cameraIndex;
    data['selected'] = selected;
    data['duration'] = duration;
    return data;
  }
}

@Entity(tableName: 'OptionGoalModel')
class OptionGoalModel {
/*
{
  "id": "379855502",
  "videoPath": "https://cdn.shootz.tech/202412181611/c58d61d0f92c799c756abed983a02560/algo_prod/highlights/200/2024-12-15/21-30-00/goal-video_200_2024-12-15_21-54-15_0.mp4",
  "videoTime": "21:54:15",
  "videoDate": "2024-12-15T00:00:00+08:00",
  "videoDateTime": "2024-12-15T00:00:00+08:00 21:54:15",
  "cameraIndex": 0,
  "selected": false,
  "otherVideos": [
    {
      "pid": "0",
      "id": "379855502",
      "videoPath": "https://cdn.shootz.tech/202412181611/c58d61d0f92c799c756abed983a02560/algo_prod/highlights/200/2024-12-15/21-30-00/goal-video_200_2024-12-15_21-54-15_0.mp4",
      "videoTime": "21:54:15",
      "videoDate": "2024-12-15T00:00:00+08:00",
      "videoDateTime": "2024-12-15T00:00:00+08:00 21:54:15",
      "cameraIndex": 0,
      "selected": false,
      "duration": 10
    }
  ],
  "duration": 10,
  "cameraMap": {
    "0": "侧面"
  }
} 
*/
  @primaryKey
  String? id;
  int? videoDateTimeStr;
  String? arenaID;
  String? videoPath;
  String? videoTime;
  String? videoDate;
  String? userId;
  String? videoDateTime;
  int? cameraIndex;
  bool? selected;
  bool? used;
  @TypeConverters([OptionGoalModelOtherVideosListConverter])
  List<OptionGoalModelOtherVideos?>? otherVideos;
  int? duration;
  @TypeConverters([OptionGoalModelCamerasConverter])
  List<OptionGoalModelCameras?>? cameras;

  OptionGoalModel({
    this.id,
    this.videoDateTimeStr,
    this.videoPath,
    this.videoTime,
    this.videoDate,
    this.videoDateTime,
    this.cameraIndex,
    this.selected,
    this.otherVideos,
    this.duration,
    this.userId,
    this.arenaID,
    this.cameras,
    this.used,
  });
  //   // 必须提供无参数构造函数以供序列化使用
  OptionGoalModel.empty()
      : this(
            id: null,
            cameraIndex: 0,
            duration: 0,
            otherVideos: [],
            userId: '',
            selected: false,
            videoTime: null,
            videoPath: null,
            videoDate: null,
            videoDateTime: null,
            arenaID: '',
            used: false);
  OptionGoalModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    videoPath = json['videoPath']?.toString();
    videoTime = json['videoTime']?.toString();
    videoDate = json['videoDate']?.toString();
    arenaID = json['arenaID']?.toString();
    userId = json['userId']?.toString();
    videoDateTime = json['videoDateTime']?.toString();
    videoDateTimeStr = json['videoDateTimeStr']?.toInt();
    cameraIndex = json['cameraIndex']?.toInt();
    selected = json['selected'];
    used = json['used'];
    if (json['otherVideos'] != null) {
      final v = json['otherVideos'];
      final arr0 = <OptionGoalModelOtherVideos>[];
      v.forEach((v) {
        arr0.add(OptionGoalModelOtherVideos.fromJson(v));
      });
      otherVideos = arr0;
    }
    duration = json['duration']?.toInt();
    if (json['cameras'] != null) {
      final v = json['cameras'];
      final arr0 = <OptionGoalModelCameras>[];
      v.forEach((v) {
        arr0.add(OptionGoalModelCameras.fromJson(v));
      });
      cameras = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['videoPath'] = videoPath;
    data['videoTime'] = videoTime;
    data['videoDate'] = videoDate;
    data['videoDateTime'] = videoDateTime;
    data['videoDateTimeStr'] = videoDateTimeStr;
    data['cameraIndex'] = cameraIndex;
    data['selected'] = selected;
    data['userId'] = userId;
    data['arenaID'] = arenaID;
    data['used'] = used;
    if (otherVideos != null) {
      final v = otherVideos;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['otherVideos'] = arr0;
    }
    data['duration'] = duration;
    if (cameras != null) {
      final v = cameras;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['cameras'] = arr0;
    }
    return data;
  }
}
