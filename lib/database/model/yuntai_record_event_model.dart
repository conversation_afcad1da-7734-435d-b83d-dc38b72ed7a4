import 'package:floor/floor.dart';

/// 标记该类为数据库表实体
@Entity(tableName: 'YuntaiRecordEventModel')
class YuntaiRecordEventModel {
  /// 主键，支持自增
  @PrimaryKey(autoGenerate: true)
  final int? id;

  @ColumnInfo(name: 'event_id')
  final int? eventId;

  @ColumnInfo(name: 'event_type')
  final int? eventType;

  @ColumnInfo(name: 'event_timstamp')
  final int? eventTimstamp;

  @ColumnInfo(name: 'event_score')
  final String? eventScore;

  @ColumnInfo(name: 'created_time')
  final String? createdTime;

  @ColumnInfo(name: 'local_video_path')
  final String? localVideoPath;

  @ColumnInfo(name: 'video_path')
  final String? videoPath;

  @ColumnInfo(name: 'local_image_path')
  final String? localImagePath;

  @ColumnInfo(name: 'image_path')
  final String? imagePath;

  @ColumnInfo(name: 'train_id')
  final int? trainId;

  final String? remark;

  // 构造函数：添加 trainId
  YuntaiRecordEventModel({
    this.id,
    this.eventId,
    this.eventType,
    this.eventTimstamp,
    this.eventScore,
    this.createdTime,
    this.localVideoPath,
    this.videoPath,
    this.localImagePath,
    this.imagePath,
    this.trainId, // 新增
    this.remark,
  });

  // 从 JSON 创建对象：添加 train_id 解析
  YuntaiRecordEventModel.fromJson(Map<String, dynamic> json)
      : id = json['id']?.toInt(),
        eventId = json['event_id']?.toInt(),
        eventType = json['event_type']?.toInt(),
        eventTimstamp = json['event_timstamp']?.toInt(),
        eventScore = json['event_score']?.toString(),
        createdTime = json['created_time']?.toString(),
        localVideoPath = json['local_video_path']?.toString(),
        videoPath = json['video_path']?.toString(),
        localImagePath = json['local_image_path']?.toString(),
        imagePath = json['image_path']?.toString(),
        trainId = json['train_id']?.toInt(), // 新增
        remark = json['remark']?.toString();

  // 转为 JSON：添加 train_id 输出
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['event_id'] = eventId;
    data['event_type'] = eventType;
    data['event_timstamp'] = eventTimstamp;
    data['event_score'] = eventScore;
    data['created_time'] = createdTime;
    data['local_video_path'] = localVideoPath;
    data['video_path'] = videoPath;
    data['local_image_path'] = localImagePath;
    data['image_path'] = imagePath;
    data['train_id'] = trainId; // 新增
    data['remark'] = remark;
    return data;
  }
}
