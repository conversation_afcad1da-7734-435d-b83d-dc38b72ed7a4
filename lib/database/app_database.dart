import 'dart:async';

import 'package:floor/floor.dart';
import 'package:shoot_z/converter/OptionGoalModelCamerasConverter.dart';
import 'package:shoot_z/converter/OptionGoalModelOtherVideosListConverter.dart';
import 'package:shoot_z/database/dao/OptionGoalDao.dart';
import 'package:shoot_z/database/dao/SelfieShotDao.dart';
import 'package:shoot_z/database/dao/YuntaiRecordDao.dart';
import 'package:shoot_z/database/dao/YuntaiRecordEventDao.dart';
import 'package:shoot_z/database/model/option_goal_model.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';
import 'package:shoot_z/database/model/yuntai_record_event_model.dart';
import 'package:shoot_z/database/model/yuntai_record_model.dart';
import 'package:sqflite/sqflite.dart' as sqflite;
part 'app_database.g.dart';

//flutter pub run build_runner build --delete-conflicting-outputs
@Database(
    version: 3, //原有线上数据库版本 1
    entities: [
      OptionGoalModel,
      ShotRecordModel,   
      YuntaiRecordEventModel,
      YuntaiRecordModel
    ]) //, views: [OptionGoalDao]
abstract class AppDatabase extends FloorDatabase {
  OptionGoalDao get optionGoalDao;
  SelfieShotDao get selfieShotDao;
  YuntaiRecordDao get yuntaiRecordDao;
  YuntaiRecordEventDao get yuntaiRecordEventDao;
}
