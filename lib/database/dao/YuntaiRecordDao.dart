// dao/yuntai_record_dao.dart
import 'package:floor/floor.dart';
import 'package:shoot_z/database/model/yuntai_record_model.dart';
import 'package:intl/intl.dart';

@dao
abstract class YuntaiRecordDao {
  /// 查询某个 matchId 下的所有记录，按开始时间升序排列
  @Query(
      'SELECT * FROM YuntaiRecordModel WHERE match_id = :matchId ORDER BY start_time ASC')
  Future<List<YuntaiRecordModel>> findAllByMatchId(String matchId);

  /// 根据 ID 查询单条记录
  @Query('SELECT * FROM YuntaiRecordModel WHERE id = :id')
  Future<YuntaiRecordModel?> findRecordById(int id);

  /// 插入单条记录（冲突时替换）
  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertRecord(YuntaiRecordModel record);

  /// 批量插入记录
  Future<void> insertRecordList(List<YuntaiRecordModel> records) async {
    for (var record in records) {
      await insertRecord(record);
    }
  }

  /// 新增：插入并自动处理时间戳合并（可选）
  /// 假设 startTime 是日期字符串（如 "2025-04-05"），videoTime 是时间字符串（如 "14:30:00"）
  Future<void> insertRecordWithTime(
    YuntaiRecordModel record,
    String videoTime,
  ) async {
    if (record.startTime != null) {
      final baseDate = DateTime.parse(record.startTime!);
      final timeFormat = DateFormat("HH:mm:ss");
      final parsedTime = timeFormat.parse(videoTime, false);
      final combinedDateTime = DateTime(
        baseDate.year,
        baseDate.month,
        baseDate.day,
        parsedTime.hour,
        parsedTime.minute,
        parsedTime.second,
      );
      // 可以选择更新 startTime 为完整时间戳字符串
      record = YuntaiRecordModel(
        id: record.id,
        trainType: record.trainType,
        matchId: record.matchId,
        videoPath: record.videoPath,
        localPath: record.localPath,
        startTime: combinedDateTime.toIso8601String(),
        endTime: record.endTime,
        createTime: record.createTime ?? DateTime.now().toIso8601String(),
        remark: record.remark,
      );
    }
    await insertRecord(record);
  }

  /// 更新记录
  @update
  Future<void> updateRecord(YuntaiRecordModel record);

  /// 删除单条记录
  @delete
  Future<void> deleteRecord(YuntaiRecordModel record);

  /// 删除某个 matchId 下的所有记录
  @Query('DELETE FROM YuntaiRecordModel WHERE match_id = :matchId')
  Future<void> deleteAllByMatchId(String matchId);

  /// 删除所有本地缓存记录（可选：根据 localPath 是否为空判断）
  @Query('DELETE FROM YuntaiRecordModel WHERE local_path IS NOT NULL')
  Future<void> deleteAllLocalRecords();

  /// 删除所有数据（谨慎使用）
  @Query('DELETE FROM YuntaiRecordModel')
  Future<void> deleteAllRecords();
}
