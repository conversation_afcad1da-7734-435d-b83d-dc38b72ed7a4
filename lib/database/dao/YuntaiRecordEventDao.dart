// dao/yuntai_record_event_dao.dart
import 'package:floor/floor.dart';
import 'package:shoot_z/database/model/yuntai_record_event_model.dart';

@dao
abstract class YuntaiRecordEventDao {
  /// 查询某个 trainId 下的所有事件，按时间戳升序排列
  @Query(
      'SELECT * FROM YuntaiRecordEventModel WHERE train_id = :trainId ORDER BY event_timstamp ASC')
  Future<List<YuntaiRecordEventModel>> findAllByTrainId(int trainId);

  /// 根据 ID 查询单条事件
  @Query('SELECT * FROM YuntaiRecordEventModel WHERE id = :id')
  Future<YuntaiRecordEventModel?> findEventById(int id);

  /// 根据 event_type 查询某类事件
  @Query(
      'SELECT * FROM YuntaiRecordEventModel WHERE event_type = :eventType AND train_id = :trainId')
  Future<List<YuntaiRecordEventModel>> findEventsByType(
      int eventType, int trainId);

  /// 插入单条事件（冲突时替换）
  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertEvent(YuntaiRecordEventModel event);

  /// 批量插入事件
  Future<void> insertEventList(List<YuntaiRecordEventModel> events) async {
    for (var event in events) {
      await insertEvent(event);
    }
  }

  /// 新增：插入事件并自动设置时间戳和创建时间
  Future<void> insertEventWithAutoTime(
    YuntaiRecordEventModel event,
    int timestamp,
  ) async {
    final updatedEvent = YuntaiRecordEventModel(
      id: event.id,
      eventId: event.eventId,
      eventType: event.eventType,
      eventTimstamp: timestamp,
      eventScore: event.eventScore,
      createdTime: DateTime.now().toIso8601String(), // 自动设置创建时间
      localVideoPath: event.localVideoPath,
      videoPath: event.videoPath,
      localImagePath: event.localImagePath,
      imagePath: event.imagePath,
      trainId: event.trainId,
      remark: event.remark,
    );
    await insertEvent(updatedEvent);
  }

  /// 更新事件
  @update
  Future<void> updateEvent(YuntaiRecordEventModel event);

  /// 删除单条事件
  @delete
  Future<void> deleteEvent(YuntaiRecordEventModel event);

  /// 删除某个 trainId 的所有事件
  @Query('DELETE FROM YuntaiRecordEventModel WHERE train_id = :trainId')
  Future<void> deleteAllByTrainId(int trainId);

  /// 删除所有事件（谨慎使用）
  @Query('DELETE FROM YuntaiRecordEventModel')
  Future<void> deleteAllEvents();
}
