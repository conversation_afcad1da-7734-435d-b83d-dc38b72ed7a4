// dao/person_dao.dart

import 'dart:convert';
import 'dart:developer';

import 'package:floor/floor.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';

//半场投篮数据库  运行命令：  flutter packages pub run build_runner build
@dao
abstract class SelfieShotDao {
  @Query(
      'SELECT * FROM ShotRecordModel WHERE training_id = :trainingId AND user_id = :userId AND type = :type ORDER BY shoot_time ASC')
  Future<List<ShotRecordModel>> findAllShot(
      String trainingId, String userId, String type);

  // // 查询每个比赛的最新记录，支持类型筛选、分页和数量控制
  // @Query('''
  //   WITH TrainingGroups AS (
  //     SELECT training_id
  //     FROM ShotRecordModel
  //     WHERE user_id = :userId
  //     AND ('' = :type OR type = :type)
  //     GROUP BY training_id
  //     ORDER BY MAX(shoot_time) DESC
  //     LIMIT :pageSize OFFSET :page
  //   ),
  //   RankedRecords AS (
  //     SELECT
  //       s.*,
  //       ROW_NUMBER() OVER (
  //         PARTITION BY s.training_id
  //         ORDER BY s.shoot_time DESC, s.id DESC
  //       ) AS row_num
  //     FROM ShotRecordModel s
  //     JOIN TrainingGroups tg ON s.training_id = tg.training_id
  //     WHERE s.user_id = :userId
  //   )
  //   SELECT * FROM RankedRecords
  //   WHERE row_num <= :limit
  // ''')
  // Future<List<ShotRecordModel>> findPagedTrainingRecords(
  //     String userId,
  //     String type, // "" 表示所有类型，"1": 单人，"2": 双人
  //     int limit,
  //     int pageSize,
  //     int page);
  // 查询每个比赛的最新记录，支持类型筛选、分页和数量控制
  @Query('''
    WITH TrainingGroups AS (
      SELECT training_id
      FROM ShotRecordModel
      WHERE user_id = :userId
      AND ('' = :type OR type = :type)
      GROUP BY training_id
      ORDER BY MAX(shoot_time) ASC
      LIMIT :pageSize OFFSET :page
    ),
    RankedRecords AS (
      SELECT 
        s.*,
        ROW_NUMBER() OVER (
          PARTITION BY s.training_id 
          ORDER BY s.shoot_time ASC, s.id ASC
        ) AS row_num
      FROM ShotRecordModel s
      JOIN TrainingGroups tg ON s.training_id = tg.training_id
      WHERE s.user_id = :userId
    )
    SELECT * FROM RankedRecords 
    WHERE row_num <= :limit
  ''')
  Future<List<ShotRecordModel>> findPagedTrainingRecords(
      String userId,
      String type, // "" 表示所有类型，"1": 单人，"2": 双人
      int limit,
      int pageSize,
      int page);

  @Query('''
  WITH RankedRecords AS (
    SELECT 
      s.*,
      ROW_NUMBER() OVER (
        PARTITION BY s.training_id 
        ORDER BY s.shoot_time DESC, s.id DESC
      ) AS row_num
    FROM ShotRecordModel s
    WHERE s.user_id = :userId
      AND ('' = :type OR s.type = :type)
  )
  SELECT * FROM RankedRecords
  WHERE row_num <= :perGroupLimit  -- 每个training_id最多取4条
  ORDER BY training_id DESC, shoot_time DESC  -- 整体按training_id降序
''')
  Future<List<ShotRecordModel>> findGroupedTrainings(
    String userId,
    String type,
    int perGroupLimit, // 每组最多取多少条（如4）
  );

  // 查询每个比赛的最新记录，支持类型筛选、分页和数量控制
  @Query('''
    WITH TrainingGroups AS (
      SELECT training_id
      FROM ShotRecordModel
      WHERE user_id = :userId 
      AND ('' = :type OR type = :type)
      AND (
      (:venueId = 0 AND venue_id = 0)
      OR
      (:venueId != 0 AND venue_id = :venueId)
    )
      GROUP BY training_id
      ORDER BY MAX(shoot_time) ASC
      LIMIT :pageSize OFFSET :page
    ),
    RankedRecords AS (
      SELECT 
        s.*,
        ROW_NUMBER() OVER (
          PARTITION BY s.training_id 
          ORDER BY s.shoot_time ASC, s.id ASC
        ) AS row_num
      FROM ShotRecordModel s
      JOIN TrainingGroups tg ON s.training_id = tg.training_id
      WHERE s.user_id = :userId
    )
    SELECT * FROM RankedRecords 
    WHERE row_num <= :limit
  ''')
  Future<List<ShotRecordModel>> findPagedTrainingRecordVenueId(
      String userId,
      int venueId,
      String type, // "" 表示所有类型，"1": 单人，"2": 双人
      int limit,
      int pageSize,
      int page);

  // @Query(
  //     'SELECT * FROM ShotRecordModel WHERE trainingId = :trainingId AND eventId = :eventId AND userId = :userId')
  // Future<ShotRecordModel> findShotById(
  //     String trainingId, String eventId, String userId);

  // @Query('SELECT name FROM OptionGoalModel')
  // Stream<List<String>> findAllPeopleName();

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertShot2(ShotRecordModel shotRecordModel);

  // 插入多个书籍的便捷方法
  Future<void> insertShotList(
      List<ShotRecordModel> datalist, String userId) async {
    for (var shotRecordModel in datalist) {
      shotRecordModel.userId = userId;
      shotRecordModel.eventId =
          "${shotRecordModel.trainingId}00${shotRecordModel.eventId}";
      log("filteredNumbers2221:${jsonEncode(shotRecordModel)}");
      await insertShot2(shotRecordModel);
    }
  }

  // 插入多个书籍的便捷方法
  Future<void> insertShot(
      ShotRecordModel shotRecordModel, String userId) async {
    shotRecordModel.userId = userId;
    if (shotRecordModel.eventId?.contains("${shotRecordModel.trainingId}00") ??
        false) {
    } else {
      shotRecordModel.eventId =
          "${shotRecordModel.trainingId}000${shotRecordModel.eventId}";
    }
    await insertShot2(shotRecordModel);
  }

  //删除
  @Query(
      'DELETE FROM ShotRecordModel WHERE training_id = :trainingId AND user_id = :userId')
  Future<void> deleteAll(String trainingId, String userId);

  @Query('DELETE FROM ShotRecordModel WHERE user_id = :userId')
  Future<void> deleteAllUserId(String userId);

  @delete
  Future<void> deleteShot2(ShotRecordModel shotRecordModel);

  @Query(
      'DELETE FROM ShotRecordModel WHERE training_id = :trainingId AND user_id = :userId AND id = :eventId')
  Future<void> deleteShot1(String trainingId, String userId, String eventId);
  // 插入记录
  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertRecord(ShotRecordModel record);

  // 更新记录
  @Update()
  Future<void> updateRecord(ShotRecordModel record);
}
