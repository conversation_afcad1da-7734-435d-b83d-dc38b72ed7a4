// // database_helper.dart
// import 'package:floor/floor.dart';
// import 'package:shoot_z/database/app_database.dart';
// import 'package:sqflite/sqflite.dart' as sqflite;
// import 'package:path_provider/path_provider.dart' as ccc;
// import 'package:path/path.dart' as p;

// class DatabaseHelper {
//   static final DatabaseHelper _instance = DatabaseHelper._internal();
//   static AppDatabase? _database;

//   DatabaseHelper._internal();

//   factory DatabaseHelper() {
//     return _instance;
//   }
//   // final databasePath = await ccc.getApplicationDocumentsDirectory();
//   //   await $FloorAppDatabase
//   //     .databaseBuilder(p.join(databasePath.path, 'app_database.db'))
//   //     //  .addMigrations(_migration)
//   //     .build();
//   Future<AppDatabase> get database async {
//     if (_database != null) return _database!;
//     final databasePath = await ccc.getApplicationDocumentsDirectory();
//     _database = await $FloorAppDatabase
//         .databaseBuilder(p.join(databasePath.path, 'app_database.db'))
//         .addMigrations([
//       Migration(1, 2, _createShotRecordTable), // 添加迁移
//     ]).build();

//     return _database!;
//   }

//   // 创建 ShotRecordModel 表的迁移函数
//   static Future<void> _createShotRecordTable(sqflite.Database database) async {
//     // 创建新表
//     await database.execute('''
//       CREATE TABLE ShotRecordModel (
//         player_confidence REAL,
//         file_path TEXT,
//         network_file_path TEXT,
//         start_time TEXT,
//         shoot_time REAL,
//         training_id TEXT,
//         user_id TEXT,
//         event_id TEXT PRIMARY KEY,
//         is_goal INTEGER,
//         player_image_path TEXT,
//         goal_time REAL,
//         shoot_coord TEXT,
//         img_load_ok TEXT,
//         video_load_ok TEXT,
//         created_at REAL,
//         type TEXT,
//         venue_name TEXT,
//         venue_id INTEGER
//       )
//     ''');

//     print('ShotRecordModel 表创建成功');
//   }

//   Future<void> close() async {
//     if (_database != null) {
//       await _database!.close();
//       _database = null;
//     }
//   }
// }
// database_helper.dart
import 'package:floor/floor.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:sqflite/sqflite.dart' as sqflite;
import 'package:path_provider/path_provider.dart' as ccc;
import 'package:path/path.dart' as p;

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static AppDatabase? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() {
    return _instance;
  }

  Future<AppDatabase> get database async {
    if (_database != null) return _database!;

    final databasePath = await ccc.getApplicationDocumentsDirectory();
    _database = await $FloorAppDatabase
        .databaseBuilder(p.join(databasePath.path, 'app_database.db'))
        .addMigrations([
      Migration(1, 2, _createShotRecordTable), // v1 → v2: 创建 ShotRecordModel
      Migration(2, 3, _createYuntaiTables), // v2 → v3: 创建两个新表 👈 新增
    ]).build();

    return _database!;
  }

  // --------------------------
  // v1 → v2: 创建 ShotRecordModel 表
  // --------------------------
  static Future<void> _createShotRecordTable(sqflite.Database database) async {
    await database.execute('''
      CREATE TABLE ShotRecordModel (
        player_confidence REAL,
        file_path TEXT,
        network_file_path TEXT,
        start_time TEXT,
        shoot_time REAL,
        training_id TEXT,
        user_id TEXT,
        event_id TEXT PRIMARY KEY,
        is_goal INTEGER,
        player_image_path TEXT,
        goal_time REAL,
        shoot_coord TEXT,
        img_load_ok TEXT,
        video_load_ok TEXT,
        created_at REAL,
        type TEXT,
        venue_name TEXT,
        venue_id INTEGER  
      )
    ''');

    print('ShotRecordModel 表创建成功');
  }

  // --------------------------
  // v2 → v3: 创建 YuntaiRecordModel 和 YuntaiRecordEventModel 表
  // --------------------------
  static Future<void> _createYuntaiTables(sqflite.Database database) async {
    // 创建 YuntaiRecordModel 表
    await database.execute('''
      CREATE TABLE YuntaiRecordModel (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        train_type INTEGER,
        match_id TEXT,
        video_path TEXT,
        local_path TEXT,
        start_time TEXT,
        end_time TEXT,
        create_time TEXT,
        remark TEXT
      )
    ''');

    // 创建 YuntaiRecordEventModel 表
    await database.execute('''
      CREATE TABLE YuntaiRecordEventModel (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        event_id INTEGER,
        event_type INTEGER,
        event_timstamp INTEGER,
        event_score TEXT,
        created_time TEXT,
        local_video_path TEXT,
        video_path TEXT,
        local_image_path TEXT,
        image_path TEXT,
        train_id INTEGER,
        remark TEXT
      )
    ''');

    print('YuntaiRecordModel 和 YuntaiRecordEventModel 表创建成功');
  }

  // --------------------------
  // 关闭数据库
  // --------------------------
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }
}
