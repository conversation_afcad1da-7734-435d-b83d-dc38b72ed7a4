// ignore_for_file: invalid_use_of_protected_member

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab5Mine/vip/logic.dart';
import 'package:ui_packages/ui_packages.dart';
import 'logic.dart';

class SearchPlacePage extends StatefulWidget {
  const SearchPlacePage({super.key});

  @override
  State<SearchPlacePage> createState() => _SearchPlacePageState();
}

class _SearchPlacePageState extends State<SearchPlacePage> {
  final logic = Get.put(SearchPlaceLogic());
  final state = Get.find<SearchPlaceLogic>().state;
  final vipLogic = Get.find<VipLogic>();
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    Get.delete<SearchPlaceLogic>();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        bottom: false,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _search(context),
            Padding(
              padding: const EdgeInsets.only(left: 20, top: 25, bottom: 20),
              child: Text(
                '选择您想开通VIP的球馆',
                style: TextStyles.regular.copyWith(color: Colours.color9393A5),
              ),
            ),
            Expanded(
                child: Obx(
              () => !state.init.value
                  ? const Center(
                      child: CupertinoActivityIndicator(
                      color: Colors.white,
                    ))
                  : _list(context),
            )),
          ],
        ),
      ),
    );
  }

  Widget _search(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 20, right: 20),
      child: Row(
        children: [
          Expanded(
              child: Container(
            height: 42,
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(21),
            ),
            child: Row(
              children: [
                const SizedBox(
                  width: 14,
                ),
                WxAssets.images.icSearch.image(),
                const SizedBox(
                  width: 10,
                ),
                Expanded(
                    child: TextField(
                  autocorrect: false,
                  controller: state.controller,
                  keyboardType: TextInputType.text,
                  style: TextStyles.display14,
                  maxLines: 1,
                  // onChanged: logic.onTextChanged,
                  decoration: InputDecoration(
                    // isDense: true,//isDense 为 true 会让 TextField 的高度变紧凑，同时调整光标和文本的位置。
                    contentPadding: const EdgeInsets.only(top: 0, bottom: 5),
                    border: InputBorder.none,
                    hintText: "请输入要查找的球场名称",
                    hintStyle: TextStyles.display14
                        .copyWith(color: Colours.color5C5C6E),
                  ),
                  textInputAction: TextInputAction.search,
                  onSubmitted: (value) {
                    logic.requestPlace();
                  },
                  onChanged: (value) => state.text.value = value,
                )),
                GestureDetector(
                    onTap: () {
                      state.controller.text = '';
                      state.text.value = '';
                    },
                    child: Obx(() => Visibility(
                        visible: state.text.isNotEmpty,
                        child: WxAssets.images.icSearchDelete.image()))),
                const SizedBox(
                  width: 12,
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _list(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.only(
            top: 0, bottom: MediaQuery.of(context).padding.bottom),
        itemCount: state.list.isEmpty
            ? 1
            : (state.controller.text.isEmpty
                ? state.list.length + 1
                : state.list.length),
        itemBuilder: (context, index) {
          if (state.list.isNotEmpty) {
            if (index == state.list.length) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  WxAssets.images.tips.image(width: 11.5.w, fit: BoxFit.fill),
                  SizedBox(
                    width: 4.w,
                  ),
                  Text(
                    '距您15km以外的球馆请在顶部搜索栏进行搜索',
                    style: TextStyles.regular
                        .copyWith(fontSize: 10.sp, color: Colours.color5C5C6E),
                  )
                ],
              );
            }
            return _modelItem(context, index);
          }
          return _empty();
        });
  }

  Widget _modelItem(BuildContext context, int index) {
    final model = state.list.value[index];
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => vipLogic.selPlace(model),
      child: Container(
        margin: const EdgeInsets.only(left: 20, right: 20, bottom: 15),
        padding: EdgeInsets.all(10.w),
        decoration: BoxDecoration(
          color: Colours.color22222D,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(children: [
                CachedNetworkImage(
                  imageUrl: model.logo ?? '',
                  width: 86.w,
                  height: 86.w,
                  fit: BoxFit.cover,
                ),
                if ((model.type ?? 0) > 0)
                  Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        height: 25.w,
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(top: 4.w),
                        decoration: BoxDecoration(
                          image: DecorationImage(
                              image: WxAssets.images.icHlItemBottom.provider(),
                              fit: BoxFit.fill),
                        ),
                        child: Text(
                          '',
                          style: TextStyles.display10,
                        ),
                      )),
              ]),
            ),
            SizedBox(
              width: 15.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    model.arenaName ?? '',
                    style: TextStyles.semiBold,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(
                    height: 14.w,
                  ),
                  Row(
                    children: [
                      WxAssets.images.icLocation.image(width: 12, height: 12),
                      SizedBox(
                        width: 3.w,
                      ),
                      Expanded(
                        child: Text(
                          model.address ?? '',
                          style: TextStyles.regular.copyWith(
                              fontSize: 12.sp, color: Colours.color5C5C6E),
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 14.w,
                  ),
                  Text(
                    "距你${model.distance}km",
                    style:
                        TextStyles.regular.copyWith(color: Colours.colorA44EFF),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _empty() {
    return Container(
      padding: const EdgeInsets.only(top: 80),
      child: Column(
        children: [
          WxAssets.images.icSearchNo.image(width: 152.w, height: 155.w),
          const SizedBox(
            height: 30,
          ),
          Text(
            "暂无搜索结果",
            style: TextStyles.display16,
          )
        ],
      ),
    );
  }
}
