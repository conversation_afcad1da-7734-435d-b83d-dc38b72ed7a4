import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab1Home/highlights/models/highlights_model.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/model/venue_highlights_model.dart';
import 'package:shoot_z/utils/event_bus.dart';

class MyHighlightsLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  late TabController tabController;
  var currentTabIndex = 0.obs;
  var tabList = [
    {'title': '全部', 'id': '0'},
    {'title': '报名中', 'id': '1'},
    {'title': '待开赛', 'id': '2'},
    {'title': '进行中', 'id': '3'},
    {'title': '已结束', 'id': '4'},
  ];
  var highlightsTypeList = [
    {'title': '场馆端集锦', 'id': '0'},
    {'title': '半场投篮集锦', 'id': '1'},
  ];
  var currentTypeIndex = 0.obs;

  /// 是否正在加载数据
  bool _isLoading = false;
  var init = false.obs;
  var page = 1;
  var pageSize = 10;
  var totalRows = 0;
  var totalCount = 0.obs;
  var totalCount1 = 0.obs;
  var userId = "";
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  //数据列表
  var dataList = <HighlightsModel>[].obs;
  var venueDataList = <VenueHighlightsModel>[].obs;
  StreamSubscription? subscription;
  @override
  void onInit() {
    super.onInit();
    userId = UserManager.instance.user?.userId ?? "";
    if (Get.arguments != null && Get.arguments.containsKey('userId')) {
      userId = Get.arguments['userId'];
    }
    subscription = BusUtils.instance.on((action) {
      if (action.key == EventBusKey.changeMyVideo) {
        currentTypeIndex.value = 1;
        page = 1;
        getVenueAchievements(true);
      }
    });
    onRefresh();
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    if (currentTypeIndex.value == 0) {
      await getdataList(false);
    } else {
      await getVenueAchievements(false);
    }
  }

  bool hasMore() {
    if (currentTypeIndex.value == 1) {
      return venueDataList.length < totalRows;
    }
    return dataList.length < totalRows;
  }

  Future<void> onRefresh() async {
    if (_isLoading) {
      return;
    }
    page = 1;
    if (currentTypeIndex.value == 0) {
      await getdataList(true);
    } else {
      await getVenueAchievements(true);
    }

    // init.value = true;
  }

  //获得最新列表
  Future<void> getdataList(bool isRefresh) async {
    init.value = false;
    _isLoading = true;
    WxLoading.show();
    Map<String, dynamic> param = {
      'pageIndex': page,
      'pageSize': pageSize,
      'userId': userId,
    };
    var url = ApiUrl.myHighlights;
    var res = await Api().get(url, queryParameters: param);
    _isLoading = false;
    init.value = true;
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      // if (isRefresh) {
      //   page = 1;
      // } else {
      page += 1;
      // }
      cc.log("result${currentTabIndex.value}$url${res.data}");
      final list = (res.data['result'] as List)
          .map((e) => HighlightsModel.fromJson(e))
          .toList();
      totalRows = res.data["totalRows"];
      totalCount.value = res.data["totalCount"];
      if (isRefresh) {
        dataList.value = list;
      } else {
        dataList.addAll(list);
      }
      dataList.refresh();
    } else {
      if (isRefresh) {
        dataList.value = [];
        totalRows = 0;
      }
      WxLoading.showToast(res.message);
    }
  }

  @override
  void dispose() {
    super.dispose();
    subscription?.cancel();
  }

  //获得最新列表
  Future<void> getVenueAchievements(bool isRefresh) async {
    init.value = false;
    _isLoading = true;
    WxLoading.show();
    Map<String, dynamic> param = {
      'pageIndex': page,
      'pageSize': pageSize,
    };
    var url = ApiUrl.myAchievements;
    var res = await Api().get(url, queryParameters: param);
    _isLoading = false;
    init.value = true;
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      // if (isRefresh) {
      //   page = 1;
      // } else {
      page += 1;
      //  }
      cc.log("result${currentTabIndex.value}$url${res.data}");
      final list = (res.data['result'] as List)
          .map((e) => VenueHighlightsModel.fromJson(e))
          .toList();
      totalRows = res.data["totalRows"];
      totalCount1.value = res.data["totalCount"];
      if (isRefresh) {
        venueDataList.value = list;
      } else {
        venueDataList.addAll(list);
      }
    } else {
      if (isRefresh) {
        venueDataList.value = [];
        totalRows = 0;
      }
      WxLoading.showToast(res.message);
    }
  }
}
