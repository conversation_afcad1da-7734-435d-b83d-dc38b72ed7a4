///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class VenueHighlightsModelItems {
/*
{
  "coverPath": "string",
  "id": "0",
  "title": "string",
  "userAvatar": "string",
  "userId": 0,
  "userName": "string",
  "venueId": "0",
  "venueName": "string",
  "videoPath": "string"
} 
*/

  String? coverPath;
  String? id;
  String? title;
  String? userAvatar;
  int? userId;
  String? userName;
  String? venueId;
  String? venueName;
  String? videoPath;

  VenueHighlightsModelItems({
    this.coverPath,
    this.id,
    this.title,
    this.userAvatar,
    this.userId,
    this.userName,
    this.venueId,
    this.venueName,
    this.videoPath,
  });
  VenueHighlightsModelItems.fromJson(Map<String, dynamic> json) {
    coverPath = json['coverPath']?.toString();
    id = json['id']?.toString();
    title = json['title']?.toString();
    userAvatar = json['userAvatar']?.toString();
    userId = json['userId']?.toInt();
    userName = json['userName']?.toString();
    venueId = json['venueId']?.toString();
    venueName = json['venueName']?.toString();
    videoPath = json['videoPath']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['coverPath'] = coverPath;
    data['id'] = id;
    data['title'] = title;
    data['userAvatar'] = userAvatar;
    data['userId'] = userId;
    data['userName'] = userName;
    data['venueId'] = venueId;
    data['venueName'] = venueName;
    data['videoPath'] = videoPath;
    return data;
  }
}

class VenueHighlightsModel {
/*
{
  "items": [
    {
      "coverPath": "string",
      "id": "0",
      "title": "string",
      "userAvatar": "string",
      "userId": 0,
      "userName": "string",
      "venueId": "0",
      "venueName": "string",
      "videoPath": "string"
    }
  ],
  "mergeTime": "string",
  "userName": "string",
  "venueId": "0",
  "venueName": "string",
  "week": "string"
} 
*/

  List<VenueHighlightsModelItems?>? items;
  String? mergeTime;
  String? userName;
  String? venueId;
  String? venueName;
  String? week;

  VenueHighlightsModel({
    this.items,
    this.mergeTime,
    this.userName,
    this.venueId,
    this.venueName,
    this.week,
  });
  VenueHighlightsModel.fromJson(Map<String, dynamic> json) {
    if (json['items'] != null) {
      final v = json['items'];
      final arr0 = <VenueHighlightsModelItems>[];
      v.forEach((v) {
        arr0.add(VenueHighlightsModelItems.fromJson(v));
      });
      items = arr0;
    }
    mergeTime = json['mergeTime']?.toString();
    userName = json['userName']?.toString();
    venueId = json['venueId']?.toString();
    venueName = json['venueName']?.toString();
    week = json['week']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (items != null) {
      final v = items;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['items'] = arr0;
    }
    data['mergeTime'] = mergeTime;
    data['userName'] = userName;
    data['venueId'] = venueId;
    data['venueName'] = venueName;
    data['week'] = week;
    return data;
  }
}
