import 'dart:developer';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/network/model/invite_mini_path_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/battle_detail_logic.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/contact_publisher_dialog.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/pay/fluwx_utils.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class BattleDetailPage extends StatelessWidget {
  BattleDetailPage({super.key});

  final logic = Get.put(BattleDetailLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text('约战详情'),
        ),
        body: _createDetailWidget(context),
        bottomNavigationBar: Obx(() {
          return UserManager.instance.user?.userId ==
                  logic.challengeModel.value.userId
              ? const SizedBox()
              : Container(
                  width: double.infinity,
                  margin: EdgeInsets.only(
                      left: 15.w,
                      right: 15.w,
                      bottom: ScreenUtil().bottomBarHeight),
                  child: Row(
                    children: [
                      InkWell(
                        onTap: () async {
                          if (Utils.isToLogin()) {
                            //分享到微信
                            InviteMiniPathModel inviteMiniPathModel =
                                InviteMiniPathModel(
                              appRawId: FluwxUtils.miniProgramUserName,
                              appId: FluwxUtils.appId,
                              path:
                                  "pagesMatch/battle/battleDetail?battleId=${logic.challengeModel.value.id}&teamId=${logic.teamId}&battleType=1",
                              image: "",
                              icon: "",
                              title: "快来和我约战吧",
                            );
                            ByteData data = await rootBundle.load(
                                "assets/images/dialog_invitation.png"); //dialog_invitation
                            Uint8List imageBytes = data.buffer.asUint8List();

                            MyShareH5.shareMiniProgram(
                                inviteMiniPathModel, imageBytes);
                          }
                        },
                        child: WxAssets.images.shareSendIcon.image(),
                      ),
                      SizedBox(
                        width: 15.w,
                      ),
                      Expanded(
                          child: InkWell(
                        onTap: () {
                          if (Utils.isToLogin()) {
                            if (logic.challengeModel.value.status == 2) {
                              WxLoading.showToast('约战已结束，无法联系发布者');
                              return;
                            }
                            Get.bottomSheet(
                              ContactPublisherDialog(
                                publisherPhone:
                                    logic.challengeModel.value.phone,
                              ),
                              backgroundColor: Colors.transparent,
                              isScrollControlled: true,
                            );
                          }
                        },
                        child: Container(
                          height: 44.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [
                                  Colours.color7732ED,
                                  Colours.colorA555EF
                                ],
                                begin: Alignment.bottomLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(25.r)),
                          child: Text(
                            '联系发布者',
                            style: TextStyles.semiBold14,
                          ),
                        ),
                      ))
                    ],
                  ),
                );
        }));
  }

  /// 列表数据
  _createDetailWidget(BuildContext context) {
    return Obx(() => Column(
          children: [
            Container(
              padding: EdgeInsets.only(
                  top: 15.w, bottom: 15.w, left: 15.w, right: 15.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      if (Utils.isToLogin()) {
                        AppPage.to(Routes.careerHighlightsHomePage, arguments: {
                          'userId': logic.challengeModel.value.userId
                        });
                      }
                    },
                    child: Row(
                      children: [
                        MyImage(
                          logic.challengeModel.value.avatar ?? "",
                          width: 32.w,
                          height: 32.w,
                          radius: 16.r,
                          isAssetImage: false,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Text(
                          logic.challengeModel.value.userName ?? "",
                          style: TextStyles.titleSemiBold16,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 14.sp,
                          color: Colors.white,
                        )
                      ],
                    ),
                  ),
                  if (UserManager.instance.user?.userId !=
                      logic.challengeModel.value.userId)
                    InkWell(
                      onTap: () {
                        if (Utils.isToLogin()) {
                          if (logic.challengeModel.value.status == 2) {
                            WxLoading.showToast('约战已结束，无法联系发布者');
                            return;
                          }
                          Get.bottomSheet(
                            ContactPublisherDialog(
                              publisherPhone: logic.challengeModel.value.phone,
                            ),
                            backgroundColor: Colors.transparent,
                            isScrollControlled: true,
                          );
                        }
                      },
                      child: Container(
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            border: Border.all(color: Colors.white, width: 1.w),
                            borderRadius: BorderRadius.circular(34.r)),
                        width: 70.w,
                        height: 28.w,
                        child: Text(
                          '联系TA',
                          style: TextStyles.regular
                              .copyWith(color: Colors.white, fontSize: 12.sp),
                        ),
                      ),
                    )
                ],
              ),
            ),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(8.r)),
              padding: EdgeInsets.only(
                  top: 20.w, bottom: 20.w, left: 15.w, right: 15.w),
              child: Column(
                children: [
                  infoWidget('场馆', logic.challengeModel.value.arenaName ?? ""),
                  SizedBox(
                    height: 20.w,
                  ),
                  infoWidget('赛制',
                      logic.challengeModel.value.challengeFormatStr ?? ""),
                  SizedBox(
                    height: 20.w,
                  ),
                  infoWidget('强度',
                      logic.challengeModel.value.challengeStrengthStr ?? ""),
                  SizedBox(
                    height: 20.w,
                  ),
                  infoWidget(
                      '费用', logic.challengeModel.value.challengeCostStr ?? ""),
                  SizedBox(
                    height: 20.w,
                  ),
                  infoWidget('补充', logic.challengeModel.value.remark ?? ""),
                  if (UserManager.instance.user?.userId !=
                      logic.challengeModel.value.userId)
                    Container(
                      margin: EdgeInsets.only(top: 20.w),
                      alignment: Alignment.centerLeft,
                      child: RichText(
                        text: TextSpan(
                          style: TextStyles.regular.copyWith(
                            color: Colours.colorA8A8BC,
                            fontSize: 12.sp,
                          ),
                          children: [
                            const TextSpan(text: '请点击'),
                            TextSpan(
                              text: '【联系发布者】',
                              style: TextStyles.regular.copyWith(
                                color: const Color(0xFFFCE051),
                                fontSize: 12.sp,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  if (Utils.isToLogin()) {
                                    if (logic.challengeModel.value.status ==
                                        2) {
                                      WxLoading.showToast('约战已结束，无法联系发布者');
                                      return;
                                    }
                                    // Show contact publisher bottom sheet
                                    Get.bottomSheet(
                                      ContactPublisherDialog(
                                        publisherPhone:
                                            logic.challengeModel.value.phone,
                                      ),
                                      backgroundColor: Colors.transparent,
                                      isScrollControlled: true,
                                    );
                                  }
                                },
                            ),
                            const TextSpan(text: '与TA取得联系'),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
            SizedBox(
              height: 40.w,
            ),
            Center(
              child: myNoDataView(
                context,
                msg: '该用户暂未选择球队',
                imagewidget: WxAssets.images.teamEmptyIcon
                    .image(width: 180.w, height: 120.w),
              ),
            ),
          ],
        )).marginSymmetric(horizontal: 15.w);
  }

  Widget infoWidget(String title, String info) {
    return Row(
      children: [
        Text(
          title,
          style: TextStyles.regular
              .copyWith(color: Colours.color5C5C6E, fontSize: 14.sp),
        ),
        SizedBox(
          width: 48.w,
        ),
        Text(
          info,
          style: TextStyles.regular
              .copyWith(color: Colours.white, fontSize: 14.sp),
        ),
      ],
    );
  }
}
