import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/c_d_key_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class CDKLogic extends GetxController {
  TextEditingController codeController = TextEditingController();
  var isCanExchange = false.obs;
  @override
  void onInit() {
    super.onInit();
    codeController.addListener(() {
      isCanExchange.value = codeController.text.trim().length >= 13;
    });
//         NEWPA9WTUA3NS   NEWFP9Y9UBEGY NEWVPZV7L5P3B 会员折扣券
//     NEWWMTEM8ZH4D NEWRP5EVRLMJJ NEWYZJV6EJ23D  赛事券
//   NEW2SJWVBH4K3 NEWUJZREY5853  svip券
//   NEWS9LMK6TW6Z NEW27LKWPSBQN   vip券
//    codeController.text = "NEW2SJWVBH4K3";
  }

// ArenaName	string
// 球馆名
// Type	integer
// 类型: 1=VIP, 2=SVIP, 3=赛事券, 4=会员折扣券
// arenaId	integer
// 球馆id
// day	integer
// 会员天数（只针对VIP和svip）
// discount	string
// 折扣类型 8.8折 9折
// matchCouponType	string
// 赛事券类型 个人 单队
  //积分兑换商品
  Future<void> getExchangeCDKey() async {
    if (codeController.text.length < 13) {
      WxLoading.showToast(S.current.cdk_tips6);
      return;
    }
    // var cdKeyModel = CDKeyModel.fromJson({
    //   "Type": 1,
    //   "day": 0,
    //   "ArenaName": "",
    //   "arenaId": 0,
    //   "matchCouponType": "",
    //   "discount": "6.6折扣"
    // });

    WxLoading.show();
    Map<String, dynamic>? param = {
      "cdKey": codeController.text.trim(),
    };

    final res =
        await Api().post(ApiUrl.postCdkey, data: param, showError: false);
    log(jsonEncode(res.data));
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      var cdKeyModel = CDKeyModel.fromJson(res.data);
      getMyBgDialog(
        S.current.Successful_exchange,
        cdKeyModel.type == 1
            ? S.current.cdk_tips2
            : cdKeyModel.type == 2
                ? S.current.cdk_tips2
                : cdKeyModel.type == 3
                    ? S.current.cdk_tips3
                    : cdKeyModel.type == 4
                        ? S.current.cdk_tips4
                        : S.current.sure,
        () async {
          AppPage.back();
          await Future.delayed(const Duration(milliseconds: 100));
          // 类型: 1=VIP, 2=SVIP, 3=赛事券, 4=会员折扣券
          switch (cdKeyModel.type ?? 0) {
            case 1:
              AppPage.to(Routes.place, needLogin: true);
              break;
            case 2:
              AppPage.to(Routes.place, needLogin: true);
              break;
            case 3:
              AppPage.to(
                Routes.scheduleHomePage,
              );
              break;
            case 4:
              AppPage.to(Routes.vipPage, needLogin: true);
              break;
          }
        },
        bgImage: DecorationImage(
            image: WxAssets.images.cdkBg.provider(), fit: BoxFit.fill),
        isShowClose: false,
        isShowCloseRight: true,
        btnIsHorizontal: false,
        contentWidget: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: RichText(
                text: TextSpan(
                    text: S.current.cdk_tips5,
                    style: TextStyle(
                        color: Colours.color9393A5,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.normal),
                    children: <InlineSpan>[
                      // 类型: 1=VIP, 2=SVIP, 3=赛事券, 4=会员折扣券
                      if (cdKeyModel.type == 1)
                        TextSpan(
                          text: "${cdKeyModel.arenaName}",
                          style: TextStyle(
                              color: Colours.color9393A5,
                              fontSize: 14.sp,
                              fontWeight: FontWeight.normal),
                        ),
                      if (cdKeyModel.type == 2)
                        TextSpan(
                          text: "\t${cdKeyModel.day}天SVIP\t",
                          style:
                              TextStyles.semiBold14.copyWith(fontSize: 24.sp),
                        ),
                      if (cdKeyModel.type != 1)
                        TextSpan(
                            text:
                                "\t${cdKeyModel.discount}${cdKeyModel.type == 3 ? (cdKeyModel.discount?.contains("赛事券") ?? false) ? "" : "${cdKeyModel.matchCouponType}赛事券" : cdKeyModel.type == 4 ? (cdKeyModel.discount?.contains("会员") ?? false) ? "" : "会员券" : ""}\t",
                            style: TextStyles.semiBold14.copyWith(
                              fontSize: 24.sp,
                            )),
                    ]),
              ),
            ),
            if (cdKeyModel.type == 1)
              SizedBox(
                height: 20.w,
              ),
            if (cdKeyModel.type == 1)
              Text(
                "${cdKeyModel.day}天VIP",
                style: TextStyles.regular.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colours.white,
                    fontSize: 20.sp),
              ),
            SizedBox(
              height: 20.w,
            ),
            Stack(
              children: [
                MyImage(
                  cdKeyModel.type == 1
                      ? "cdk_vip.png"
                      : cdKeyModel.type == 2
                          ? "cdk_svip.png"
                          : cdKeyModel.type == 3
                              ? 'cdk_event_ticket.png'
                              : "cdk_coupon.png",
                  fit: BoxFit.fill,
                  bgColor: Colors.transparent,
                  isAssetImage: true,
                  radius: 0.r,
                  width: 229.w,
                  height: 138.w,
                ),
                if (cdKeyModel.type == 1 || cdKeyModel.type == 2)
                  Positioned(
                      top: 64.w,
                      left: 31.w,
                      child: ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [
                            Colours.colorFFF9DC,
                            Colours.colorE4C8FF,
                            Colours.colorE5F3FF,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ).createShader(bounds),
                        child: Text(
                          '${cdKeyModel.day}天${cdKeyModel.type == 1 ? 'VIP' : cdKeyModel.type == 2 ? 'SVIP' : ''}',
                          style: TextStyles.semiBold14,
                        ),
                      )),
                if (cdKeyModel.type == 3)
                  Positioned(
                      bottom: 32.w,
                      right: 30.w,
                      child: ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [
                            Colours.colorFFF9DC,
                            Colours.colorE4C8FF,
                            Colours.colorE5F3FF,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ).createShader(bounds),
                        child: Text(
                          '${cdKeyModel.matchCouponType}赛事券',
                          style:
                              TextStyles.semiBold14.copyWith(fontSize: 10.sp),
                        ),
                      ))
              ],
            )
          ],
        ),
      );
    } else {
      getMyDialog(S.current.failure_exchanged, S.current.sure, () {
        AppPage.back();
      },
          isShowClose: false,
          btnIsHorizontal: false,
          contentWidget: Padding(
            padding: EdgeInsets.only(left: 50.w, right: 50.w),
            child: Text(res.message,
                maxLines: 5,
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: Colours.color9393A5,
                    height: 2,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.normal)),
          ));
    }
  }

  // //兑换商品弹窗
  // void getExchangeGoodsDialog(String title, String sureText,
  //     GoodsDetailModel goodsDetailModel, void Function()? onPressed) {
  //   Get.dialog(BaseDialog(
  //     title: title,
  //     onPressed: onPressed,
  //     isShowCannel: true,
  //     sureText: sureText,
  //     child: Padding(
  //       padding: const EdgeInsets.symmetric(horizontal: 20.0),
  //       child: Column(
  //         mainAxisSize: MainAxisSize.min,
  //         children: [
  //           Container(
  //             padding: const EdgeInsets.all(30),
  //             child: MyImage(
  //               goodsDetailModel.thumbUrl ?? '',
  //               //  holderImg: "home/index/df_banner_top",
  //               fit: BoxFit.fill,
  //               width: 174.w,
  //               height: 116.w,
  //               bgColor: Colours.white,
  //               errorImage: "error_img_white.png",
  //               placeholderImage: "error_img_white.png",
  //               radius: 5.r,
  //             ),
  //           ),
  //           SizedBox(
  //             height: 5.w,
  //           ),
  //           RichText(
  //             text: TextSpan(
  //                 text: S.current.Confirmed_use,
  //                 style: TextStyle(
  //                     color: Colours.color5C5C6E,
  //                     fontSize: 14.sp,
  //                     fontWeight: FontWeight.bold),
  //                 children: <InlineSpan>[
  //                   TextSpan(
  //                       text: "${goodsDetailModel.pointPrice ?? "-"}",
  //                       style: TextStyle(
  //                           color: Colours.colorA44EFF,
  //                           fontSize: 26.sp,
  //                           fontWeight: FontWeight.normal)),
  //                   TextSpan(
  //                       text: S.current.Point_exchange,
  //                       style: TextStyle(
  //                           color: Colours.color5C5C6E,
  //                           fontSize: 14.sp,
  //                           fontWeight: FontWeight.normal)),
  //                 ]),
  //           ),
  //         ],
  //       ),
  //     ),
  //   ));
  // }
}
