import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_swiper_plus/flutter_swiper_plus.dart';
import 'package:geolocator/geolocator.dart' as geolocator;
import 'package:get/get.dart';
import 'package:map_launcher/map_launcher.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/database/model/option_goal_model.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/arena_details_model.dart';
import 'package:shoot_z/pages/game/models/game_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/competition_model.dart';
import 'package:shoot_z/pages/tab3Create/place/models/place_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../../generated/l10n.dart';

class ArenaDetailsLogic extends GetxController
    with WidgetsBindingObserver, GetSingleTickerProviderStateMixin {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  SwiperController swiperController = SwiperController();
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  var isDaka = false.obs; //是否球馆打卡
  var tabNameList = [
    S.current.arena_info,
    S.current.recent_games,
    S.current.recent_competitions
  ];
  TabController? tabController;
  var tabbarIndex = 0.obs;
  VideoController videoController = VideoController();
  var arenaID = 0.obs;
  var placemodel3 = Rx<PlaceModel?>(null); //球馆列表实例化数据
  var arenaDetailsModel = ArenaDetailsModel().obs;
  //数据列表
  RxList<Matches> dataList = <Matches>[].obs;
  RxList<CompetitionModel> competitionList = <CompetitionModel>[].obs;
  var indexVideo = 9999.obs;
  //打卡图片的位置
  var offset = Offset(2, ScreenUtil().screenHeight - 211.w - 240.w)
      .obs; //ScreenUtil().screenHeight - 240 - 400
  //选中数据列表
  RxList<OptionGoalModel> dataCheckList = <OptionGoalModel>[].obs;
  StreamSubscription? refreshSubscription;
  var isShare = false.obs;
  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    tabController = TabController(length: tabNameList.length, vsync: this);
    tabController?.addListener(
      () {
        tabbarIndex.value = tabController?.index ?? 0;

        if (tabController?.indexIsChanging ?? false) {
          UserManager.instance.postApmTracking(0,
              nowPage: Routes.arenaDetailsPage,
              toPage: Routes.moreHighlightsPage,
              subPage: tabbarIndex.value == 0
                  ? "Getmyvideo"
                  : tabbarIndex.value == 1
                      ? "Recentcompetitions"
                      : "Recentschedule",
              remark: tabbarIndex.value == 0
                  ? "子界面1场馆信息"
                  : tabbarIndex.value == 1
                      ? "子界面2近期比赛"
                      : "子界面3近期赛程",
              content: "进入场馆详情页面");
        }
      },
    );
    arenaID.value = Get.arguments["id"]; //39 ??
    refreshSubscription = BusUtils.instance.on((p0) {
      if (p0.key == EventBusKey.toArenaDetails) {
        if (UserManager.instance.isLogin) {
          getDaoGoalList();
        }
      }
      if (p0.key == EventBusKey.subcribeMatchesRefresh) {
        getdataList(isLoad: false, controller: refreshController);
      }
    });
    getdataList(isLoad: false, controller: refreshController);
    getDataInfo(true);
    getCompetitionList();
    if (UserManager.instance.isLogin) {
      getPointsIsClockIn();
    }
  }

  @override
  void onReady() {
    super.onReady();
    if (UserManager.instance.isLogin) {
      getDaoGoalList();
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
    } else if (state == AppLifecycleState.paused) {}
  }

  reloadVideo(String videoUrl, int index) async {
    swiperController.move(index);
    videoController.setData(videoPath: videoUrl);
  }

  //获得打卡信息
  Future<void> getPointsIsClockIn() async {
    // WxLoading.show();
    Map<String, dynamic>? param = {};
    final res = await Api().get(ApiUrl.pointsIsClockIn, queryParameters: param);
    //WxLoading.dismiss();
    if (res.isSuccessful()) {
      isDaka.value = res.data["data"] ?? false;
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //球场打卡
  Future<void> getPointsClockIn(Position? position) async {
    // WxLoading.show();
    Map<String, dynamic>? param = {
      "arenaID": arenaID.value,
      "latitude": (position?.latitude ?? "0.0").toString(),
      "longitude": (position?.longitude ?? "0.0").toString(),
      "taskID": 3,
    };
    final res =
        await Api().post(ApiUrl.pointsClockIn, data: param, showError: false);
    //打卡成功：{"data":true}0
    //null90000今日已打卡

    log(jsonEncode(res.data) + jsonEncode(res.code));
    //{"data":{"point":10,"success":true}}
    //WxLoading.dismiss();
    if (res.isSuccessful()) {
      getMyDialog(
        S.current.clocking_success,
        S.current.sure,
        () {
          AppPage.back();
        },
        btnText2: S.current.Go_points,
        onPressed2: () {
          AppPage.back();
          AppPage.to(Routes.pointsPage);
        },
        isShowClose: false,
        imageAsset: "clock_successful.png",
        imgHeight: 100.w,
        imgWidth: 100.w,
        contentWidget: RichText(
          text: TextSpan(
              text: "+",
              style: TextStyle(
                  color: Colours.colorA44EFF,
                  fontSize: 22.sp,
                  fontWeight: FontWeight.normal),
              children: <InlineSpan>[
                TextSpan(
                    text: "${res.data["data"]?["point"] ?? 0}\t",
                    style: TextStyle(
                        color: Colours.colorA44EFF,
                        fontSize: 26.sp,
                        fontWeight: FontWeight.normal)),
                TextSpan(
                    text: S.current.integral,
                    style: TextStyle(
                        color: Colours.colorA44EFF,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.normal)),
              ]),
        ),
      );
    } else {
      getMyDialog(
        S.current.clocking_failure,
        S.current.reclock,
        () {
          AppPage.back();
        },
        isShowClose: true,
        imageAsset: "clock_failure.png",
        imgHeight: 100.w,
        imgWidth: 100.w,
        contentWidget: RichText(
          text: TextSpan(
              text: "+",
              style: TextStyle(
                  color: Colours.colorA44EFF,
                  fontSize: 22.sp,
                  fontWeight: FontWeight.normal),
              children: <InlineSpan>[
                TextSpan(
                    text: "${res.data["data"]?["point"] ?? 0}\t",
                    style: TextStyle(
                        color: Colours.colorA44EFF,
                        fontSize: 26.sp,
                        fontWeight: FontWeight.normal)),
                TextSpan(
                    text: S.current.integral,
                    style: TextStyle(
                        color: Colours.colorA44EFF,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.normal)),
              ]),
        ),
      );
    }
  }

//获得数据
  Future<void> getDataInfo(bool isRefresh) async {
    if (isRefresh) {
      if (await LocationUtils.instance.checkPermission()) {
        await LocationUtils.instance.getCurrentPosition();
      }
    }

    final position = LocationUtils.instance.position;
    if (position == null) {
      WxLoading.showToast(S.current.failed_location);
      return;
    }
    // WxLoading.show();
    var param = {
      'latitude': '${position.latitude}',
      'longitude': '${position.longitude}',
      'arenaID': arenaID.value,
    };

    final res = await Api().get(ApiUrl.arenasDetail, queryParameters: param);
    //WxLoading.dismiss();
    log('message${res.data}');
    if (res.isSuccessful()) {
      arenaDetailsModel.value = ArenaDetailsModel.fromJson(res.data);
      for (int i = 0; i < (arenaDetailsModel.value.path?.length ?? 0); i++) {
        if (arenaDetailsModel.value.path?[i] != null) {
          if (arenaDetailsModel.value.path?[i] is ArenaDetailsModelPath) {
            if (arenaDetailsModel.value.path?[i]?.type == 1) {
              indexVideo.value = i;
              reloadVideo(arenaDetailsModel.value.path?[i]?.path ?? "", i);
            }
          }
        }
      }

      arenaDetailsModel.refresh();

      if (dataFag["isFrist"] as bool) {
        dataFag["isFrist"] = false;
        refresh();
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      "arenaID": arenaID.value,
      'page': dataFag["page"] ?? 1,
      'limit': 10,
    };
    var res = await Api().get(ApiUrl.arenaGameList, queryParameters: param);

    if (res.isSuccessful()) {
      List list = res.data["arenaMatch"];
      List<Matches> modelList = list.map((e) => Matches.fromJson(e)).toList();
      log("zzzzzz12removeAt-${modelList.length}");
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          controller.loadNoData();
          //  controller.loadComplete();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList.assignAll(modelList);
        controller.refreshCompleted();
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

//预约赛事报告
  getMatchesSubscribe(String matchId) async {
    Map<String, dynamic> param = {"matchId": matchId};
    var res = await Api()
        .post(ApiUrl.matchesSubscribe(matchId), queryParameters: param);
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.game_report4);
      getdataList(isLoad: false, controller: refreshController);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //取消预约
  matchesCancelSubscribe(String matchId) async {
    Map<String, dynamic> param = {"matchId": matchId};
    var res = await Api()
        .post(ApiUrl.matchesCancelSubscribe(matchId), queryParameters: param);
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.cancel_subcribe);
      getdataList(isLoad: false, controller: refreshController);
    } else {
      WxLoading.showToast(res.message);
    }
  }

//获得最新赛程列表
  getCompetitionList() async {
    Map<String, dynamic> param = {
      "arenaId": arenaID.value,
      'page': 1,
      'limit': 10,
    };
    var res = await Api().get(ApiUrl.competitionsList, queryParameters: param);

    if (res.isSuccessful()) {
      List list = res.data["result"];
      List<CompetitionModel> modelList =
          list.map((e) => CompetitionModel.fromJson(e)).toList();

      competitionList.addAll(modelList);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  @override
  void onClose() {
    super.onClose();
    refreshSubscription?.cancel();
    videoController.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  openMapsSheet(
      context, String title, double latitude, double longitude) async {
    try {
      final coords = Coords(latitude, longitude);
      //final title = "Ocean Beach";
      final availableMaps = await MapLauncher.installedMaps;
      showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return SafeArea(
            child: SingleChildScrollView(
              child: Container(
                padding: EdgeInsets.only(top: 12.w),
                //  color: Colours.color000000,
                child: Wrap(
                  children: <Widget>[
                    for (var map in availableMaps)
                      ListTile(
                        onTap: () {
                          map.showMarker(
                            coords: coords,
                            title: title,
                          );
                        },
                        title: Text(
                          map.mapType == MapType.amap
                              ? S.current.Geo_Maps
                              : map.mapType == MapType.baidu
                                  ? S.current.Badiu_Maps
                                  : map.mapType == MapType.tencent
                                      ? S.current.Tencent_Maps
                                      : map.mapType == MapType.apple
                                          ? S.current.Apple_Maps
                                          : map.mapType == MapType.google
                                              ? S.current.Google_Maps
                                              : map.mapName,
                          style: TextStyles.regular.copyWith(
                              fontSize: 14.sp, color: Colours.color333333),
                        ),
                        leading: SvgPicture.asset(
                          map.icon,
                          height: 30.0,
                          width: 30.0,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    } catch (e) {
      print(e);
    }
  }

  //改变顶部指示器
  void changeSwiper(int index) {
    if (indexVideo.value != 9999) {
      if (index == indexVideo.value) {
        if (videoController.dataSource != null &&
            videoController.dataSource ==
                arenaDetailsModel.value.path?[index]?.path) {
          //判断视屏链接相等就播放
          videoController.play();
        } else {
          reloadVideo(arenaDetailsModel.value.path?[index]?.path ?? "", index);
        }
      } else {
        videoController.pause();
      }
    }
  }

//打卡随着手势移动
  Offset calOffset(Size size, Offset offset, Offset nextOffset) {
    double dx = 0;
    //水平方向偏移量不能小于0不能大于屏幕最大宽度
    if (offset.dx + nextOffset.dx <= 0) {
      dx = 0;
    } else if (offset.dx + nextOffset.dx >= (size.width - 50)) {
      dx = size.width - 50;
    } else {
      dx = offset.dx + nextOffset.dx;
    }
    double dy = 0;
    //垂直方向偏移量不能小于0不能大于屏幕最大高度
    if (offset.dy + nextOffset.dy >= (size.height - 100)) {
      dy = size.height - 100;
    } else if (offset.dy + nextOffset.dy <= kToolbarHeight) {
      dy = kToolbarHeight;
    } else {
      dy = offset.dy + nextOffset.dy;
    }
    return Offset(
      dx,
      dy,
    );
  }

  //打卡弹窗
  Future<void> getDaka() async {
    if (!(await LocationUtils.instance.checkPermission())) {
      getMyDialog(
        S.current.clocking_failure,
        S.current.open_now,
        () {
          AppPage.back();
          LocationUtils.instance.openSettings("我们需要您的位置信息以提供位置，完成打卡");
        },
        isShowClose: true,
        imageAsset: "clock_failure.png",
        imgHeight: 100.w,
        imgWidth: 100.w,
        contentWidget: Column(
          children: [
            SizedBox(
              height: 5.w,
            ),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                text: S.current.reclock_tips,
                style: TextStyle(
                    color: Colours.color9393A5,
                    fontSize: 14.sp,
                    height: 2,
                    fontWeight: FontWeight.normal),
              ),
            ),
          ],
        ),
      );
    } else {
      WxLoading.show();
      await LocationUtils.instance.getCurrentPosition();
      WxLoading.dismiss();

      final position = LocationUtils.instance.position;
      if (position == null) {
        WxLoading.showToast(S.current.failed_location);
        return;
      }
      double sss = geolocator.Geolocator.distanceBetween(
          position.latitude,
          position.longitude,
          arenaDetailsModel.value.latitude ?? 0.0,
          arenaDetailsModel.value.longitude ?? 0.0);
      log("zzzzzz12removeAt1-$sss");
      if (sss > 1000) {
        geolocator.Geolocator.distanceBetween(
            position.latitude,
            position.longitude,
            arenaDetailsModel.value.latitude ?? 0.0,
            arenaDetailsModel.value.longitude ?? 0.0);
        var distance = "";
        if (sss > 1000) {
          distance = "${(sss ~/ 10) / 100}km";
        } else {
          distance = "${(sss * 10) ~/ 10}m";
        }
        getMyDialog(
          S.current.clocking_failure,
          S.current.reclock,
          () {
            AppPage.back();
            getDaka();
          },
          isShowClose: true,
          imageAsset: "clock_failure.png",
          imgHeight: 100.w,
          imgWidth: 100.w,
          contentWidget: Column(
            children: [
              SizedBox(
                height: 5.w,
              ),
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                    text: S.current.clocking_distance(""),
                    style: TextStyle(
                        color: Colours.color9393A5,
                        fontSize: 14.sp,
                        height: 2,
                        fontWeight: FontWeight.normal),
                    children: <InlineSpan>[
                      TextSpan(
                          text: distance,
                          style: TextStyle(
                              color: Colours.colorA44EFF,
                              fontSize: 14.sp,
                              height: 2,
                              fontWeight: FontWeight.normal)),
                    ]),
              ),
            ],
          ),
        );
      } else {
        //球馆打卡
        getPointsClockIn(position);
      }
    }
  }

  //数据库取数据
  Future<void> getDaoGoalList() async {
    // 获取当前本地时间
    DateTime now = DateTime.now();

    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final optionGoalDao = database.optionGoalDao;
    await optionGoalDao
        .findAllGoal(arenaID.value.toString(),
            UserManager.instance.userInfo.value?.userId ?? "")
        .then((v) {
      // // 使用 where 方法过滤列表
      List<OptionGoalModel> filteredNumbers = v.where((value) {
        bool result = false;
        String? timeString = value.videoDate;
        if (timeString != null) {
          try {
            // 解析日期时间字符串
            DateTime dateTime = DateTime.parse(timeString);

            // 判断两个日期是否相差8天以内
            result = Utils.areDatesWithinEightDays(now, dateTime);
            // 打印 UTC 时间和本地时间
            // log("getDaoSql5=${result}");
          } catch (e) {
            log("getDaoSql6catch=${e}");
          }
        }

        return result;
      }).toList();

      dataCheckList.assignAll(filteredNumbers);
    });
    await optionGoalDao.deleteAll(arenaID.value.toString(),
        UserManager.instance.userInfo.value?.userId ?? "");
    await Future.delayed(const Duration(milliseconds: 100));
    await optionGoalDao.insertGoalList(dataCheckList, arenaID.value.toString(),
        UserManager.instance.userInfo.value?.userId ?? "");
  }
}
