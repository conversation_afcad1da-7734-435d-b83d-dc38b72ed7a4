// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_players_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TeamPlayersModel _$TeamPlayersModelFromJson(Map<String, dynamic> json) =>
    TeamPlayersModel(
      (json['players'] as List<dynamic>)
          .map((e) => Players.fromJson(e as Map<String, dynamic>))
          .toList(),
      json['teamId'] as String,
      json['teamName'] as String,
    );

Map<String, dynamic> _$TeamPlayersModelToJson(TeamPlayersModel instance) =>
    <String, dynamic>{
      'players': instance.players,
      'teamId': instance.teamId,
      'teamName': instance.teamName,
    };

Players _$PlayersFromJson(Map<String, dynamic> json) => Players(
      json['playerName'] as String?,
      json['playerId'] as String,
      json['photo'] as String,
      json['number'] as String,
      json['score'] as int,
      json['rate'] as String,
      json['shootCount'] as int,
      json['shootHit'] as int,
      json['fragmentCount'] as int,
      json['trashTalk'] as String,
      json['reboundCount'] as int,
      json['assistCount'] as int,
      json['mvp'] as bool,
      json['scoreKing'] as bool,
      json['threePointKing'] as bool,
      json['freeThrowKing'] as bool,
      json['assistKing'] as bool,
      json['reboundKing'] as bool,
      json['scoreRate'] as String,
      json['assistRate'] as String,
      json['reboundRate'] as String,
      json['locked'] as int,
      json['contributionValue'].toDouble(),
    );

Map<String, dynamic> _$PlayersToJson(Players instance) => <String, dynamic>{
      'playerName': instance.playerName,
      'playerId': instance.playerId,
      'photo': instance.photo,
      'number': instance.number,
      'score': instance.score,
      'rate': instance.rate,
      'shootCount': instance.shootCount,
      'shootHit': instance.shootHit,
      'fragmentCount': instance.fragmentCount,
      'trashTalk': instance.trashTalk,
      'reboundCount': instance.reboundCount,
      'assistCount': instance.assistCount,
      'mvp': instance.mvp,
      'scoreKing': instance.scoreKing,
      'threePointKing': instance.threePointKing,
      'freeThrowKing': instance.freeThrowKing,
      'assistKing': instance.assistKing,
      'reboundKing': instance.reboundKing,
      'scoreRate': instance.scoreRate,
      'assistRate': instance.assistRate,
      'reboundRate': instance.reboundRate,
      'locked': instance.locked,
      'contributionValue': instance.contributionValue,
    };
