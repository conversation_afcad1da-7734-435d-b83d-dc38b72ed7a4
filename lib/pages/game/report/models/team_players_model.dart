import 'package:json_annotation/json_annotation.dart';

part 'team_players_model.g.dart';

@JsonSerializable()
class TeamPlayersModel extends Object {
  @JsonKey(name: 'players')
  List<Players> players;

  @Json<PERSON>ey(name: 'teamId')
  String teamId;

  @Json<PERSON>ey(name: 'teamName')
  String teamName;

  TeamPlayersModel(
    this.players,
    this.teamId,
    this.teamName,
  );

  factory TeamPlayersModel.fromJson(Map<String, dynamic> srcJson) =>
      _$TeamPlayersModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$TeamPlayersModelToJson(this);
}

@JsonSerializable()
class Players extends Object {
  @JsonKey(name: 'playerName')
  String? playerName;

  @JsonKey(name: 'playerId')
  String playerId;

  @JsonKey(name: 'photo')
  String photo;

  @JsonKey(name: 'number')
  String number;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'score')
  int score;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'rate')
  String rate;

  @Json<PERSON><PERSON>(name: 'shootCount')
  int shootCount;

  @Json<PERSON>ey(name: 'shootHit')
  int shootHit;

  @Json<PERSON>ey(name: 'fragmentCount')
  int fragmentCount;

  @JsonKey(name: 'trashTalk')
  String trashTalk;

  @JsonKey(name: 'reboundCount')
  int reboundCount;

  @JsonKey(name: 'assistCount')
  int assistCount;

  @JsonKey(name: 'mvp')
  bool mvp;

  @JsonKey(name: 'scoreKing')
  bool scoreKing;

  @JsonKey(name: 'threePointKing')
  bool threePointKing;

  @JsonKey(name: 'freeThrowKing')
  bool freeThrowKing;

  @JsonKey(name: 'assistKing')
  bool assistKing;

  @JsonKey(name: 'reboundKing')
  bool reboundKing;

  @JsonKey(name: 'scoreRate')
  String scoreRate;

  @JsonKey(name: 'assistRate')
  String assistRate;

  @JsonKey(name: 'reboundRate')
  String reboundRate;

  @JsonKey(name: 'locked')
  int locked;

  @JsonKey(name: 'contributionValue')
  double contributionValue;

  bool get locking => locked == 1;

  Players(
      this.playerName,
      this.playerId,
      this.photo,
      this.number,
      this.score,
      this.rate,
      this.shootCount,
      this.shootHit,
      this.fragmentCount,
      this.trashTalk,
      this.reboundCount,
      this.assistCount,
      this.mvp,
      this.scoreKing,
      this.threePointKing,
      this.freeThrowKing,
      this.assistKing,
      this.reboundKing,
      this.scoreRate,
      this.assistRate,
      this.reboundRate,
      this.locked,
      this.contributionValue);

  factory Players.fromJson(Map<String, dynamic> srcJson) =>
      _$PlayersFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PlayersToJson(this);
}
