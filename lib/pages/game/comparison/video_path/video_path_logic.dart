import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/FileUtils.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class VideoPathLogic extends GetxController {
  late VideoController? videoController;
  var videoPath = "".obs;
  var videoId = "".obs;
  var trainingId = "";
  var teamName = "".obs;
  var isShowShareUpdate = "".obs; //0不带分享  1带分享  2本地视频 单人  3本地视频 多人
  var shotRecordModel = ShotRecordModel().obs;
  @override
  void onInit() {
    super.onInit();
    videoPath.value = Get.arguments["videoPath"];
    isShowShareUpdate.value = Get.arguments["isShowShareUpdate"] ?? "0";

    // final file = File(videoPath.value);
    log("file2=${videoPath.value}");
    //  Get.arguments["videoPath"];
    teamName.value = Get.arguments["teamName"];
    switch (isShowShareUpdate.value) {
      case "1": //普通视频
        videoId.value = Get.arguments["videoId"] ?? "";
        break;
      case "2": //本地视频 本地片段
      case "3": //本地视频 本地片段
        shotRecordModel.value =
            Get.arguments["shotRecordModel"] as ShotRecordModel;
        videoController = VideoController(isNetwork: false);
        break;
      case "4": //云台创作 播放本地视频

        videoPath.value =
            "/storage/emulated/0/Android/data/com.shootZ.app.shoot_z/files/Movies/final_videos/extended_last_20250908_101344.mp4";
        videoController = VideoController(isNetwork: false);
        if (Get.arguments != null && Get.arguments.containsKey('trainingId')) {
          trainingId = Get.arguments['trainingId'] ?? '';
        }
        if (Get.arguments != null && Get.arguments.containsKey('videoId')) {
          videoId.value = Get.arguments["videoId"] ?? "";
        }

        break;
      default:
        videoController = VideoController();

        break;
    }

    getVideosInfo();
  }

  Future<void> getVideosInfo() async {
    if ((videoPath.value).isNotEmpty) {
      videoController?.setData(
          videoPath: videoPath.value, videoCover: 'error_image_width');
    }
  }

  @override
  void onClose() {
    super.onClose();
    videoController?.dispose();
  }

  void share() async {
    MyShareH5.getShareH5(ShareHighlights(
        sharedFrom: UserManager.instance.userInfo.value?.userId ?? "",
        highlightId: videoId.value,
        type: isShowShareUpdate.value));
  }

  void downloadAndSaveVideo() {
    Utils.downloadAndSaveToPhotoAlbum(videoPath.value);
  }

  void showDeleteDialog() {
    Get.dialog(CustomAlertDialog(
      title: S.current.confirm_deletion,
      content: S.current.video_removal_tips,
      onPressed: () async {
        AppPage.back();
        getDeleteVideo();
      },
    ));
  }

  Future<void> getDeleteVideo() async {
    Map<String, dynamic> param2 = {"id": videoId.value};
    var url = await ApiUrl.getDeleteVideo(videoId.value);
    var res = await Api().delete(url, data: param2);
    log("getDeleteVideo=${videoId.value}-${res.data}");
    if (res.isSuccessful()) {
      AppPage.back(result: true);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  deleteVideo(ShotRecordModel shotRecordModel) async {
    getMyDialog(
      "确认删除此视频？",
      S.current.sure,
      content: "删除后报告中的视频也会被删除",
      () async {
        AppPage.back();
        final database =
            await $FloorAppDatabase.databaseBuilder('app_database.db').build();
        final selfieShotDao = database.selfieShotDao;
        //删除本地文件
        FileUtils.deleteFile(
            UserManager.instance
                .changeFilePathInIOS(shotRecordModel.filePath ?? ""),
            context: Get.context!,
            isShow: false);
        selfieShotDao.deleteShot1(
            shotRecordModel.trainingId ?? "0",
            UserManager.instance.userInfo.value?.userId ?? "",
            shotRecordModel.eventId ?? "0");

        if (isShowShareUpdate.value == "2") {
          BusUtils.instance
              .fire(EventAction(key: EventBusKey.deleteLocalVideo1));
        } else {
          BusUtils.instance
              .fire(EventAction(key: EventBusKey.deleteLocalVideo2));
        }
        AppPage.back();
      },
      isShowClose: false,
      btnIsHorizontal: true,
      btnText2: S.current.cancel,
      onPressed2: () {
        AppPage.back();
      },
    );
  }

  //将视频保存到相册
  Future<void> downloadAndSaveToSinglePhotoAlbum(String videoPath) async {
    getMyDialog(
      S.current.dialog_title,
      S.current.sure,
      content: "确认下载选中视频至手机相册？",
      () {
        AppPage.back();
        Utils.localDownloadAndSaveToSinglePhotoAlbum(videoPath);
      },
      isShowClose: false,
      btnIsHorizontal: true,
      btnText2: S.current.cancel,
      onPressed2: () {
        AppPage.back();
      },
    );
  }

  deleteVideo4() async {
    getMyDialog(
      "确认删除此视频？",
      S.current.sure,
      content: "删除后报告中的视频也会被删除",
      () async {
        AppPage.back();
        getDeleteVideo4();
      },
      isShowClose: false,
      btnIsHorizontal: true,
      btnText2: S.current.cancel,
      onPressed2: () {
        AppPage.back();
      },
    );
  }

  //删除视频
  Future<void> getDeleteVideo4() async {
    //删除本地文件
    WxLoading.show();
    Map<String, dynamic> param = {
      'trainingId': trainingId,
      "videoIds": [videoId.value],
    };
    var res = await Api().delete(ApiUrl.deletePtzRecordList, data: param);
    log("deletePtzRecordList=${param} ${res.data}");
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast("删除成功");
      FileUtils.deleteFile(videoPath.value, //list[i].filePath ??
          context: Get.context!,
          isShow: false);
      if (isShowShareUpdate.value == "4") {
        BusUtils.instance.fire(EventAction(key: EventBusKey.deleteLocalVideo3));
      }
      AppPage.back();
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
