import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';

class VideoEditHomeLogic extends GetxController
    with GetTickerProviderStateMixin {
  TabController? tabController;
  var currentTabIndex = 0.obs;
  var type = 0;
  var tabNameList = [S.current.yuntai_creation, S.current.yuntai_highlights];
  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 2, vsync: this);
    tabController?.addListener(() {
      currentTabIndex.value = tabController?.index ?? 0;
    });
    if (Get.arguments != null && Get.arguments is Map<String, dynamic>) {
      final args = Get.arguments as Map<String, dynamic>;
      type = args['type'] as int? ?? 0;
      if (type == 1) {
        tabController?.animateTo(1);
      }
    }
  }

  // @override
  // void onClose() {
  //   super.onClose();
  // }

  @override
  void dispose() {
    tabController?.dispose();
    super.dispose();
  }
}
