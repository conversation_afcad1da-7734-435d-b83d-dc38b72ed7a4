import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/selfie_shot_local_info_model.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';
import 'package:shoot_z/network/model/tab_yuntai_creation_item_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab/upload/UploadController.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/FileUtils.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';

class YuntaiCreationInfoLogic extends GetxController {
  RefreshController refreshController2 =
      RefreshController(initialRefresh: false);
  final VideoController videoController =
      VideoController(pushDisposeOnAndroid: true);
  TextEditingController nickNameController = TextEditingController();
  var indexVideoId = "".obs;
  var compositeOption2 = ["0", "0", "0", "0"].obs; //视频效果与个性化 0选中  1选中 多选
  var rememberOption = "0".obs; //记住我的选择
  var isShare = true.obs; //是否可以分享 0不能 1可以
  final uploadController = Get.find<UploadController>();
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList = <ShotRecordModel>[].obs;
  // 是否全部选中（计算属性）
  RxInt get checkCount {
    final selectedCount = dataList.where((item) => item.isCheck == "1").length;
    return selectedCount.obs;
  }

  var tabYuntaiCreationItemModel = TabYuntaiCreationItemModel().obs;
  var trainingId = "".obs;
  var type = "1".obs; //1单人  2多人
  var isHorizontal = true.obs; //1单人  2多人
  var allCheck = false.obs;
  StreamSubscription? subscription;
  var showUploadTip = true.obs;
  @override
  void onInit() async {
    super.onInit();
    final isShowed = await WxStorage.instance.getBool("showUploadTip") ?? false;
    showUploadTip.value = !isShowed;
    //自由半场 本地视频 单个比赛详情
    if (Get.arguments != null &&
        Get.arguments.containsKey('tabYuntaiCreationItemModel')) {
      tabYuntaiCreationItemModel.value =
          Get.arguments['tabYuntaiCreationItemModel']
              as TabYuntaiCreationItemModel;
      trainingId.value = "1310"; // "1310 2";   "1304"  1
      //"${tabYuntaiCreationItemModel.value.trainingId ?? 0}";
      type.value = "2";
      //shootType 拍摄类型 0:未知 1: 半场拍摄, 2: 全场拍摄, 3: 全场赛事  1当作横屏
      isHorizontal.value =
          (tabYuntaiCreationItemModel.value.shootType == 1) ? false : true;
      rememberOption.value =
          await WxStorage.instance.getString("rememberOptionMerge") ?? "0";
      if (rememberOption.value == "0") {
        compositeOption2[1] =
            (await WxStorage.instance.getInt("compositeOptionMerge1") ?? 0) == 1
                ? "1"
                : "0"; //慢动作视频效果与个性化 0选中  1选中 多选
        isShare.value =
            (await WxStorage.instance.getInt("isShareOptionMerge") ?? 0) == 1
                ? true
                : false;
      }
    }

    // BusUtils.instance.fire(EventAction(key: EventBusKey.uploadVideo));
    subscription = BusUtils.instance.on((action) {
      if (action.key == EventBusKey.uploadVideo) {
        getShootingRecords();
      } else if (action.key == EventBusKey.deleteLocalVideo1 ||
          action.key == EventBusKey.deleteLocalVideo2) {
        getdataList(controller: refreshController2, isLoad: false);
      }
    });
    getdataList(controller: refreshController2, isLoad: false);
  }

//改变选择视频index
  void changeVideoIndex(
    int index,
  ) {
    log("changeVideoIndex-1");
    indexVideoId.value = dataList[index].eventId ?? "";
    if (indexVideoId.value != "") {
      log("changeVideoIndex-2");
      reloadVideo(dataList[index].filePath ?? "");
    }
  }

  reloadVideo(String videoUrl) async {
    log("changeVideoIndex-4");
    if (videoUrl.isEmpty) {
      log("changeVideoIndex-5=$videoUrl");
      return;
    }
    log("changeVideoIndex-6=$videoUrl");
    videoController.setData(
      videoPath: videoUrl,
    );
    videoController.play();
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final selfieShotDao = database.selfieShotDao;
    List<ShotRecordModel> filteredNumbers = await selfieShotDao.findAllShot(
        trainingId.value,
        UserManager.instance.userInfo.value?.userId ?? "",
        type.value);
    dataList.assignAll(filteredNumbers);
    log("getShootingRecords2=${trainingId.value}");
    if (dataList.isNotEmpty) {
      changeVideoIndex(
        0,
      );
    } else {
      indexVideoId.value = "";
      videoController.setData(videoPath: '');
    }
    getShootingRecords();

    // for (int i = 0; i < dataList.length; i++) {
    //   final outputPath = await _generateFinalOutputPath();
    //   final editor = VideoEditorBuilder(videoPath: dataList[i].filePath ?? '')
    //     ..compress(resolution: VideoResolution.p720)
    //     ..generateThumbnail(positionMs: 7000, quality: 80)
    //     ..removeAudio(); // 静音

    //   // log("VideoMergePagemergedPath133z1=${jsonEncode(dataList)}");
    //   try {
    //     await editor.export(
    //       outputPath: outputPath,
    //     );
    //   } catch (e) {
    //     log("$e");
    //   }
    //   dataList[i].playerImagePath = outputPath;
    //   log("VideoMergePagemergedPath133z1=${i}  ${outputPath}");
    // }
    // dataList.refresh();
    // if (dataFag["isFrist"] as bool) {
    //   dataFag["isFrist"] = false;
    //   refresh();
    // }
    //TODO
    // var outputPath =
    //     "/storage/emulated/0/Android/data/com.shootZ.app.shoot_z/files/Movies/final_merge_1757422012563.mp4";
    // ShotRecordModel ssss = filteredNumbers.first;
    // ssss.filePath = outputPath;
    // ssss.eventId = "123123122231";
    // uploadController.addTasks([ssss]);
  }

  // Future<String> _generateFinalOutputPath() async {
  //   Directory dir2;
  //   if (Platform.isAndroid) {
  //     // Android专属目录（无需权限）
  //     final dir = await getExternalStorageDirectory();
  //     dir2 = Directory(path.join(dir!.path, 'Movies', 'merge_videos_image'));
  //   } else {
  //     // iOS处理
  //     var dir = await getApplicationDocumentsDirectory();
  //     dir2 = Directory(path.join(dir.path, 'merge_videos_image'));
  //   }
  //   if (!await dir2.exists()) await dir2.create(recursive: true);
  //   return path.join(dir2.path,
  //       'final_merge_img_${DateTime.now().millisecondsSinceEpoch}.png');
  // }

  //获得投篮记录列表
  getShootingRecords() async {
    Map<String, dynamic> param = {
      'trainingId': trainingId.value,
      'hasVideos': 1, //是否只查询有视频的事件记录
    };
    var url = ApiUrl.getShootinEventVideos(trainingId.value);
    var res = await Api().get(url, queryParameters: param);
    log("getShootingRecords2=${jsonEncode(res.data)}");
    if (res.isSuccessful()) {
      if (res.data != null) {
        var list = res.data ?? [];
        var modelList =
            list.map((e) => SelfieShotLocalInfoModel.fromJson(e)).toList();
        log("zzzzzz12removeAt-${res.data}");
        for (int i = 0; i < dataList.length; i++) {
          for (int a = 0; a < modelList.length; a++) {
            if (modelList[a].eventId == (dataList[i].eventId ?? "")) {
              dataList[i].newworkFilePath = modelList[a].videoPath;
              dataList[i].videoLoadOK = "1";
            }
          }
        }
      }
      dataList.refresh();
    } else {
      WxLoading.showToast(res.message);
    }
    checkAllVideo(isCheck: false);
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  //全选和取消全选
  Future<void> checkAllVideo({bool? isCheck}) async {
    allCheck.value = isCheck ?? !allCheck.value;
    for (int i = 0; i < dataList.length; i++) {
      dataList[i].isCheck = (allCheck.value) ? "1" : "0";
    }
    dataList.refresh();
  }

  deleteVideo() async {
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final selfieShotDao = database.selfieShotDao;
    var list = dataList.where((value) {
      return value.isCheck == "1";
    }).toList();
    for (int i = 0; i < list.length; i++) {
      if (list[i].isCheck == "1") {
        log("deleteVideo=${jsonEncode(list[i])}");
        selfieShotDao.deleteShot1(
            list[i].trainingId ?? "0",
            UserManager.instance.userInfo.value?.userId ?? "",
            list[i].eventId ?? "0");
        if (type.value == "1") {
          BusUtils.instance
              .fire(EventAction(key: EventBusKey.deleteLocalVideo1));
        } else {
          BusUtils.instance
              .fire(EventAction(key: EventBusKey.deleteLocalVideo2));
        }
        log("deleteVideo2=${type.value}");
        dataList.remove(list[i]);
        FileUtils.deleteFile(list[i].filePath ?? "", //list[i].filePath ??
            context: Get.context!,
            isShow: false);
        if (dataList.isEmpty) {
          AppPage.back();
        }
        //删除本地文件
      }

      // if (dataList[i].isCheck == "1") {
      //   selfieShotDao.deleteShot1(
      //       dataList[i].trainingId ?? "0",
      //       UserManager.instance.userInfo.value?.userId ?? "",
      //       dataList[i].eventId ?? "0");
      //   if (type.value == "1") {
      //     BusUtils.instance
      //         .fire(EventAction(key: EventBusKey.deleteLocalVideo1));
      //   } else {
      //     BusUtils.instance
      //         .fire(EventAction(key: EventBusKey.deleteLocalVideo2));
      //   }
      //   log("deleteVideo2=${type.value}");
      //   dataList.removeAt(i);
      //   FileUtils.deleteFile(dataList[i].filePath ?? "", context: Get.context!);
      //   //删除本地文件
      // }
    }
  }

  Future<void> uploadVideo() async {
    // // 申请权限
    // if (defaultTargetPlatform == TargetPlatform.android) {
    //   // 请求基本的前台服务权限
    //   var status = await Permission.notification.request();
    //   if (!status.isGranted) {
    //     print('前台服务权限被拒绝');
    //     return;
    //   }
    // }
    var list = dataList.where((value) {
      return value.isCheck == "1" && value.videoLoadOK != "1";
    }).toList();
    if (list.isEmpty) {
      WxLoading.showToast("请选择一个未上传的视频");
      return;
    }
    //uploadVideo=/storage/emulated/0/Android/data/com.shootZ.app.shoot_z/files/Movies/final_videos/extended_last_20250827_171729.mp4
    // var list2 = await Utils.listFiles(
    //     "/storage/emulated/0/Android/data/com.shootZ.app.shoot_z/files/Movies/final_videos");
    // log("uploadVideo1=$list2");
    uploadController.addTasks(list);
  }

//将视频保存到相册
  Future<void> loadVideo() async {
    var list = dataList.where((value) {
      return value.isCheck == "1";
    }).toList();
    Utils.localDownloadAndSaveToPhotoAlbum(list);
  }

  // // 获取视频缩略图（用于预览）
  // static Future<Uint8List?> getVideoThumbnail(String videoPath) async {
  //   try {
  //     final thumbnail = await VideoThumbnail.thumbnailData(
  //       video: videoPath,
  //       imageFormat: ImageFormat.JPEG,
  //       maxWidth: 200,
  //       quality: 75,
  //     );
  //     return thumbnail;
  //   } catch (e) {
  //     print('获取视频缩略图失败: $e');
  //     return null;
  //   }
  // }

  @override
  void onClose() {
    subscription?.cancel();
    videoController.pause();
    videoController.videoPlayerController?.setVolume(0);
    videoController.videoPlayerController?.seekTo(const Duration(seconds: 0));
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
    // 强制GC（针对视频编解码器泄漏）
    SystemChannels.platform.invokeMethod('Memory.forceGC');
    super.onClose();
  }

  @override
  void dispose() {
    subscription?.cancel();

    // 停止所有动画
    videoController.dispose();
    // 释放视频编解码器
    SystemChannels.platform.invokeMethod('VideoPlayer.disposeAll');
    super.dispose();
  }

  //合成视频
  Future<void> compositeVideo(List<dynamic> list2) async {
    if (rememberOption.value == "0") {
      await WxStorage.instance.setString(
          "rememberOptionMerge", rememberOption.value); //记住我的选择 0记住  1不记住
      await WxStorage.instance.setInt(
          "isShareOptionMerge", isShare.value ? 1 : 0); //是否 共享到场地展示为精彩视频
      await WxStorage.instance.setInt("compositeOptionMerge1",
          (compositeOption2[1] != "1") ? 0 : 1); //慢动作视频效果与个性化 0选中  1选中 多选
    }
  }
}
