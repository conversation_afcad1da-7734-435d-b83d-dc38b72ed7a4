import 'dart:convert';
import 'dart:developer';

import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';
import 'package:shoot_z/pages/tab/upload/GlobalButtonManager.dart';
import 'package:shoot_z/pages/tab/upload/NotificationService.dart';
import 'package:shoot_z/pages/tab/upload/StorageService.dart';
import 'package:shoot_z/pages/tab/upload/UploadTaskManager.dart';
import 'package:shoot_z/utils/event_bus.dart';

class UploadTask {
  final String id;
  final String filePath;
  final ShotRecordModel shotRecordModel;

  // 所有可变状态都改为可观察的
  final RxDouble progress; // 进度
  final RxString status; // 状态文本
  final RxBool isCompleted; // 是否完成
  final RxBool isFailed; // 是否失败

  UploadTask({
    required this.filePath,
    required this.shotRecordModel,
    double progress = 0.0,
    String status = '等待上传',
    bool isCompleted = false,
    bool isFailed = false,
  })  : id = '${shotRecordModel.eventId}',
        progress = progress.obs,
        status = status.obs,
        isCompleted = isCompleted.obs,
        isFailed = isFailed.obs;

  // 转换为JSON（用于持久化存储）
  Map<String, dynamic> toJson() => {
        'id': id,
        'filePath': filePath,
        'shotRecordModel': shotRecordModel.toJson(),
        'progress': progress.value,
        'status': status.value,
        'isCompleted': isCompleted.value,
        'isFailed': isFailed.value,
      };

  // 从JSON创建（用于从存储恢复）
  factory UploadTask.fromJson(Map<String, dynamic> json) {
    return UploadTask(
      filePath: json['filePath'],
      shotRecordModel: ShotRecordModel.fromJson(json['shotRecordModel']),
      progress: json['progress'] ?? 0.0,
      status: json['status'] ?? '等待上传',
      isCompleted: json['isCompleted'] ?? false,
      isFailed: json['isFailed'] ?? false,
    );
  }

  // 便捷方法：标记为上传中
  void markAsUploading() {
    status.value = '上传中';
    isCompleted.value = false;
    isFailed.value = false;
  }

  // 便捷方法：标记为完成
  void markAsCompleted() {
    progress.value = 1.0;
    status.value = '上传完成';
    isCompleted.value = true;
    isFailed.value = false;
  }

  // 便捷方法：标记为失败
  void markAsFailed([String? error]) {
    status.value = error ?? '上传失败';
    isCompleted.value = false;
    isFailed.value = true;
  }
}

// String _generateTaskId(String filePath) {
//   // 生成唯一任务ID（基于文件路径和哈希值）
//   return 'task_${filePath.hashCode}';
// }

class UploadController extends GetxController {
  final RxList<UploadTask> _tasks = <UploadTask>[].obs;
  final RxBool _isUploading = false.obs;
  final RxDouble _buttonPositionX = (-1.0).obs; // 初始值设为-1表示未设置
  final RxDouble _buttonPositionY = (-1.0).obs;
  final RxBool _onlyWifiUpload = true.obs;
  final RxBool _autoUploadWhenCharging = false.obs;
  final RxInt _maxConcurrentUploads = 3.obs;
  // 全局上传状态
  final Rx<UploadStatus> globalStatus = UploadStatus.idle.obs;
  var isShowMaxDialoh = false.obs; //true大窗 false小窗
  List<UploadTask> get tasks => _tasks;
  // 使isCompleted可观察
  RxBool get isUploading => _isUploading;
  double get buttonPositionX => _buttonPositionX.value;
  double get buttonPositionY => _buttonPositionY.value;
  RxBool get onlyWifiUpload => _onlyWifiUpload;
  RxBool get autoUploadWhenCharging => _autoUploadWhenCharging;
  RxInt get maxConcurrentUploads => _maxConcurrentUploads;

  RxInt get totalTasks => _tasks.length.obs;
  RxInt get failedTasks =>
      _tasks.where((task) => task.isFailed.value).length.obs;
  RxInt get completedTasks =>
      _tasks.where((task) => task.isCompleted.value).length.obs;
  RxDouble get progress {
    if (_tasks.isEmpty) return (0.0).obs;
    // 1. 先计算所有任务进度的总和
    final totalProgress =
        _tasks.fold(0.0, (sum, task) => sum + task.progress.value);
    return (totalProgress / _tasks.length).obs;
  }

  // 更新全局状态
  void _updateGlobalStatus(int type) {
    Future.delayed(const Duration(milliseconds: 300)).then((onValue) {
      if (_tasks.isEmpty) {
        globalStatus.value = UploadStatus.idle;
      } else if (_isUploading.value) {
        globalStatus.value = UploadStatus.uploading;
      } else if (_tasks.every((t) => t.isCompleted.value)) {
        globalStatus.value = UploadStatus.completed;
        BusUtils.instance.fire(EventAction(key: EventBusKey.uploadVideo));
        resetUpload();
      } else if (_tasks.any((t) => t.isFailed.value)) {
        globalStatus.value = UploadStatus.failed;
        resetUpload();
        BusUtils.instance.fire(EventAction(key: EventBusKey.uploadVideo));
      } else {
        globalStatus.value = UploadStatus.paused;
      }
      log("_updateGlobalStatus=${globalStatus.value}-${failedTasks.value}-${completedTasks.value}-${totalTasks.value}");

      // 通知所有监听者
      update();

      // 发送全局通知
      //  _sendStatusNotification(type);
    });
  }

  // 发送全局状态通知
  void _sendStatusNotification(int type) {
    switch (globalStatus.value) {
      case UploadStatus.uploading:
        // log("_sendStatusNotification$type");
        if (type == 1) {
          Get.find<NotificationService>().showUploadingNotification();
        }
        break;
      case UploadStatus.completed:
        Get.find<NotificationService>().showCompletedNotification();
        break;
      case UploadStatus.failed:
        Get.find<NotificationService>().showFailedNotification();
        break;
      case UploadStatus.paused:
        Get.find<NotificationService>().showPausedNotification();
        break;
      default:
        break;
    }
  }

  @override
  void onInit() {
    super.onInit();
    _loadSettings();
    _loadButtonPosition();
    //_addTestTasks();
    // 在状态变化时调用
    ever(_isUploading, (_) => _updateGlobalStatus(1));
    ever(_tasks, (_) => _updateGlobalStatus(2));
  }

  // void _addTestTasks() {
  //   final testTasks = List.generate(
  //       5,
  //       (index) =>
  //           '/storage/emulated/0/Android/data/com.shootZ.app.shoot_z/files/Movies/video_$index.mp4');
  //   addTasks(testTasks);
  // }

  Future<void> _loadSettings() async {
    _onlyWifiUpload.value =
        await StorageService.getData('only_wifi_upload', true);
    _autoUploadWhenCharging.value =
        await StorageService.getData('auto_upload_charging', false);
    _maxConcurrentUploads.value =
        await StorageService.getData('max_concurrent_uploads', 3);
  }

  Future<void> _loadButtonPosition() async {
    final x = await StorageService.getData('button_position_x', -1.0);
    final y = await StorageService.getData('button_position_y', -1.0);

    // 如果位置未设置，使用默认右下角位置
    if (x == -1.0 || y == -1.0) {
      _buttonPositionX.value = -1.0;
      _buttonPositionY.value = -1.0;
    } else {
      _buttonPositionX.value = x;
      _buttonPositionY.value = y;
    }
  }

  Future<void> _saveButtonPosition() async {
    await StorageService.saveData('button_position_x', _buttonPositionX.value);
    await StorageService.saveData('button_position_y', _buttonPositionY.value);
  }

  Future<void> saveSettings() async {
    await StorageService.saveData('only_wifi_upload', _onlyWifiUpload.value);
    await StorageService.saveData(
        'auto_upload_charging', _autoUploadWhenCharging.value);
    await StorageService.saveData(
        'max_concurrent_uploads', _maxConcurrentUploads.value);
  }

  void updateButtonPosition(double x, double y) {
    _buttonPositionX.value = x;
    _buttonPositionY.value = y;
    _saveButtonPosition();
  }

  void simulateUpload(UploadTask task) async {
    for (double progress = 0.0; progress < 1.0; progress += 0.1) {
      await Future.delayed(const Duration(seconds: 1));
      task.progress.value = progress;
      task.status.value = '上传中 ${(progress * 100).toStringAsFixed(0)}%';
      // 触发列表更新
      // tasks.refresh();
      update(); // 通知 UI 更新
    }

    task.progress.value = 1.0;
    task.status.value = '上传成功';
    task.isCompleted.value = true;
    log("_updateGlobalStatu4=${completedTasks.value}-${failedTasks.value}");
    // 触发列表更新
    // tasks.refresh();
    update(); // 通知 UI 更新
    if (tasks.every((t) => t.isCompleted.value || t.isFailed.value)) {
      _isUploading.value = false;
    }
  }

  void addTasks(List<ShotRecordModel> filePaths) {
    // final newTasks = filePaths
    //     .map((shotRecordModel) => UploadTask(
    //         filePath: shotRecordModel.filePath ?? "",
    //         shotRecordModel: shotRecordModel))
    //     .toList();
    // _tasks.addAll(newTasks);
    log("addTasksaddTasks=${jsonEncode(filePaths)}");
    var ishava = true;
    var isCompleted = true;
    for (var item in filePaths) {
      final task = _tasks.firstWhereOrNull((t) => t.id == item.eventId);
      if (task != null) {
        if (!task.isCompleted.value) {
          isCompleted = false;
        }
      } else {
        ishava = false;
        final newTasks =
            UploadTask(filePath: item.filePath ?? "", shotRecordModel: item);
        _tasks.add(newTasks);
      }
    }
    if (ishava) {
      if (isCompleted) {
        WxLoading.showToast("这些任务,已经上传完成");
      } else {
        WxLoading.showToast("已经添加该任务");
      }
    } else {
      // 添加任务后显示悬浮按钮
      GlobalButtonManager.show();
      startUpload();
    }
  }

  void addTask(ShotRecordModel shotRecordModel) {
    final task =
        _tasks.firstWhereOrNull((t) => t.id == shotRecordModel.eventId);
    if (task != null) {
      if (!task.isCompleted.value) {
        WxLoading.showToast("该任务,已经上传完成");
      } else {
        WxLoading.showToast("已经添加该任务");
      }
    } else {
      final newTasks = UploadTask(
          filePath: shotRecordModel.filePath ?? "",
          shotRecordModel: shotRecordModel);
      _tasks.add(newTasks);
      // 添加任务后显示悬浮按钮
      GlobalButtonManager.show();
      startUpload();
    }
  }

  void startUpload() {
    UploadTaskManager.resumeProcessing();
    log("startUpload1");
    if (_tasks.isEmpty || _isUploading.value) return;
    log("startUpload2");
    var isCompleted = true;
    for (var task in _tasks) {
      if (!task.isCompleted.value) {
        isCompleted = false;
      }
    }
    if (isCompleted) {
      WxLoading.showToast("这些任务,已经上传完成");
      resetUpload();
      return;
    }
    log("startUpload3");
    _isUploading.value = true;
    for (final task in _tasks) {
      if (!task.isCompleted.value && !task.isFailed.value) {
        UploadTaskManager.addTask(task.shotRecordModel);
      }
    }
  }

  // 添加缺失的方法
  void markTaskCompleted(String taskId) {
    final task = _tasks.firstWhereOrNull((t) => t.id == taskId);
    if (task != null) {
      task.progress.value = 1.0;
      task.status.value = '上传成功';
      task.isCompleted.value = true;
      log("_updateGlobalStatus5=${completedTasks.value}-${failedTasks.value}");
      update();
    }
  }

  // 添加缺失的方法
  void markTaskFailed(String taskId, String error) {
    final task = _tasks.firstWhereOrNull((t) => t.id == taskId);
    if (task != null) {
      task.status.value = '上传失败: $error';
      task.isFailed.value = true;
      log("_updateGlobalStatus3=${completedTasks.value}-${failedTasks.value}");
      update();
    }
  }

  // 添加缺失的方法
  void updataData(UploadTask task) {
    if ((tasks.length == (completedTasks.value + failedTasks.value)) ||
        progress.value >= 1) {
      _isUploading.value = false;
    }
    // log("processTaskprocessTask22=${progress.value}");
    log("_updateGlobalStatus2=${completedTasks.value}-${failedTasks.value}");
    if (completedTasks.value == totalTasks.value) {
      globalStatus.value = UploadStatus.completed;
      resetUpload();
    }
    update();
  }

  void pauseUpload() {
    _isUploading.value = false;
    UploadTaskManager.pauseProcessing();
  }

  void resumeUpload() {
    //    _isUploading.value = false;
    if (!_isUploading.value) {
      _isUploading.value = true;
      for (final task in _tasks) {
        if (!task.isCompleted.value && !task.isFailed.value) {
          UploadTaskManager.addTask(task.shotRecordModel);
        }
      }
      UploadTaskManager.resumeProcessing();
    }
  }

  void resetUpload() {
    _tasks.clear();
    _isUploading.value = false;
    // 清空任务后隐藏悬浮按钮
    GlobalButtonManager.hide();
  }

  void setOnlyWifiUpload(bool value) {
    _onlyWifiUpload.value = value;
    saveSettings();
  }

  void setAutoUploadWhenCharging(bool value) {
    _autoUploadWhenCharging.value = value;
    saveSettings();
  }

  void setMaxConcurrentUploads(int value) {
    _maxConcurrentUploads.value = value;
    saveSettings();
  }
}

enum UploadStatus {
  idle, // 空闲状态
  uploading, // 上传中
  paused, // 已暂停
  completed, // 已完成
  failed, // 有失败任务
}
