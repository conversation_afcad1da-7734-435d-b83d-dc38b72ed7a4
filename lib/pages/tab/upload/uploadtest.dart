// ignore_for_file: avoid_print, deprecated_member_use

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:math' as cc;
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';
import 'package:shoot_z/network/model/tencent_cos_model.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/BlockingQueue.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/tencentcos/FetchCredentials.dart';
import 'package:tencentcloud_cos_sdk_plugin/cos.dart';
import 'package:tencentcloud_cos_sdk_plugin/cos_transfer_manger.dart';
import 'package:tencentcloud_cos_sdk_plugin/pigeon.dart' as pigeon;
import 'package:flutter/material.dart';
import 'package:ui_packages/ui_packages.dart';

class Uploadtest extends GetxController with GetSingleTickerProviderStateMixin {
  static const platform =
      MethodChannel('com.example.my_flutter_app/native_method');

  Future<void> _getResultToApp(Map<String, dynamic> param) async {
    try {
      await platform.invokeMethod('uploadResult', param);
    } on PlatformException catch (e) {
      print("native_method：Failed to invoke native method: '${e.message}'.");
    }
  }

  BlockingQueue<ShotRecordModel> blockingQueue = BlockingQueue(10);
  TabController? tabController;
  var tabbarIndex = 0.obs;
  var uploadListLength = 0.obs;
  var isEnd = false.obs;
  var videolength = 0.obs;

  // 文件上传状态
  var fileUploadStatus = <String, FileUploadStatus>{}.obs;

  // 重试机制配置
  static const maxRetryCount = 3;
  static const initialRetryDelay = 1000; // 1秒
  static const maxRetryDelay = Duration(minutes: 1);
  @override
  void onInit() {
    super.onInit();
  }

  showUploadListDialog(BuildContext context, String trainingId1) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context2) {
        return WillPopScope(
          onWillPop: () async => false,
          child: Obx(() {
            return Padding(
              padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
              child: Material(
                type: MaterialType.transparency,
                child: Center(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          color: Colors.transparent,
                          child: Column(
                            children: [
                              Container(
                                constraints: BoxConstraints(
                                  maxHeight: 220.w,
                                  minHeight: 185.w,
                                ),
                                decoration: BoxDecoration(
                                  color: Colours.color191921,
                                  borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(25.r),
                                    bottomRight: Radius.circular(25.r),
                                  ),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 18, vertical: 2),
                                width: double.infinity,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    const SizedBox(height: 30),
                                    Center(
                                      child: Text(
                                        "提示",
                                        style: TextStyles.regular.copyWith(
                                            color: Colours.white,
                                            fontSize: 18.sp),
                                      ),
                                    ),
                                    const SizedBox(height: 20),
                                    Text(
                                      "视频正在上传，剩余${uploadListLength.value}个视频",
                                      style: TextStyles.regular.copyWith(
                                          color: Colours.white,
                                          fontSize: 14.sp),
                                      maxLines: 20,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 50),
                                    GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () {
                                        if (uploadListLength.value > 0) {
                                          WxLoading.showToast(
                                              "请稍等，剩余${uploadListLength.value}视频");
                                        } else {
                                          AppPage.back();
                                          Future.delayed(const Duration(
                                                  milliseconds: 100))
                                              .then((onValue) {});
                                        }
                                      },
                                      child: Container(
                                        height: 46.w,
                                        width: double.infinity,
                                        alignment: Alignment.center,
                                        margin: EdgeInsets.only(right: 10.w),
                                        padding: EdgeInsets.only(
                                            left: 5.w,
                                            right: 5.w,
                                            top: 3.w,
                                            bottom: 3.w),
                                        decoration: BoxDecoration(
                                          color: Colours.color282735,
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(28.r)),
                                          gradient: uploadListLength.value <= 0
                                              ? const LinearGradient(
                                                  colors: [
                                                    Colours.color7732ED,
                                                    Colours.colorA555EF
                                                  ],
                                                  begin: Alignment.bottomLeft,
                                                  end: Alignment.bottomRight,
                                                )
                                              : null,
                                        ),
                                        child: Text(
                                          "生成报告",
                                          style: TextStyles.titleMedium18
                                              .copyWith(fontSize: 15.sp),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }

  @override
  void onReady() {
    super.onReady();
    getUploadqueue();
  }

  getUploadqueue() async {
    while (true) {
      var data = await blockingQueue.dequeue();
      await uploadShotRecord(data);
    }
  }

  postHalfShootingResource(
      ShotRecordModel shotRecordModel, String uploadPath, int type) async {
    Map<String, dynamic> param = {
      "eventId": shotRecordModel.eventId,
      "path": uploadPath,
      "resType": type, //1图片  2视频
    };

    var url =
        await ApiUrl.halfShootingResource(shotRecordModel.trainingId ?? "");
    var res = await Api().post(url, data: [param]);

    if (res.isSuccessful()) {
      log("资源上传成功: ${shotRecordModel.eventId} $uploadPath");
    } else {
      WxLoading.showToast(res.message);
    }
  }

  Future<void> uploadShotRecord(ShotRecordModel shotRecordModel) async {
    try {
      final tencentCosModel = await fetchSessionCredentials2();
      await Cos().forceInvalidationCredential();
      await Cos().initWithSessionCredential(FetchCredentials());
      final serviceConfig = pigeon.CosXmlServiceConfig(
        region: tencentCosModel.region ?? "ap-guangzhou",
        isDebuggable: true,
        isHttps: true,
      );

      final transferManager = await _initializeTransferManager(serviceConfig);
      // 并行处理视频和图片上传
      await Future.wait([
        _processFile(
          shotRecordModel,
          shotRecordModel.filePath,
          "video",
          "mobile/videos/",
          transferManager,
          tencentCosModel.bucketName ?? "",
          (url) => postHalfShootingResource(shotRecordModel, url, 2),
        ),
      ]);
    } catch (e) {
      log("uploadShotRecord error: $e");
      _getResultToApp({'success': false, 'message': '上传失败: $e'});

      // 失败后重新加入队列
      await Future.delayed(const Duration(seconds: 5));
      blockingQueue.enqueue(shotRecordModel);
    } finally {
      uploadListLength.value = await blockingQueue.getLength();
    }
  }

  Future<CosTransferManger> _initializeTransferManager(
      pigeon.CosXmlServiceConfig serviceConfig) async {
    Cos().registerDefaultService(serviceConfig);

    final transferConfig = pigeon.TransferConfig(
      forceSimpleUpload: false,
      enableVerification: true,
      divisionForUpload: 2097152,
      sliceSizeForUpload: 1048576,
    );

    await Cos().registerDefaultTransferManger(serviceConfig, transferConfig);
    return Cos().getDefaultTransferManger();
  }

  Future<void> _processFile(
    ShotRecordModel shotRecordModel,
    String? filePath,
    String type,
    String remotePrefix,
    CosTransferManger transferManager,
    String bucketName,
    Function(String) onSuccess,
  ) async {
    if (filePath == null || filePath.isEmpty) {
      _getResultToApp({'success': false, 'message': '$type路径为空'});
      return;
    }

    // 检查文件是否已成功上传
    if (_isFileAlreadyUploaded(filePath)) {
      log("$type 文件已经成功上传，跳过: $filePath");
      return;
    }

    // 检查文件状态
    final fileStatus = fileUploadStatus[filePath] ??
        FileUploadStatus(
          path: filePath,
          status: UploadStatus.pending,
          retryCount: 0,
          lastError: '',
        );

    // 检查文件是否存在
    if (!await _fileExists(filePath)) {
      _handleFileNotFound(type, filePath, fileStatus);
      return;
    }

    // 检查文件大小
    if (await _isZeroSizeFile(filePath)) {
      _handleZeroSizeFile(type, filePath, fileStatus);
      return;
    }

    // 生成远程存储路径
    final remoteKey = _generateRemoteKey(filePath, remotePrefix);

    try {
      await _uploadWithRetry(
        shotRecordModel,
        transferManager,
        bucketName,
        remoteKey,
        filePath,
        type,
        fileStatus,
        onSuccess,
      );
    } catch (e) {
      log("$type 上传失败，达到最大重试次数: $filePath, 错误: $e");
      _getResultToApp(
          {'success': false, 'message': '$type上传失败: ${e.toString()}'});

      // 将整个记录重新加入队列
      await Future.delayed(const Duration(seconds: 3));
      blockingQueue.enqueue(shotRecordModel);
    }
  }

  bool _isFileAlreadyUploaded(String filePath) {
    final status = fileUploadStatus[filePath];
    return status != null && status.status == UploadStatus.success;
  }

  void _handleFileNotFound(
      String type, String filePath, FileUploadStatus status) {
    log("$type文件不存在: $filePath");
    _getResultToApp({'success': false, 'message': '$type文件不存在: $filePath'});

    status.status = UploadStatus.failed;
    status.retryCount = maxRetryCount;
    status.lastError = "文件不存在";
    fileUploadStatus[filePath] = status;
  }

  void _handleZeroSizeFile(
      String type, String filePath, FileUploadStatus status) {
    log("$type文件大小为0: $filePath");
    _getResultToApp({'success': false, 'message': '$type文件大小为0: $filePath'});

    status.status = UploadStatus.failed;
    status.retryCount = maxRetryCount;
    status.lastError = "文件大小为0";
    fileUploadStatus[filePath] = status;
  }

  Future<void> _uploadWithRetry(
    ShotRecordModel shotRecordModel,
    CosTransferManger transferManager,
    String bucketName,
    String remoteKey,
    String filePath,
    String type,
    FileUploadStatus status,
    Function(String) onSuccess,
  ) async {
    int attempt = 0;
    final random = cc.Random();

    // 设置初始状态
    status.status = UploadStatus.retrying;
    status.retryCount = 0;
    status.lastError = '';
    fileUploadStatus[filePath] = status;

    while (attempt < maxRetryCount) {
      attempt++;
      status.retryCount = attempt;
      fileUploadStatus[filePath] = status;

      log("尝试上传 $type (第 $attempt 次): $filePath");

      try {
        // 执行上传
        await _uploadToCos(
            transferManager, bucketName, remoteKey, filePath, type, onSuccess);

        // 上传成功
        status.status = UploadStatus.success;
        status.retryCount = 0;
        status.lastError = '';
        fileUploadStatus[filePath] = status;

        log("$type 上传成功: $filePath");
        return;
      } catch (e) {
        // 更新状态
        status.lastError = e.toString();
        fileUploadStatus[filePath] = status;

        log("$type 上传失败 (第 $attempt 次): $filePath, 错误: $e");

        if (attempt < maxRetryCount) {
          // 计算指数退避延迟
          final delay = _calculateRetryDelay(attempt, random);
          log("将在 ${delay.inMilliseconds} 毫秒后重试...");

          // 等待一段时间后重试
          await Future.delayed(delay);
        }
      }
    }

    // 达到最大重试次数仍然失败
    status.status = UploadStatus.failed;
    fileUploadStatus[filePath] = status;
    throw Exception("$type 上传失败，达到最大重试次数: $filePath");
  }

  Duration _calculateRetryDelay(int attempt, cc.Random random) {
    // 指数退避算法
    final baseDelay = initialRetryDelay * cc.pow(2, attempt - 1);

    // 添加随机抖动（避免所有客户端同时重试）
    final jitter = random.nextInt(500);

    // 限制最大延迟时间
    final delayMs = cc.min(baseDelay + jitter, maxRetryDelay.inMilliseconds);

    return Duration(milliseconds: delayMs.toInt());
  }

  Future<void> _uploadToCos(
    CosTransferManger transferManager,
    String bucketName,
    String remoteKey,
    String filePath,
    String type,
    Function(String) onSuccess,
  ) async {
    final completer = Completer<pigeon.CosXmlResult>();

    // 创建结果监听器
    final listener = ResultListener((headers, result) {
      if (!completer.isCompleted) {
        completer.complete(result);
      }
    }, (clientException, serviceException) {
      if (!completer.isCompleted) {
        completer.completeError(
          clientException ?? serviceException ?? Exception('上传失败'),
        );
      }
    });

    // 创建上传任务
    //  final uploadTask =
    await transferManager.upload(
      bucketName,
      remoteKey,
      filePath: filePath,
      resultListener: listener,
    );

    // 等待上传完成
    try {
      final result = await completer.future;
      final url = result.accessUrl ?? "无法获取URL";
      log("$type 上传成功: $url");
      onSuccess(url);
      _getResultToApp(
          {'success': true, 'accessUrl': url, 'filePath': filePath});
    } catch (e) {
      log("$type 上传失败: $e");
      _getResultToApp(
          {'success': false, 'message': '$type上传失败: ${e.toString()}'});
      rethrow; // 重新抛出以便重试机制捕获
    }
  }

  Future<bool> _fileExists(String filePath) async {
    try {
      return await File(filePath).exists();
    } catch (e) {
      log("检查文件存在失败: $e");
      return false;
    }
  }

  Future<bool> _isZeroSizeFile(String filePath) async {
    try {
      final file = File(filePath);
      final length = await file.length();
      return length == 0;
    } catch (e) {
      log("检查文件大小失败: $e");
      return true;
    }
  }

  String _generateRemoteKey(String filePath, String prefix) {
    final fileExtension = filePath.substring(filePath.lastIndexOf("."));
    return "$prefix${DateTime.now().millisecondsSinceEpoch}$fileExtension";
  }

  Future<TencentCosModel> fetchSessionCredentials2() async {
    final httpClient = HttpClient();
    try {
      final request = await httpClient
          .getUrl(Uri.parse("https://i.shootz.tech/mgr-api/common/sts-data"));
      final response = await request.close();

      if (response.statusCode == HttpStatus.ok) {
        final json = await response.transform(utf8.decoder).join();
        return TencentCosModel.fromJson(jsonDecode(json));
      } else {
        throw Exception("获取临时密钥失败: ${response.statusCode}");
      }
    } catch (e) {
      log("fetchSessionCredentials2 error: $e");
      throw Exception("获取临时密钥失败: $e");
    } finally {
      httpClient.close();
    }
  }
}

enum UploadStatus {
  pending, // 等待上传
  uploading, // 上传中
  success, // 上传成功
  retrying, // 重试中
  failed, // 上传失败
}

class FileUploadStatus {
  final String path;
  UploadStatus status;
  int retryCount;
  String lastError;

  FileUploadStatus({
    required this.path,
    this.status = UploadStatus.pending,
    this.retryCount = 0,
    this.lastError = '',
  });

  @override
  String toString() {
    return 'FileUploadStatus{path: $path, status: $status, retryCount: $retryCount, lastError: $lastError}';
  }
}
