import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:shoot_z/database/dao/SelfieShotDao.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab/upload/UploadController.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:tencentcloud_cos_sdk_plugin/cos_transfer_manger.dart';
import 'package:tencentcloud_cos_sdk_plugin/pigeon.dart' as pigeon;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';
import 'package:shoot_z/network/model/tencent_cos_model.dart';
import 'package:shoot_z/utils/tencentcos/FetchCredentials.dart';
import 'package:tencentcloud_cos_sdk_plugin/cos.dart';

class CosUploadService extends GetxService {
  final logger = Logger();
  final UploadController uploadController = Get.find();

  // 最大重试次数
  static const int maxRetryCount = 3;

  // 初始重试延迟（毫秒）
  static const int initialRetryDelay = 1000;

  // 最大重试延迟（毫秒）
  static const Duration maxRetryDelay = Duration(seconds: 30);

  // 文件上传状态跟踪
  final RxMap<String, FileUploadStatus> fileUploadStatus =
      <String, FileUploadStatus>{}.obs;

  // 存储已完成的任务ID
  final RxSet<String> completedTaskIds = <String>{}.obs;

  // 存储任务重试次数
  final Map<String, int> taskRetryCounts = {};
  late final AppDatabase? database;
  late final SelfieShotDao? selfieShotDao;
  @override
  void onInit() {
    super.onInit();
    _loadCompletedTasks();
  }

  Future<void> _loadCompletedTasks() async {
    //  final prefs = await SharedPreferences.getInstance();
    // final completedIds = prefs.getStringList('completed_task_ids') ?? [];
    // completedTaskIds.addAll(completedIds);
    database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    selfieShotDao = database?.selfieShotDao;
  }

  Future<void> _saveCompletedTasks() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('completed_task_ids', completedTaskIds.toList());
  }

  // 添加上传任务
  void addUploadTask(ShotRecordModel record, taskId) {
    log("processTaskprocessTask4=");
    // 检查任务是否已完成
    if (completedTaskIds.contains(taskId)) {
      //logger.i("任务已成功完成，跳过: $taskId");
      // // 更新任务进度
      final task = uploadController.tasks
          .firstWhereOrNull((t) => t.shotRecordModel.eventId == taskId);
      if (task != null) {
        task.progress.value = 1.0;
        task.isCompleted.value = true;
        task.status.value = '上传成功';
        uploadController.updataData(task);
      }
      return;
    }
    log("processTaskprocessTask5=");
    // 检查任务是否已存在
    if (fileUploadStatus.containsKey(taskId)) {
      logger.i("任务已存在，跳过添加: $taskId");
      return;
    }
    log("processTaskprocessTask6=");
    // 创建新任务状态
    fileUploadStatus[taskId] = FileUploadStatus(
      taskId: taskId,
      filePath: record.filePath!,
      status: UploadStatus.pending,
      progress: 0.0,
      retryCount: 0,
      lastError: '',
    );
    // 开始上传
    _processUpload(record, taskId);
  }

  // 处理上传任务
  Future<void> _processUpload(ShotRecordModel record, String taskId) async {
    // 获取任务状态
    final status = fileUploadStatus[taskId]!;
    log("processTaskprocessTask7=");
    try {
      // 更新状态为上传中
      _updateTaskStatus(taskId, UploadStatus.uploading, 0.0);

      // 获取腾讯云凭证
      final tencentCosModel = await fetchSessionCredentials2();

      // 初始化 COS 服务
      await _initializeCosService(tencentCosModel);

      // 获取传输管理器
      final transferManager = Cos().getDefaultTransferManger();

      // 执行上传
      await _uploadWithRetry(
        record,
        tencentCosModel.bucketName ?? "",
        taskId,
        transferManager,
        status,
      );
      log("processTaskprocessTask8=");
      // 标记任务完成
      _markTaskCompleted(taskId);
    } catch (e) {
      logger.e("任务上传失败: $taskId, 错误: $e");
      log("processTaskprocessTask9=任务上传失败: $taskId, 错误: $e");
      _handleUploadFailure(taskId, e.toString());
    }
  }

  // 带重试机制的上传
  Future<void> _uploadWithRetry(
    ShotRecordModel record,
    String bucketName,
    String taskId,
    CosTransferManger transferManager,
    FileUploadStatus status,
  ) async {
    int attempt = 0;
    final random = math.Random();

    while (attempt < maxRetryCount) {
      attempt++;
      status.retryCount = attempt;
      fileUploadStatus[taskId] = status;

      logger.i("尝试上传 (第 $attempt 次): $taskId");
      log("processTaskprocessTask10=");
      try {
        // 更新状态为重试中
        _updateTaskStatus(
            taskId,
            attempt > 1 ? UploadStatus.retrying : UploadStatus.uploading,
            status.progress);
        var time = DateTime.now().millisecondsSinceEpoch;
        // 执行上传
        await _uploadToCos(
          transferManager,
          bucketName,
          record.filePath!,
          taskId,
          status,
          record.trainingId ?? time.toString(),
        );

        return; // 上传成功，退出循环
      } catch (e) {
        logger.e("上传失败 (第 $attempt 次): $taskId, 错误: $e");

        // 更新错误信息
        status.lastError = e.toString();
        fileUploadStatus[taskId] = status;
        _updateTaskStatus(taskId, UploadStatus.failed, status.progress,
            error: e.toString());

        if (attempt < maxRetryCount) {
          // 计算指数退避延迟
          final delay = _calculateRetryDelay(attempt, random);
          logger.i("将在 ${delay.inMilliseconds} 毫秒后重试...");

          // 等待一段时间后重试
          await Future.delayed(delay);
        }
      }
    }

    // 达到最大重试次数仍然失败
    throw Exception("任务上传失败，达到最大重试次数: $taskId");
  }

  // 实际执行上传到 COS
  Future<void> _uploadToCos(
    CosTransferManger transferManager,
    String bucketName,
    String filePath,
    String eventId,
    FileUploadStatus status,
    String tid,
  ) async {
    log("processTaskprocessTask11=");

    // 生成远程存储路径
    final remoteKey = _generateRemoteKey(filePath,
        "mobile/videos/${UserManager.instance.user?.userId ?? ""}/$tid/video/");
    final completer = Completer<pigeon.CosXmlResult>();
    // 创建结果监听器
    final listener = ResultListener((headers, result) {
      if (!completer.isCompleted) {
        completer.complete(result);
      }
    }, (clientException, serviceException) {
      if (!completer.isCompleted) {
        completer.completeError(
          clientException ?? serviceException ?? Exception('上传失败'),
        );
      }
    });
    log("processTaskprocessTask12=");
    // 创建上传任务
    // final uploadTask =
    await transferManager.upload(
      bucketName,
      remoteKey,
      filePath: filePath,
      resultListener: listener,
      progressCallBack: (complete, total) {
        // 进度回调
        final progress = total > 0 ? complete / total : 0.0;
        _updateTaskProgress(eventId, progress);
      },
    );
    // 等待上传完成
    try {
      final result = await completer.future;
      final url = result.accessUrl ?? "无法获取URL";
      log("processTaskprocessTask13=上传成功: $url");
      //logger.i("上传成功: $filePath");
      // // 更新任务进度

      for (var item in uploadController.tasks) {
        log("processTaskprocessTask17=上传成功:${eventId}--${jsonEncode(item.id)}---- ${jsonEncode(item.shotRecordModel)}");
      }
      final task = uploadController.tasks
          .firstWhereOrNull((t) => t.shotRecordModel.eventId == eventId);

      if (task != null) {
        task.progress.value = 1.0;
        task.isCompleted.value = true;
        task.status.value = '上传成功';
        uploadController.updataData(task);
        var ss = task.shotRecordModel;
        ss.newworkFilePath = url;
        ss.videoLoadOK = "1";
        postHalfShootingResource(task.shotRecordModel.eventId ?? "",
            task.shotRecordModel.trainingId ?? "", url, 2);
        if (selfieShotDao != null) {
          log("processTaskprocessTask14=上传成功: $url-${jsonEncode(ss)}");
          selfieShotDao?.insertShot(
              ss, UserManager.instance.user?.userId ?? "");
        } else {
          log("processTaskprocessTask15=上传成功: $url-${jsonEncode(ss)}");
          database = await $FloorAppDatabase
              .databaseBuilder('app_database.db')
              .build();
          selfieShotDao = database?.selfieShotDao;
          selfieShotDao?.insertShot(
              ss, UserManager.instance.user?.userId ?? "");
        }
      }
    } catch (e) {
      log("processTaskprocessTask16=上传失败: $e");
      final task = uploadController.tasks
          .firstWhereOrNull((t) => t.shotRecordModel.eventId == eventId);
      if (task != null) {
        task.progress.value = 0.0;
        task.isCompleted.value = false;
        task.isFailed.value = true;
        task.status.value = '上传失败$e';
        uploadController.updataData(task);
      }
      //logger.e("上传失败: $filePath, 错误: $e");
      rethrow; // 重新抛出以便重试机制捕获
    }
  }

  postHalfShootingResource(
      String eventId, String trainingId, String uploadPath, int type) async {
    Map<String, dynamic> param = {
      "eventId": eventId,
      "trainingId": trainingId,
      "path": uploadPath,
      "resType": type, //1图片  2视频
    };

    var url = await ApiUrl.halfShootingResource(trainingId);
    var res = await Api().post(url, data: [param]);

    if (res.isSuccessful()) {
      log("资源上传成功: $eventId $uploadPath");
      BusUtils.instance.fire(EventAction(key: EventBusKey.uploadVideo));
    } else {
      // WxLoading.showToast(res.message);
    }
  }

  // 更新任务进度
  void _updateTaskProgress(String taskId, double progress) {
    // 更新服务状态
    if (fileUploadStatus.containsKey(taskId)) {
      final status = fileUploadStatus[taskId]!;
      status.progress = progress;
      fileUploadStatus[taskId] = status;
    }

    _updateTaskStatus(taskId, UploadStatus.uploading, progress);
  }

  // 更新任务状态
  void _updateTaskStatus(
    String taskId,
    UploadStatus status,
    double progress2, {
    String error = '',
  }) {
    var progress = progress2;
    // 更新服务状态
    if (fileUploadStatus.containsKey(taskId)) {
      final taskStatus = fileUploadStatus[taskId]!;
      taskStatus.status = status;
      taskStatus.progress = progress;
      taskStatus.lastError = error;
      fileUploadStatus[taskId] = taskStatus;
    }

    // 更新控制器状态
    String statusText;
    bool isCompleted = false;
    bool isFailed = false;

    switch (status) {
      case UploadStatus.uploading:
        statusText = '上传中 ${(progress * 100).toStringAsFixed(0)}%';
        break;
      case UploadStatus.retrying:
        statusText = '重试中 ${(progress * 100).toStringAsFixed(0)}%';
        break;
      case UploadStatus.success:
        statusText = '上传成功';
        isCompleted = true;
        progress = 1.0;
        break;
      case UploadStatus.failed:
        statusText = '上传失败: $error';
        isFailed = true;
        break;
      default:
        statusText = '等待上传';
    }
    // 更新控制器状态
    final task = uploadController.tasks.firstWhereOrNull((t) => t.id == taskId);
    if (task != null) {
      task.progress.value = progress;
      task.status.value = statusText;
      task.isCompleted.value = isCompleted;
      task.isFailed.value = isFailed;
      uploadController.updataData(task);
    }
    // uploadController.updateTaskStatus(
    //     taskId, statusText, isCompleted, isFailed);
  }

  // 标记任务完成
  void _markTaskCompleted(String taskId) {
    // 更新状态
    _updateTaskStatus(taskId, UploadStatus.success, 1.0);

    // 添加到已完成任务
    //completedTaskIds.add(taskId);
    _saveCompletedTasks();

    //logger.i("任务完成: $taskId");
  }

  // 处理上传失败
  void _handleUploadFailure(String taskId, String error) {
    // 更新状态
    _updateTaskStatus(taskId, UploadStatus.failed, 1.0, error: error);

    //logger.e("任务失败: $taskId, 错误: $error");
  }

  // ========== 辅助方法 ==========

  Duration _calculateRetryDelay(int attempt, math.Random random) {
    // 指数退避算法
    final baseDelay = initialRetryDelay * math.pow(2, attempt - 1);

    // 添加随机抖动（避免所有客户端同时重试）
    final jitter = random.nextInt(500);

    // 限制最大延迟时间
    final delayMs = math.min(baseDelay + jitter, maxRetryDelay.inMilliseconds);

    return Duration(milliseconds: delayMs.toInt());
  }

  // String _generateTaskId(ShotRecordModel record) {
  //   // 使用文件路径和时间戳生成唯一ID
  //   return '${record.filePath.hashCode}_${DateTime.now().millisecondsSinceEpoch}';
  // }

  String _generateRemoteKey(String filePath, String prefix) {
    final fileExtension = filePath.substring(filePath.lastIndexOf("."));
    return "$prefix${DateTime.now().millisecondsSinceEpoch}$fileExtension";
  }

  // ========== COS 服务初始化 ==========

  Future<void> _initializeCosService(TencentCosModel tencentCosModel) async {
    await Cos().forceInvalidationCredential();
    await Cos().initWithSessionCredential(FetchCredentials());

    final serviceConfig = pigeon.CosXmlServiceConfig(
      region: tencentCosModel.region ?? "ap-guangzhou",
      isDebuggable: true,
      isHttps: true,
    );

    final transferConfig = pigeon.TransferConfig(
      forceSimpleUpload: false,
      enableVerification: true,
      divisionForUpload: 2097152,
      sliceSizeForUpload: 1048576,
    );

    await Cos().registerDefaultService(serviceConfig);
    await Cos().registerDefaultTransferManger(serviceConfig, transferConfig);
  }

  // ========== 网络请求方法 ==========

  Future<TencentCosModel> fetchSessionCredentials2() async {
    final httpClient = HttpClient();
    try {
      final request = await httpClient
          .getUrl(Uri.parse("https://i.shootz.tech/mgr-api/common/sts-data"));
      final response = await request.close();

      if (response.statusCode == HttpStatus.ok) {
        final json = await response.transform(utf8.decoder).join();
        return TencentCosModel.fromJson(jsonDecode(json));
      } else {
        throw Exception("获取临时密钥失败: ${response.statusCode}");
      }
    } catch (e) {
      //logger.e("fetchSessionCredentials2 error: $e");
      throw Exception("获取临时密钥失败: $e");
    } finally {
      httpClient.close();
    }
  }
}

// ========== 模型和枚举 ==========

enum UploadStatus {
  pending, // 等待上传
  uploading, // 上传中
  retrying, // 重试中
  success, // 上传成功
  failed, // 上传失败
}

class FileUploadStatus {
  final String taskId;
  final String filePath;
  UploadStatus status;
  double progress;
  int retryCount;
  String lastError;

  FileUploadStatus({
    required this.taskId,
    required this.filePath,
    this.status = UploadStatus.pending,
    this.progress = 0.0,
    this.retryCount = 0,
    this.lastError = '',
  });

  @override
  String toString() {
    return 'FileUploadStatus{taskId: $taskId, status: $status, progress: $progress, retryCount: $retryCount}';
  }
}
