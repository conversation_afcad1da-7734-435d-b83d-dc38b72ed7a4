import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:developer';
import 'dart:math' as math;
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';
import 'package:shoot_z/pages/tab/upload/CosUploadService.dart';
import 'package:shoot_z/pages/tab/upload/UploadController.dart';

class UploadTaskManager {
  static const String _prefKey = 'upload_tasks';
  static const String _statePrefKey = 'upload_manager_state';
  static final BlockingQueue<ShotRecordModel> _uploadQueue = BlockingQueue(10);

  static Timer? _processTimer;
  static bool _isRunning = false;
  static bool _isPaused = false;

  // 保存任务处理进度
  static ShotRecordModel? _currentProcessingTask;
  static int _currentAttempt = 0;

  // 事件流控制器
  static final StreamController<UploadEvent> _eventStreamController =
      StreamController.broadcast();

  // 状态流控制器
  static final StreamController<UploadManagerState> _stateStreamController =
      StreamController.broadcast();

  // 初始化任务管理器
  static Future<void> initialize() async {
    // 恢复管理器的状态
    await _restoreState();

    // 恢复持久化的任务
    await _restoreTasks();

    // 如果之前处于运行状态且未暂停，启动任务处理
    if (_isRunning && !_isPaused) {
      _startProcessing();
    }
  }

  // 暂停任务处理
  static void pauseProcessing() {
    if (!_isRunning || _isPaused) return;

    _isPaused = true;
    _isRunning = false;
    // 取消当前处理定时器
    _processTimer?.cancel();
    _processTimer = null;

    // 保存状态到持久化存储
    _saveState();

    // 发送状态更新事件
    _stateStreamController.add(UploadManagerState.paused);

    // 发送暂停事件
    _eventStreamController.add(UploadEvent(
      type: UploadEventType.processingPaused,
      taskId: _currentProcessingTask?.eventId,
    ));
  }

  // 恢复任务处理
  static void resumeProcessing() {
    if (!_isRunning || !_isPaused) return;

    _isPaused = false;

    // 发送状态更新事件
    _stateStreamController.add(UploadManagerState.resumed);

    // 发送恢复事件
    _eventStreamController.add(UploadEvent(
      type: UploadEventType.processingResumed,
      taskId: _currentProcessingTask?.eventId,
    ));

    // 重新启动任务处理
    _startProcessing();
  }

  // 添加任务
  static Future<void> addTask(ShotRecordModel record) async {
    log("startUpload4");
    // 添加到内存队列
    _uploadQueue.enqueue(record);
    log("startUpload5");
    // 保存到持久化存储
    await _saveTask(record);

    // 确保处理正在运行（未暂停）
    if (!_isRunning && !_isPaused) {
      log("startUpload6");
      _startProcessing();
    }
    log("startUpload7");
    // 发送任务添加事件
    _eventStreamController.add(UploadEvent(
      type: UploadEventType.taskAdded,
      taskId: record.eventId,
      record: record,
    ));
    log("startUpload8");
    _startProcessing();
  }

  // 开始任务处理
  static void _startProcessing() {
    log("startUpload9");
    if (_isRunning) return;
    log("startUpload10");
    _isRunning = true;
    _isPaused = false;

    // 发送状态更新事件
    _stateStreamController.add(UploadManagerState.started);

    // 立即启动一个任务（如果有）
    if (_uploadQueue._queue.isNotEmpty && !_isPaused) {
      _processTask();
    }

    // 每5秒尝试处理下一个任务
    _processTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      if (!_isPaused && _uploadQueue._queue.isNotEmpty) {
        _processTask();
      }
    });
  }

  // 处理单个任务
  static Future<void> _processTask() async {
    if (_isPaused) return;
    log("processTaskprocessTask1=${_isPaused}");
    try {
      // 获取任务
      var _currentProcessingTask2 = await _uploadQueue.dequeue();
      _currentProcessingTask = _currentProcessingTask2;
      log("processTaskprocessTask1=${jsonEncode(_currentProcessingTask2)}");
      // 发送任务开始处理事件
      _eventStreamController.add(UploadEvent(
        type: UploadEventType.processingStarted,
        taskId: _currentProcessingTask2.eventId,
      ));

      // 确保控制器已初始化
      if (!Get.isRegistered<UploadController>()) {
        Get.put(UploadController());
      }

      // 获取上传服务实例
      final uploadService = Get.find<CosUploadService>();

      // 添加上传任务并等待完成
      uploadService.addUploadTask(
          _currentProcessingTask2, _currentProcessingTask2.eventId);
      // 重新加入队列等待重试
      // 从持久化存储中移除
      await _removeTask(_currentProcessingTask2.eventId ?? "");
      // 发送任务完成事件
      _eventStreamController.add(UploadEvent(
        type: UploadEventType.processingCompleted,
        taskId: _currentProcessingTask2.eventId,
      ));

      log("processTaskprocessTask2=${jsonEncode(_currentProcessingTask2)}");
      log("processTaskprocessTask3=${_isPaused}");
      // 重置当前处理任务
      _currentProcessingTask = null;
      _currentAttempt = 0;
    } catch (e, stack) {
      log('任务处理失败: $e\n$stack');

      // 更新重试计数
      _currentAttempt++;

      // 发送任务失败事件
      _eventStreamController.add(UploadEvent(
        type: UploadEventType.processingFailed,
        taskId: _currentProcessingTask?.eventId,
        error: e.toString(),
      ));

      // 重新加入队列等待重试
      if (_currentProcessingTask != null) {
        _uploadQueue.enqueue(_currentProcessingTask!);
      }

      // 指数退避重试
      final delay = Duration(seconds: math.pow(2, _currentAttempt).toInt());
      Timer(delay, () {
        if (!_isPaused && _currentProcessingTask != null) {
          _processTask();
        }
      });
    }
  }

  // 保存管理器状态
  static Future<void> _saveState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        _statePrefKey,
        jsonEncode({
          'isRunning': _isRunning,
          'isPaused': _isPaused,
          'currentTaskId': _currentProcessingTask?.eventId,
          'currentAttempt': _currentAttempt,
        }));
  }

  // 恢复管理器状态
  static Future<void> _restoreState() async {
    final prefs = await SharedPreferences.getInstance();
    final stateJson = prefs.getString(_statePrefKey);
    if (stateJson == null) return;

    try {
      final state = jsonDecode(stateJson) as Map<String, dynamic>;
      _isRunning = state['isRunning'] as bool? ?? false;
      _isPaused = state['isPaused'] as bool? ?? false;
      _currentAttempt = state['currentAttempt'] as int? ?? 0;

      // 恢复当前处理任务（如果有）
      final taskId = state['currentTaskId'] as String?;
      if (taskId != null) {
        _currentProcessingTask = _uploadQueue._queue.firstWhere(
          (task) => task.eventId == taskId,
          // orElse: () => _tryRecoverTaskFromPrefs(taskId),
        );
      }
    } catch (e) {
      log('恢复状态失败: $e');
    }
  }

  // 尝试从持久化存储恢复任务
  // static ShotRecordModel _tryRecoverTaskFromPrefs(String taskId) {
  //   try {
  //     // 在实际应用中，需要从持久化存储中查找任务
  //     // 这里简化为创建一个新任务
  //     return ShotRecordModel(
  //       id: taskId,
  //       timestamp: DateTime.now(),
  //       // 其他属性...
  //     );
  //   } catch (e) {
  //     // 如果无法恢复，创建一个空任务
  //     return ShotRecordModel(eventId: taskId, timestamp: DateTime.now());
  //   }
  // }

  // 保存任务到持久化存储
  static Future<void> _saveTask(ShotRecordModel record) async {
    final prefs = await SharedPreferences.getInstance();
    final tasks = prefs.getStringList(_prefKey) ?? [];
    tasks.add(jsonEncode(record.toJson()));
    await prefs.setStringList(_prefKey, tasks);
  }

  // 从持久化存储恢复任务
  static Future<void> _restoreTasks() async {
    final prefs = await SharedPreferences.getInstance();
    final taskJsons = prefs.getStringList(_prefKey) ?? [];

    for (final jsonStr in taskJsons) {
      try {
        final record = ShotRecordModel.fromJson(jsonDecode(jsonStr));
        _uploadQueue.enqueue(record);
      } catch (e) {
        log('恢复任务失败: $e');
      }
    }
  }

  // 从持久化存储中移除任务
  static Future<void> _removeTask(String taskId) async {
    final prefs = await SharedPreferences.getInstance();
    final taskJsons = prefs.getStringList(_prefKey) ?? [];

    final filtered = taskJsons.where((jsonStr) {
      try {
        final record = ShotRecordModel.fromJson(jsonDecode(jsonStr));
        return record.eventId != taskId;
      } catch (e) {
        return false;
      }
    }).toList();

    await prefs.setStringList(_prefKey, filtered);

    // 如果移除的是当前处理的任务
    if (_currentProcessingTask?.eventId == taskId) {
      _currentProcessingTask = null;
      _currentAttempt = 0;
    }
  }

  // 获取任务事件流
  static Stream<UploadEvent> get eventStream => _eventStreamController.stream;

  // 获取管理器状态流
  static Stream<UploadManagerState> get stateStream =>
      _stateStreamController.stream;

  // 获取当前状态
  static UploadManagerState get currentState {
    if (!_isRunning) return UploadManagerState.stopped;
    if (_isPaused) return UploadManagerState.paused;
    return UploadManagerState.running;
  }

  // 获取当前处理任务
  static ShotRecordModel? get currentProcessingTask => _currentProcessingTask;

  // 获取当前重试次数
  static int get currentAttempt => _currentAttempt;
}

// 任务管理器状态
enum UploadManagerState {
  stopped, // 已停止
  started, // 已启动
  paused, // 已暂停
  resumed, // 已恢复
  running, //'处理中'
}

// 上传事件模型
class UploadEvent {
  final UploadEventType type;
  final String? taskId;
  final ShotRecordModel? record;
  final String? error;

  UploadEvent({
    required this.type,
    this.taskId,
    this.record,
    this.error,
  });
}

// 上传事件类型
enum UploadEventType {
  taskAdded, // 任务已添加
  processingStarted, // 处理已开始
  processingCompleted, // 处理已完成
  processingFailed, // 处理已失败
  processingPaused, // 处理已暂停
  processingResumed, // 处理已恢复
}

// 使用您的BlockingQueue实现
class BlockingQueue<E> {
  final int _waitTime;
  final Queue<E> _queue = Queue<E>();

  BlockingQueue(this._waitTime);

  void enqueue(E item) {
    _queue.add(item);
  }

  Future<E> dequeue() async {
    while (_queue.isEmpty) {
      await Future<void>.delayed(Duration(seconds: _waitTime));
    }
    return _queue.removeFirst();
  }

  void clearAll() {
    _queue.clear();
  }
}
