import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/create_match_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/report_package_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'dart:developer' as cc;

import 'package:shoot_z/utils/pay/pay_utils.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/generated/l10n.dart';
import 'package:ui_packages/ui_packages.dart';

class ReportRightsBuyLogic extends GetxController {
  var topTagList = [
    {
      'iconPath': 'assets/images/rights_report.png',
      'iconDesc': '创建比赛后可\n生成赛事报告'
    },
    {
      'iconPath': 'assets/images/rights_upload.png',
      'iconDesc': '比赛视频可自\n动保存至云端'
    },
    {'iconPath': 'assets/images/rights_time.png', 'iconDesc': '自购买起一年\n时间内有效'}
  ];
  var packageList = <ReportPackageModel>[].obs;
  var currentSelectIndex = 0.obs;
  StreamSubscription? paySubscription;
  @override
  void onInit() {
    super.onInit();
    _getReportPackage();
    paySubscription = BusUtils.instance.on((event) async {
      if (event.key == EventBusKey.payResult) {
        cc.log("!!!!!!!13232");
        if (event.action == true) {
          // paySuccess();
          final createMatchLogic = Get.find<CreateMatchLogic>();
          createMatchLogic.getReportNumber();
          AppPage.back();
        }
      }
    });
  }

  _getReportPackage() async {
    var res = await Api().get(ApiUrl.getPtzPackage);
    if (res.isSuccessful()) {
      packageList.value = (res.data as List)
          .map((e) => ReportPackageModel.fromJson(e))
          .toList();
    }
  }

  void paySuccess() {
    Get.dialog(
      Padding(
        padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
        child: Material(
          type: MaterialType.transparency,
          color: Colors.transparent,
          child: Center(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(
                    color: Colors.transparent,
                    child: Column(
                      children: <Widget>[
                        //upload_top_img
                        SizedBox(
                          height: 60.w,
                        ),
                        SizedBox(
                          height: 100.w,
                          width: double.infinity,
                          child: Stack(
                            alignment: Alignment.bottomCenter,
                            children: [
                              Container(
                                height: 65.w,
                                width: double.infinity,
                                margin: EdgeInsets.only(top: 35.w),
                                decoration: BoxDecoration(
                                  color: Colours.color191921,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(25.r),
                                    topRight: Radius.circular(25.r),
                                  ),
                                ),
                              ),
                              Transform.translate(
                                offset: Offset(0, -30.w),
                                child: MyImage(
                                  "daka3.png",
                                  width: 78.w,
                                  height: 78.w,
                                  isAssetImage: true,
                                  fit: BoxFit.fitWidth,
                                  bgColor: Colors.transparent,
                                ),
                              ),
                            ],
                          ),
                        ),

                        Transform.translate(
                          offset: const Offset(0, -10),
                          child: Container(
                            alignment: Alignment.topLeft,
                            decoration: BoxDecoration(
                              color: Colours.color191921,
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(25.r),
                                bottomRight: Radius.circular(25.r),
                              ),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            width: double.infinity,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: <Widget>[
                                Text(
                                  packageList[currentSelectIndex.value].name ??
                                      "",
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 18, color: Colours.colorA44EFF),
                                ),
                                SizedBox(
                                  height: 25.w,
                                ),
                                Text(
                                    '您已成功${packageList[currentSelectIndex.value].name}赛事报告',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: Colours.color9393A5,
                                      fontWeight: AppFontWeight.regular(),
                                      height: 1,
                                    )),
                                SizedBox(
                                  height: 35.w,
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () => AppPage.back(),
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                      right: 25.w,
                                      left: 25.w,
                                    ),
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 3.w,
                                        bottom: 3.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color282735,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                      gradient: const LinearGradient(
                                        colors: [
                                          Colours.color7732ED,
                                          Colours.colorA555EF
                                        ],
                                        begin: Alignment.bottomLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Text(
                                      UiS.current.ok,
                                      style: TextStyles.regular
                                          .copyWith(fontSize: 15.sp),
                                    ),
                                  ),
                                ),
                                const SizedBox(
                                  height: 30,
                                ),
                              ],
                            ),
                          ),
                        ),

                        SizedBox(
                          height: 25.w,
                        ),
                        GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            final createMatchLogic =
                                Get.find<CreateMatchLogic>();
                            createMatchLogic.getReportNumber();
                            AppPage.back();
                          },
                          child: WxAssets.images.icCloseDialog
                              .image(width: 30.w, height: 30.w),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      barrierColor: Colors.black.withOpacity(0.85),
    );
  }

  buyNow() async {
    WxLoading.show();
    final clientType = GetPlatform.isIOS ? "2" : "1";
    final orderRes = await Api().post(ApiUrl.buyPtzMatchNum, data: {
      'orderChan': 2,
      'clientType': clientType,
      'userId': UserManager.instance.user?.userId,
      'packageId': packageList[currentSelectIndex.value].id ?? 0
    });
    if (!orderRes.isSuccessful()) {
      WxLoading.dismiss();
      return;
    }
    final orderId = orderRes.data['orderId'];
    if (GetPlatform.isIOS) {
      WxLoading.dismiss();
      final appProductId = orderRes.data['appProductId'];
      PayUtils.instance.applePay(appProductId, orderId);
    } else {
      final res = await Api()
          .post(ApiUrl.pay, data: {'orderId': orderId, 'channel': 1});
      WxLoading.dismiss();
      if (res.isSuccessful()) {
        final channelPayParams = res.data['channelPayParams'];
        if (channelPayParams != null) {
          PayUtils.instance.wxPay(channelPayParams);
        } else {
          WxLoading.showToast('获取支付参数出错');
        }
      }
    }
  }

  @override
  void onReady() {
    super.onReady();
  }
}
