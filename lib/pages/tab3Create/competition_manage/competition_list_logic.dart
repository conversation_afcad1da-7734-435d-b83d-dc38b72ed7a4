import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/competition_model.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/match_home_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/match_model.dart';
import 'dart:developer' as cc;

class CompetitionListLogic extends GetxController {
  var items = <MatchModel>[].obs; // 数据源
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  @override
  void onInit() {
    super.onInit();
    getdataList();
  }

  @override
  void onReady() {
    super.onReady();
  }

//获得最新列表
  getdataList({isLoad = false}) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'pageIndex': dataFag["page"] ?? 1,
      'pageSize': 10,
    };

    var res = await Api().get(ApiUrl.getPtzSeries, queryParameters: param);
    if (res.isSuccessful()) {
      final modelList = ((res.data['result'] ?? []) as List)
          .map((e) => MatchModel.fromJson(e))
          .toList();
      if (isLoad) {
        items.addAll(modelList);
        items.refresh();
        if (items.length == res.data['totalCount']) {
          refreshController.loadNoData();
          //  controller.loadComplete();
        } else {
          refreshController.loadComplete();
        }
      } else {
        refreshController.resetNoData();
        items.assignAll(modelList);
        refreshController.refreshCompleted();
      }
    } else {
      refreshController.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  //删除比赛
  deleteMatch(String? competitionId, CompetitionModel model,
      MatchHomeLogic controller) async {
    cc.log("request$model");
    var url = await ApiUrl.editMatch(competitionId ?? '', model.id ?? '');
    var res = await Api().delete(url);
    cc.log("result${res.data}");
    if (res.isSuccessful()) {
      controller.getdataList(isLoad: false);
      WxLoading.showToast('删除成功');
      getdataList();
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
