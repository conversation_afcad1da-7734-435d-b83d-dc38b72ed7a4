// ignore_for_file: unused_element

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/inappwebview/router.dart';
import 'package:shoot_z/pages/game/details/analyze_item_view.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/detail/report/logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:ui_packages/ui_packages.dart';

class PTZStatisticsView extends StatelessWidget {
  PTZStatisticsView({super.key});
  final logic = Get.find<MatchTeamReportLogic>();
  final state = Get.find<MatchTeamReportLogic>().state;
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 20.w, bottom: 15.w, left: 15.w),
            alignment: Alignment.centerLeft,
            child: const TextWithIcon(title: '球队表现'),
          ),
          _statistics(context),
          _mvp(context),
          _best(context),
          // _sectionScore(context),
          _analyze(context),
          _toFeedback(context)
        ],
      ),
    );
  }

  Widget _toFeedback(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(15.w),
      padding: EdgeInsets.all(15.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.r), color: Colours.color191921),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                "${S.current.dialog_title}：",
                style: TextStyles.regular.copyWith(fontSize: 12.sp),
              ),
              const Spacer(),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  var url = "";
                  if (const String.fromEnvironment('env',
                          defaultValue: 'dev') !=
                      'pro') {
                    url =
                        "https://idev.shootz.tech/kf/?userId=${UserManager.instance.userInfo.value?.userId ?? ""}";
                  } else {
                    url =
                        "https://i.shootz.tech/kf/?userId=${UserManager.instance.userInfo.value?.userId ?? ""}";
                  }

                  //测试 https://idev.shootz.tech/kf 正式 https://i.shootz.tech/kf
                  WebviewRouter router = WebviewRouter(
                      url: url,
                      showNavigationBar: true,
                      needBaseHttp: false,
                      title: S.current.feedback);
                  AppPage.to(Routes.webviewh5, arguments: router);
                },
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(width: 1, color: Colours.white),
                    borderRadius: BorderRadius.circular(22.r),
                  ),
                  padding:
                      EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.w),
                  child: Text(
                    "去反馈",
                    style: TextStyles.regular
                        .copyWith(fontSize: 10.sp, color: Colours.white),
                  ),
                ),
              )
            ],
          ),
          SizedBox(
            height: 15.w,
          ),
          Text(
            "如遇数据有误，且与实际数据出入较大，您可以点击右侧按钮反馈留言，请附上具体数据错误项及错误原因。",
            style: TextStyles.regular.copyWith(
                fontSize: 12.sp, color: Colours.colorBFBFBF, height: 1.3),
          )
        ],
      ),
    );
  }

  Widget _statistics(BuildContext context) {
    return Container(
      // height: 55.w,
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.circular(12.r)),
      child: GridView.builder(
          scrollDirection: Axis.vertical,
          // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
          shrinkWrap: true,
          physics:
              const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 0,
            mainAxisSpacing: 0,
            childAspectRatio: 115 / 73,
          ),
          padding: EdgeInsets.only(bottom: 0.w),
          itemCount: logic.playerDatalist.length,
          itemBuilder: (context, index) {
            return Obx(() {
              return GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {},
                child: Container(
                  padding: EdgeInsets.only(top: 15.w, bottom: 15.w),
                  decoration: BoxDecoration(
                    border: Border(
                      right: index % 3 != 2 // 不是最后一列
                          ? BorderSide(width: 1.w, color: Colours.color231F2E)
                          : BorderSide.none,
                      bottom: index < logic.playerDatalist.length - 3 // 不是最后一行
                          ? BorderSide(width: 1.w, color: Colours.color231F2E)
                          : BorderSide.none,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(logic.playerDatalist[index]["data"] ?? "0",
                          textAlign: TextAlign.right,
                          style: TextStyles.numberDin16),
                      const Spacer(),
                      Text(
                        logic.playerDatalist[index]["name"] ?? "",
                        textAlign: TextAlign.right,
                        style: TextStyles.regular.copyWith(
                            fontSize: 14.sp, color: Colours.color5C5C6E),
                      ),
                    ],
                  ),
                ),
              );
            });
          }),
    );
  }

  Widget _mvp(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
            margin: EdgeInsets.only(top: 20.w, bottom: 15.w),
            child: const TextWithIcon(title: '本场MVP')),
        Container(
          height: 140.w,
          padding: EdgeInsets.symmetric(vertical: 15.w, horizontal: 17.w),
          decoration: BoxDecoration(
            image: DecorationImage(
                image: WxAssets.images.mVPBg.provider(), fit: BoxFit.fill),
          ),
          child: Obx(
            () {
              final model = state.model.value.teamBest.mvp;
              return GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => logic.unlockPlayer(model.playerId, model.locking),
                child: Row(
                  children: [
                    ClipRRect(
                        borderRadius: BorderRadius.circular(4.w),
                        child: CachedNetworkImage(
                          imageUrl: model.photo,
                          width: 80.w,
                          height: 110.w,
                          fit: BoxFit.cover,
                        )),
                    SizedBox(
                      width: 20.w,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 15.w,
                        ),
                        // Text(model.number,
                        //     style: TextStyles.semiBold14
                        //         .copyWith(fontSize: 26.sp)),
                        RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: model.number,
                                style: TextStyles.semiBold14
                                    .copyWith(fontSize: 26.sp),
                              ),
                              WidgetSpan(
                                child: Transform.translate(
                                  offset: const Offset(0, 1), // 向下偏移2像素
                                  child: Text(
                                    '号',
                                    style: TextStyles.semiBold14
                                        .copyWith(fontSize: 18.sp),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Spacer(),
                        Row(
                          children: [
                            Column(
                              children: [
                                Text(
                                  model.locking ? '**' : model.score,
                                  style: TextStyles.semiBold14
                                      .copyWith(fontSize: 18.sp),
                                ),
                                SizedBox(
                                  height: 8.w,
                                ),
                                Text(
                                  S.current.score,
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      color: Colours.color5C5C6E),
                                ),
                              ],
                            ),
                            SizedBox(
                              width: 15.w,
                            ),
                            Column(
                              children: [
                                Text(
                                  model.locking
                                      ? '**'
                                      : model.rebound.toString(),
                                  style: TextStyles.semiBold14
                                      .copyWith(fontSize: 18.sp),
                                ),
                                SizedBox(
                                  height: 8.w,
                                ),
                                Text(
                                  S.current.rebound,
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      color: Colours.color5C5C6E),
                                ),
                              ],
                            ),
                            SizedBox(
                              width: 15.w,
                            ),
                            Column(
                              children: [
                                Text(
                                  model.locking
                                      ? '**'
                                      : model.assist.toString(),
                                  style: TextStyles.semiBold14
                                      .copyWith(fontSize: 18.sp),
                                ),
                                SizedBox(
                                  height: 8.w,
                                ),
                                Text(
                                  S.current.assist,
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      color: Colours.color5C5C6E),
                                ),
                              ],
                            ),
                            SizedBox(
                              width: 15.w,
                            ),
                            Column(
                              children: [
                                Text(
                                  model.locking ? '**' : '${model.rate}%',
                                  style: TextStyles.semiBold14
                                      .copyWith(fontSize: 18.sp),
                                ),
                                SizedBox(
                                  height: 8.w,
                                ),
                                Text(
                                  S.current.shotRate,
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      color: Colours.color5C5C6E),
                                ),
                              ],
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 8.w,
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    ).marginSymmetric(horizontal: 15.w);
  }

  Widget _best(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(children: [
          const TextWithIcon(title: '最佳球员'),
          const Spacer(),
          GestureDetector(
            onTap: () => logic.tabController?.animateTo(1),
            child: Row(
              children: [
                Text(
                  S.current.more_player,
                  style: TextStyles.regular
                      .copyWith(fontSize: 12.sp, color: Colours.color9393A5),
                ),
                WxAssets.images.icArrowRight
                    .image(width: 14.w, fit: BoxFit.fill),
              ],
            ),
          ),
        ]).marginOnly(top: 20.w, bottom: 15.w, left: 15.w, right: 15.w),
        SizedBox(
          height: 117.w,
          child: ListView.builder(
              padding: EdgeInsets.only(left: 15.w, right: 5.w),
              scrollDirection: Axis.horizontal,
              itemCount: 5,
              itemBuilder: (context, index) {
                return Obx(() => _bestListItem(context, index));
              }),
        ),
      ],
    );
  }

  Widget _bestListItem(BuildContext context, int index) {
    var model = state.model.value.teamBest.scoreKing;
    var title = '得分王';
    var number = '${model.score}分';
    switch (index) {
      case 2:
        model = state.model.value.teamBest.assistKing;
        title = '助攻王';
        number = '${model.assist}个';
      case 1:
        model = state.model.value.teamBest.reboundKing;
        title = '篮板王';
        number = '${model.rebound}个';
      case 4:
        model = state.model.value.teamBest.freeThrowKing;
        title = '罚球王';
        number = '${model.hit}个';
      case 3:
        model = state.model.value.teamBest.threePointKing;
        title = '三分王';
        number = '${model.hit}个';
    }
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => logic.unlockPlayer(model.playerId, model.locking),
      child: Container(
        margin: EdgeInsets.only(right: 10.w),
        width: 60.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.w),
        ),
        child: Column(
          children: [
            SizedBox(
              height: 96.w,
              child: Stack(
                children: [
                  ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: Stack(children: [
                        CachedNetworkImage(
                          imageUrl: model.photo,
                          width: 60.w,
                          height: 87.w,
                          fit: BoxFit.cover,
                        ),
                        Visibility(
                            visible: model.locking,
                            child: Positioned.fill(
                                child: Container(
                              color: Colors.black.withOpacity(0.5),
                              alignment: Alignment.center,
                              child: WxAssets.images.icGameLock
                                  .image(width: 18.w, fit: BoxFit.fill),
                            ))),
                      ])),
                  Positioned(
                      left: 0,
                      top: 0,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 2.w, horizontal: 4.w),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.only(
                            bottomRight: Radius.circular(4.w),
                          ),
                        ),
                        child: Text(
                          model.number,
                          style: TextStyles.medium
                              .copyWith(color: Colours.white, fontSize: 12.sp),
                        ),
                      )),
                  Positioned(
                      left: 11.w,
                      bottom: 0,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 5.w, horizontal: 4.w),
                        decoration: BoxDecoration(
                          color: Colours.color2E1575,
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(4.w),
                            bottomLeft: Radius.circular(4.w),
                          ),
                        ),
                        child: Text(
                          title,
                          style: TextStyles.medium
                              .copyWith(color: Colours.white, fontSize: 10.sp),
                        ),
                      )),
                ],
              ),
            ),
            SizedBox(
              height: 3.w,
            ),
            Text(
              number,
              style: TextStyles.display12.copyWith(color: Colours.white),
            )
            // Expanded(
            //   child: Column(
            //     crossAxisAlignment: CrossAxisAlignment.start,
            //     children: [
            //       SizedBox(
            //         height: 3.w,
            //       ),
            //       Text(
            //         '$title王',
            //         style: GoogleFonts.oswald(
            //             fontSize: 16.sp,
            //             fontWeight: AppFontWeight.bold(),
            //             color: Colours.white,
            //             height: 1),
            //       ),
            //       SizedBox(
            //         height: 21.w,
            //       ),
            //       Row(
            //         children: [
            //           Container(
            //             width: 16.w,
            //             height: 18.w,
            //             alignment: Alignment.center,
            //             decoration: BoxDecoration(
            //                 image: DecorationImage(
            //                     image: WxAssets.images.icTeamQy.provider(),
            //                     fit: BoxFit.fill)),
            //             child: Text(
            //               model.number,
            //               style: GoogleFonts.oswald(
            //                   fontSize: 10.sp,
            //                   fontWeight: AppFontWeight.semiBold(),
            //                   color: Colours.white,
            //                   height: 1),
            //             ),
            //           ),
            //           SizedBox(
            //             width: 10.w,
            //           ),
            //           Text(
            //             title,
            //             style: TextStyles.regular.copyWith(
            //                 fontSize: 12.sp, color: Colours.color5C5C6E),
            //           ),
            //           SizedBox(
            //             width: 3.w,
            //           ),
            //           Text(
            //             model.locking ? '**' : number,
            //             style: TextStyles.semiBold.copyWith(
            //                 fontSize: 12.sp, color: Colours.color9393A5),
            //           ),
            //         ],
            //       ),
            //     ],
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  Widget _sectionScore(BuildContext context) {
    final hide =
        state.sectionScoreList == null || state.sectionScoreList!.length != 2;
    return Visibility(
      visible: !hide,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '小节比分',
            style: TextStyles.regular.copyWith(fontSize: 16.sp),
          ).marginOnly(top: 30.w, bottom: 20.w),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 16.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListView.builder(
                physics: const ClampingScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: 3,
                itemBuilder: (context, index) {
                  return Obx(() => _scoreListItem(context, index));
                }),
          ),
        ],
      ).marginSymmetric(horizontal: 15.w),
    );
  }

  Widget _scoreListItem(BuildContext context, int index) {
    final one = state.sectionScoreList!.first;
    final two = state.sectionScoreList!.last;
    final color = index == 0 ? Colours.color5C5C6E : Colours.white;
    final text2 =
        index == 0 ? "Q1" : (index == 1 ? one.secScore1 : two.secScore1);
    final text3 =
        index == 0 ? "Q2" : (index == 1 ? one.secScore2 : two.secScore2);
    final text4 =
        index == 0 ? "Q3" : (index == 1 ? one.secScore3 : two.secScore3);
    final text5 =
        index == 0 ? "Q4" : (index == 1 ? one.secScore4 : two.secScore4);
    final text6 =
        index == 0 ? "总分" : (index == 1 ? one.totalScore : two.totalScore);
    final hide = (state.currentIndex.value == 0 && index == 2) ||
        (state.currentIndex.value == 1 && index == 1);
    return Visibility(
      visible: !hide,
      child: Row(
        children: [
          Expanded(child: _scoreText(text2.toString(), color)),
          Expanded(child: _scoreText(text3.toString(), color)),
          Expanded(child: _scoreText(text4.toString(), color)),
          Expanded(child: _scoreText(text5.toString(), color)),
          Expanded(child: _scoreText(text6.toString(), color)),
        ],
      ).paddingOnly(bottom: index == 0 ? 20.w : 0),
    );
  }

  Widget _scoreText(String text, Color color) {
    return Text(
      text,
      style: TextStyles.regular.copyWith(fontSize: 12.sp, color: color),
      textAlign: TextAlign.center,
    );
  }

  Widget _analyze(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 20.w, bottom: 15.w, left: 15.w),
          child: const TextWithIcon(title: '技术分析'),
        ),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 15.w),
          padding:
              EdgeInsets.only(left: 20.w, right: 20.w, top: 20.w, bottom: 6.w),
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: const BoxDecoration(
                          color: Colours.color7732ED, shape: BoxShape.circle),
                    ),
                    SizedBox(
                      width: 5.w,
                    ),
                    Text(
                      state.modelList.first.teamName,
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp, color: Colours.color9393A5),
                    )
                  ],
                ),
                const Spacer(),
                Row(
                  children: [
                    Text(
                      state.modelList.last.teamName,
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp, color: Colours.color9393A5),
                    ),
                    SizedBox(
                      width: 5.w,
                    ),
                    Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: const BoxDecoration(
                          color: Colours.colorE282FF, shape: BoxShape.circle),
                    ),
                  ],
                ),
              ],
            ).marginOnly(bottom: 20.w),
            ListView.builder(
                physics: const ClampingScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: 10,
                itemBuilder: (context, index) {
                  final leftTeam = state.modelList.first.teamScoreDetail;
                  final rightTeam = state.modelList.last.teamScoreDetail;
                  var needCalculate = true;
                  var left = 0.0;
                  var right = 0.0;
                  var title = '';
                  switch (index) {
                    case 0:
                      title = '得分';
                      left = leftTeam.totalScore.toDouble();
                      right = rightTeam.totalScore.toDouble();
                    case 1:
                      title = '篮板';
                      left = leftTeam.reboundCount.toDouble();
                      right = rightTeam.reboundCount.toDouble();
                    case 2:
                      title = '助攻';
                      left = leftTeam.assistCount.toDouble();
                      right = rightTeam.assistCount.toDouble();
                    case 3:
                      title = '投篮命中率';
                      left = double.parse(leftTeam.shootRate) / 100;
                      right = double.parse(rightTeam.shootRate) / 100;
                      needCalculate = false;
                    case 4:
                      title = '三分';
                      left = leftTeam.threePointShootCount.toDouble();
                      right = rightTeam.threePointShootCount.toDouble();
                    case 5:
                      title = '三分命中率';
                      left = double.parse(
                              leftTeam.threePointShootRate.isNotEmpty
                                  ? leftTeam.threePointShootRate
                                  : '0') /
                          100;
                      left = double.parse(
                              rightTeam.threePointShootRate.isNotEmpty
                                  ? leftTeam.threePointShootRate
                                  : '0') /
                          100;
                      needCalculate = false;
                    case 6:
                      title = '前场篮板';
                      left = leftTeam.offensiveReboundCount.toDouble();
                      right = rightTeam.offensiveReboundCount.toDouble();
                    case 7:
                      title = '后场篮板';
                      left = leftTeam.defensiveReboundCount.toDouble();
                      right = rightTeam.defensiveReboundCount.toDouble();
                    case 8:
                      title = '罚球';
                      left = leftTeam.freeThrowShootCount.toDouble();
                      right = rightTeam.freeThrowShootCount.toDouble();
                    case 9:
                      title = '罚球命中率';
                      left = double.parse(leftTeam.freeThrowShootRate.isNotEmpty
                              ? leftTeam.freeThrowShootRate
                              : '0') /
                          100;
                      left = double.parse(
                              rightTeam.freeThrowShootRate.isNotEmpty
                                  ? leftTeam.freeThrowShootRate
                                  : '0') /
                          100;
                      needCalculate = false;
                  }
                  return AnalyzeItemView(
                    left: left,
                    right: right,
                    title: title,
                    needCalculate: needCalculate,
                  ).marginOnly(bottom: 18.w);
                }),
          ]),
        ),
      ],
    );
  }
}
