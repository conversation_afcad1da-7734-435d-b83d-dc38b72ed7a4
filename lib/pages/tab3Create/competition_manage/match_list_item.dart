import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/competition_model.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/match_home_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/competition_list_logic.dart';
import 'package:shoot_z/pages/tab3Create/creation_way_logic.dart';
import 'package:shoot_z/pages/tab3Create/ptz/model/connection_status.dart';
import 'package:shoot_z/pages/tab3Create/ptz/ptz_binding/ptz_binding_view.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class MatchListItem extends StatelessWidget {
  final CompetitionModel item;
  const MatchListItem({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    final status =
        item.status == 0 ? "未开始" : (item.status == 1 ? "进行中" : "已结束");

    final statusColor =
        item.status == 1 ? Colours.color922BFF : Colours.colorA8A8BC;
    final markIcon = item.markStatus == 0
        ? WxAssets.images.icDfx
        : (item.markStatus == 1
            ? WxAssets.images.icFxz
            : WxAssets.images.icYsc);
    final mark =
        item.markStatus == 0 ? "待分析" : (item.markStatus == 1 ? "分析中" : "已生成");
    final markColor =
        item.markStatus == 2 ? Colours.white : Colours.color6F6F84;
    return Container(
      padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
      decoration: BoxDecoration(
        color: Colours.color191921,
        borderRadius: BorderRadius.circular(8.w),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 105.w,
                child: Text(
                  "${item.matchDateStr} ${item.week} ${item.matchTimeStr}",
                  style: TextStyles.display12,
                ),
              ),
              SizedBox(
                width: 20.w,
              ),

              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    WxAssets.images.mapAddress.image(),
                    const SizedBox(
                      width: 4,
                    ),
                    Flexible(
                      child: Text(
                        textAlign: TextAlign.end,
                        item.venueName ?? '',
                        style: TextStyles.display12,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    )
                  ],
                ),
              )
              // const Expanded(child: SizedBox.shrink()),
            ],
          ),
          SizedBox(
            height: 15.w,
          ),
          GestureDetector(
              onTap: () {
                if (item.status == 2) {
                  AppPage.to(Routes.matchDetailsPage, arguments: item.id);
                }
              },
              child: IntrinsicHeight(
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          Column(
                            children: [
                              Row(
                                children: [
                                  MyImage(
                                    item.leftTeamLogo ?? "",
                                    width: 22.w,
                                    height: 22.w,
                                    radius: 11.r,
                                    placeholderImage: "my_team_head4.png",
                                    errorImage: "my_team_head4.png",
                                  ),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  SizedBox(
                                    width: 125,
                                    child: Text(
                                      item.leftTeamName ?? "",
                                      style: TextStyles.semiBold14,
                                      overflow:
                                          TextOverflow.ellipsis, // 超出部分显示省略号
                                      maxLines: 1, // 限制为单行
                                    ),
                                  ),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  Expanded(
                                      child: Text(
                                    '${item.leftTeamScore ?? ""}',
                                    style: TextStyles.numberDin16,
                                    textAlign: TextAlign.right,
                                  )),
                                  SizedBox(
                                    width: 23.w,
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 15.w,
                              ),
                              Row(
                                children: [
                                  MyImage(
                                    item.rightTeamLogo ??
                                        "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/banner/zxf.png",
                                    width: 22.w,
                                    height: 22.w,
                                    radius: 11.r,
                                    placeholderImage: "my_team_head4.png",
                                    errorImage: "my_team_head4.png",
                                  ),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  SizedBox(
                                      width: 125,
                                      child: Text(
                                        item.rightTeamName ?? "",
                                        style: TextStyles.semiBold14,
                                      )),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  Expanded(
                                      child: Text(
                                    '${item.rightTeamScore ?? ""}',
                                    style: TextStyles.numberDin16,
                                    textAlign: TextAlign.right,
                                  )),
                                  SizedBox(
                                    width: 23.w,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Container(
                      // width: 87.w,
                      // height: double.infinity,
                      margin: EdgeInsets.only(left: 30.w),
                      child: item.status == 0
                          ? Text(
                              status,
                              style: TextStyles.displayBold16,
                            )
                          : Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  textAlign: TextAlign.end,
                                  status,
                                  style: TextStyles.titleSemiBold16
                                      .copyWith(color: statusColor),
                                ),
                                SizedBox(
                                  height: 10.w,
                                ),
                                if (item.markStatus == 1)
                                  Container(
                                    width: 78.w,
                                    height: 22.w,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        image: DecorationImage(
                                            image: WxAssets
                                                .images.homeImgBottomLong
                                                .provider(),
                                            fit: BoxFit.fill)),
                                    child: ShaderMask(
                                        shaderCallback: (bounds) =>
                                            const LinearGradient(
                                              colors: [
                                                Colours.colorFFF9DC,
                                                Colours.colorE4C8FF,
                                                Colours.colorE5F3FF,
                                              ],
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight,
                                            ).createShader(bounds),
                                        child: Text(
                                          "AI分析中",
                                          style: TextStyles.regular.copyWith(
                                            fontSize: 12.sp,
                                            color: Colours.white,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        )),
                                  ),
                                if (item.markStatus != 1)
                                  Container(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 6.w),
                                    width: 56.w,
                                    height: 20.w,
                                    decoration: BoxDecoration(
                                      color: Colours.color292933,
                                      borderRadius: BorderRadius.circular(3.w),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        markIcon.image(
                                            width: 10.w, height: 10.w),
                                        Text(
                                          mark,
                                          style: TextStyles.display12.copyWith(
                                              fontSize: 10.w, color: markColor),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                    ),
                  ],
                ),
              )),
          const SizedBox(
            height: 15,
          ),
          if (item.status == 0)
            Container(
              height: 50,
              decoration: BoxDecoration(
                  border: Border(
                      top: BorderSide(
                          color: Colours.color10D8D8D8, width: 1.w))),
              child: Row(
                children: [
                  InkWell(
                    onTap: () {
                      CreationWayLogic creationWayLogic =
                          Get.find<CreationWayLogic>();
                      if (creationWayLogic.gimbalManager.connectionStatus ==
                          ConnectionStatus.connected) {
                        AppPage.to(Routes.nativeCameraScreen);
                      } else {
                        showPTZBindingSheet(context);
                      }
                    },
                    //     AppPage.to(Routes.halfVideoSelectionPage, arguments: {
                    //   "trainingId": '283',
                    //   "reportId": '35',
                    //   "type": '2', //1单人 2多人
                    // }),
                    child: Container(
                      width: 78.w,
                      height: 22.w,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Colours.color7732ED, Colours.colorA555EF],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                          borderRadius: BorderRadius.circular(14.r)),
                      child: Text(
                        '开始拍摄',
                        style: TextStyles.titleSemiBold12,
                      ),
                    ),
                  ),
                  const Expanded(child: SizedBox.shrink()),
                  InkWell(
                    onTap: () {
                      AppPage.to(Routes.createMatchPage,
                          arguments: {'edit': item}).then((onValue) {
                        if (onValue != null) {
                          MatchHomeLogic homeLogic = Get.find();
                          homeLogic.getdataList(isLoad: false);
                        }
                      });
                    },
                    child: Container(
                      width: 54.w,
                      height: 22.w,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          border: Border.all(color: Colors.white, width: 1),
                          borderRadius: BorderRadius.circular(14.r)),
                      child: Text(
                        '编辑',
                        style: TextStyles.titleSemiBold12,
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 15,
                  ),
                  InkWell(
                    onTap: () {
                      getMyDialog(
                        '是否删除',
                        '确定',
                        content: '是否确认删除当前比赛？',
                        () {
                          AppPage.back();
                          MatchHomeLogic matchHomeController =
                              Get.find(); // 获取已存在的 Controller
                          CompetitionListLogic listController =
                              Get.find(); // 获取已存在的 Controller
                          listController.deleteMatch(
                              matchHomeController.matchItem.value.id,
                              item,
                              matchHomeController);
                        },
                        isShowClose: false,
                        btnIsHorizontal: true,
                        btnText2: S.current.cancel,
                        onPressed2: () {
                          AppPage.back();
                        },
                      );
                      // AppPage.to(Routes.videoClipSelectionPage,arguments: {
                      //                     "trainingId":
                      //                         '283',
                      //                     "reportId": '35',
                      //                     "type": '2', //1单人 2多人
                      //                   });
                    },
                    child: Container(
                      width: 54.w,
                      height: 22.w,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          border: Border.all(color: Colors.white, width: 1),
                          borderRadius: BorderRadius.circular(14.r)),
                      child: Text(
                        '删除',
                        style: TextStyles.titleSemiBold12,
                      ),
                    ),
                  )
                ],
              ),
            )
        ],
      ),
    );
  }
}
