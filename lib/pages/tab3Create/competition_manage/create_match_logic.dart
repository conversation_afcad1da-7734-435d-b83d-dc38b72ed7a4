import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/competition_list_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/competition_model.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/match_home_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/match_model.dart';
import 'package:shoot_z/routes/app.dart';

class CreateMatchLogic extends GetxController with WidgetsBindingObserver {
  var dateStr = "".obs;
  var timeStr = "".obs;
  var headImgPath = "".obs;
  var contentImgPath = "".obs;
  var leftTeamModel = MatchModel().obs;
  var rightTeamModel = MatchModel().obs;
  var leftColorMap = <String, dynamic>{
    'colorValue': const Color(0xFF191921),
    'colorName': '球衣颜色'
  }.obs;
  var rightColorMap = <String, dynamic>{
    'colorValue': const Color(0xFF191921),
    'colorName': '球衣颜色'
  }.obs;
  var venueName = ''.obs;
  var venueId = 0;
  var reportNum = 0.obs;
  var halfCourtList = <CompetitionModelHalf?>[];
  CompetitionModelLocation placeModel = CompetitionModelLocation();
  var needReport = true.obs;
  var matchItem = MatchModel();
  var matchId = '';
  var isEdit = false;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      if (Get.arguments.containsKey('create')) {
        matchItem = Get.arguments['create'];
      }
      if (Get.arguments.containsKey('edit')) {
        isEdit = true;
        CompetitionModel model = Get.arguments['edit'];
        timeStr.value = model.matchTime ?? '';
        dateStr.value = model.matchDate ?? '';
        venueName.value = model.venueName ?? '';
        leftTeamModel.update((val) {
          val?.id = model.leftTeamId ?? '';
          val?.name = model.leftTeamName ?? '';
          val?.logo = model.leftTeamLogo ?? '';
        });
        rightTeamModel.update((val) {
          val?.id = model.rightTeamId ?? '';
          val?.name = model.rightTeamName ?? '';
          val?.logo = model.rightTeamLogo ?? '';
        });
        halfCourtList = model.half ?? [];
        venueId = model.venueId ?? 0;
        // placeModel.address = locationStr.value;
        // placeModel.cityId = model.location?.cityId;
        // placeModel.lat = model.location?.lat;
        // placeModel.lng = model.location?.lng;
        leftColorMap["colorValue"] =
            Color(int.parse('0x${model.leftTeamColor ?? 'ffa8a8bc'}'));
        rightColorMap["colorValue"] =
            Color(int.parse('0x${model.rightTeamColor ?? 'ffa8a8bc'}'));
        needReport.value = model.matchReport ?? true;
        matchId = model.id ?? '';
      }
    }
    getReportNumber();
  }

  @override
  void onReady() {
    super.onReady();
  }

  getReportNumber() async {
    var res = await Api().get(ApiUrl.getPTZMatchReportNum);
    if (res.isSuccessful()) {
      reportNum.value = res.data ?? 0;
    }
  }

  //创建比赛/修改比赛
  createMatch({String? matchId}) async {
    if (dateStr.value == '') {
      WxLoading.showToast('请选择比赛日期');
      return;
    }
    if (timeStr.value == '') {
      WxLoading.showToast('请选择比赛时间');
      return;
    }
    if (venueName.value == '') {
      WxLoading.showToast('请选择比赛场地');
      return;
    }
    if (leftTeamModel.value.id == null) {
      WxLoading.showToast('请选择两个球队');
      return;
    }
    if (rightTeamModel.value.id == null) {
      WxLoading.showToast('请选择两个球队');
      return;
    }
    List<Map<String, dynamic>?> halfCourtMapList =
        halfCourtList.map((user) => user?.toJson()).toList();
    Map<String, dynamic> request = {
      "leftTeamColor": leftColorMap["colorValue"].value.toRadixString(16),
      "leftTeamId": leftTeamModel.value.id,
      "matchDate": dateStr.value,
      "matchReport": needReport.value,
      "matchTime": timeStr.value,
      "rightTeamColor": rightColorMap["colorValue"].value.toRadixString(16),
      "rightTeamId": rightTeamModel.value.id,
      "venueId": venueId,
      "half": halfCourtMapList
    };
    cc.log("request$request");
    WxLoading.show();
    if (matchId != null) {
      MatchHomeLogic homeLogic = Get.find();
      var url =
          await ApiUrl.editMatch(homeLogic.matchItem.value.id ?? '', matchId);
      var res = await Api().PUT(url, data: request);
      WxLoading.dismiss();
      cc.log("result${res.data}");
      if (res.isSuccessful()) {
        AppPage.back(result: true);
      } else {
        WxLoading.showToast(res.message);
      }
      return;
    }
    var url = await ApiUrl.getMatchList(matchItem.id ?? '');
    var res = await Api().post(url, data: request);
    WxLoading.dismiss();
    cc.log("result${res.data}");
    if (res.isSuccessful()) {
      CompetitionListLogic competitionLogic = Get.find();
      competitionLogic.getdataList();
      AppPage.back(result: true);
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
