import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/create_competition_logic.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class CreateCompetitionPage extends StatelessWidget {
  CreateCompetitionPage({super.key});

  final logic = Get.put(CreateCompetitionLogic());
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: AppBar(
          title: const Text('创建赛事'),
        ),
        body: _createTeamWidget(context),
        bottomNavigationBar: InkWell(
          onTap: () {
            logic.createMatchRequest();
          },
          child: Container(
            width: double.infinity,
            height: 50.w,
            alignment: Alignment.center,
            margin: EdgeInsets.only(
                left: 15.w, right: 15.w, bottom: ScreenUtil().bottomBarHeight),
            decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colours.color7732ED, Colours.colorA555EF],
                  begin: Alignment.bottomLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25.r)),
            child: Text(
              '创建我的赛事',
              style: TextStyles.semiBold14,
            ),
          ),
        ),
      );
    });
  }

  /// 列表数据
  _createTeamWidget(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 102.w,
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              logic.getImage(0);
            },
            child: Container(
              decoration: BoxDecoration(
                  color: const Color(0xFF191921),
                  borderRadius: BorderRadius.circular(50.r)),
              width: 100.w,
              height: 100.w,
              child: logic.headImgPath.value == ""
                  ? WxAssets.images.halfAdd.image(width: 20.w, height: 20.w)
                  : MyImage(
                      bgColor: Colors.transparent,
                      logic.headImgPath.value,
                      // width: 100.w,
                      // height: 100.w,
                      errorImage: "my_team_head.png",
                      placeholderImage: "my_team_head.png",
                      radius: 50.r,
                    ),
            ),
          ),
          Container(
            padding: const EdgeInsets.only(top: 15, bottom: 40),
            child: Text('上传赛事图标',
                style: TextStyles.regular.copyWith(color: Colours.color5C5C6E)),
          ),
          Container(
            margin: EdgeInsets.only(left: 20.w, right: 20.w),
            width: double.infinity,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            height: 52.w,
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(16.r)),
            child: Row(
              children: [
                Expanded(
                    child: TextField(
                  controller: logic.txtController1,
                  style: TextStyles.regular,
                  inputFormatters: [
                    // FilteringTextInputFormatter.allow(
                    //     RegExp(r'[a-zA-Z0-9\u4E00-\u9FFF]')),
                    // FilteringTextInputFormatter.allow(
                    //     RegExp(r'[\u4e00-\u9fa50-9]')), // 只允许输入数字
                    CustomLengthLimitingTextInputFormatter(maxLength: 30),
                  ],
                  decoration: InputDecoration(
                    hintText: '请输入赛事名称',
                    hintStyle:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                    contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                    //让文字垂直居中,
                    border: InputBorder.none,
                  ),
                  keyboardType: TextInputType.name,
                )),
                Text(
                  "${logic.textLength1.value}/15",
                  style:
                      TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class CustomLengthLimitingTextInputFormatter extends TextInputFormatter {
  CustomLengthLimitingTextInputFormatter({required this.maxLength});

  final int maxLength;

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // 计算输入值的有效长度：英文字符计为1，中文字符计为2
    int count = 0;
    for (final rune in newValue.text.runes) {
      if (rune >= 0x4E00 && rune <= 0x9FFF) {
        // 中文字符范围
        count += 2;
      } else {
        count += 1;
      }
      // 如果超过最大长度，则不允许更新
      if (count > maxLength) {
        return oldValue;
      }
    }
    return newValue;
  }
}
