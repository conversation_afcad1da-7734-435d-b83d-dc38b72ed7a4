import 'dart:async';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/ptz/model/connection_status.dart';
import 'package:shoot_z/pages/tab3Create/ptz/model/gimbal_device.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/gimbal_manager.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/gimbal_service.dart';

enum CoursStep {
  placePtz, //放置云台
  openBluetooth, //请开启蓝牙
  connectPtz, //开始连接云台
  showDevice, //展示设备
  connectingPtz, //正在连接云台
  permissionNotEnabled, //未开启权限
  permissionGranted //已开启权限
}

class CreationWayLogic extends GetxController {
  var checked = false.obs; //选中下次不再弹出
  var cameraOpen = false.obs; //相机权限是否打开
  var photoOpen = false.obs; //相册权限是否打开
  var microOpen = false.obs; //麦克风权限是否打开
  var locationOpen = false.obs; //位置权限是否打开
  var bluetoothOpen = false.obs; //蓝牙权限是否打开
  var permissionChecked = false.obs; //权限是否已检查过，避免重复检查
  var isScanning = false.obs;
  var discoveredDevices = Rxn<GimbalDevice>();
  var connectedDevice = Rxn<GimbalDevice>();
  var connectionStatus = ConnectionStatus.disconnected.obs;
  var errorMessage = ''.obs;
  var isPortrait = false;
  // 使用共享的 GimbalManager
  GimbalManager get gimbalManager => GimbalService.gimbal;
  // 流监听订阅
  StreamSubscription<List<GimbalDevice>>? _deviceStreamSubscription;
  StreamSubscription<ConnectionStatus>? _connectionStreamSubscription;
  StreamSubscription<String>? _errorStreamSubscription;
  var coursStep = CoursStep.placePtz.obs;
  @override
  void onInit() {
    super.onInit();
    // 设置流监听
    _setupStreamListeners();
  }

  void startScanning() {
    isScanning.value = true;
    errorMessage.value = '';
    gimbalManager.startScanning();
    // 更新设备列表的逻辑
  }

  void stopScanning() {
    isScanning.value = false;
    gimbalManager.stopScanning();
  }

  /// 检查所有权限是否都已开启
  void checkPermissionAllOpen() {
    if (locationOpen.value &&
        cameraOpen.value &&
        photoOpen.value &&
        microOpen.value) {
      coursStep.value = CoursStep.permissionGranted;
    } else {
      coursStep.value = CoursStep.permissionNotEnabled;
    }
  }

  /// 设置流监听
  void _setupStreamListeners() {
    // 监听设备发现事件
    _deviceStreamSubscription =
        gimbalManager.discoveredDevicesStream.listen((devices) {
      log('发现 ${devices.length} 个设备: ${devices.map((d) => d.name).join(', ')}');
      if (devices.isNotEmpty) {
        discoveredDevices.value = devices.first;
      }
    });

    // 监听连接状态变化
    _connectionStreamSubscription =
        gimbalManager.connectionStatusStream.listen((status) {
      connectionStatus.value = status;
      log('连接状态变化: $status');
      if (status == ConnectionStatus.connected) {
        checkPermissionAllOpen();
      }
    });

    // 监听错误消息
    _errorStreamSubscription = gimbalManager.errorMessageStream.listen((error) {
      errorMessage.value = error;
      if (error.isNotEmpty) {
        log('云台错误: $error');
      }
    });
  }

  @override
  void onClose() {
    // 取消流监听订阅
    _deviceStreamSubscription?.cancel();
    _connectionStreamSubscription?.cancel();
    _errorStreamSubscription?.cancel();
    super.onClose();
  }
}
