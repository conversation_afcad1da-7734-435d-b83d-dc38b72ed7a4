import 'dart:async';
import 'package:flutter_common/wx_bus.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/database/model/TrainingGroup.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/utils/event_bus.dart';

class SelfieShotLocalItemLogic1 extends GetxController {
  RefreshController controller = RefreshController(initialRefresh: false);
  var dataFag = {
    "page": 0,
    "isFrist": true,
  }.obs;
  var type = "1"; //1是单人  2多人  “”全部
  var dataList = <TrainingGroup>[].obs;
  StreamSubscription? subscription;
  var venueId = 0.obs;
  @override
  void onInit() {
    super.onInit();
    subscription = BusUtils.instance.on((action) {
      if (action.key == EventBusKey.deleteLocalVideo1) {
        getdataList(isLoad: false);
      }
    });
  }

  setVenueData(var venueId2) {
    venueId.value = venueId2;
    getdataList(isLoad: false);
  }

  @override
  void dispose() {
    super.dispose();
    subscription?.cancel();
  }

// 分页获取分组数据
  // Future<Map<String, List<ShotRecordModel>>> getPagedGroups(
  //   String userId,
  //   String type, {
  //   int page = 0, // 0-based页码
  //   int pageSize = 10, // 每页多少组
  //   int perGroupLimit = 4, // 每组最多几条
  // }) async {
  //   final database =
  //       await $FloorAppDatabase.databaseBuilder('app_database.db').build();
  //   final selfieShotDao = database.selfieShotDao;
  //   final allRecords = await selfieShotDao.findGroupedTrainings(
  //     userId,
  //     type,
  //     perGroupLimit,
  //   );

  //   // 按training_id分组
  //   final grouped = <String, List<ShotRecordModel>>{};
  //   for (final record in allRecords) {
  //     grouped.putIfAbsent(record.trainingId ?? "", () => []).add(record);
  //   }

  //   // 分页逻辑
  //   final sortedGroups = grouped.entries.toList()
  //     ..sort((a, b) => b.key.compareTo(a.key)); // training_id降序

  //   final start = page * pageSize;
  //   final end = min(start + pageSize, sortedGroups.length);

  //   return Map.fromEntries(sortedGroups.sublist(start, end));
  // }

  //获得最新列表
  getdataList({
    isLoad = true,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 0;
    }
    int page = (dataFag["page"] ?? 0) as int;
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final selfieShotDao = database.selfieShotDao;
    List<ShotRecordModel> filteredNumbers =
        await selfieShotDao.findPagedTrainingRecords(
            UserManager.instance.userInfo.value?.userId ?? "",
            // venueId.value,
            type,
            4,
            200,
            page);

    // 按 training_id 分组
    final groupedRecords = <String, List<ShotRecordModel>>{};
    for (final record in filteredNumbers) {
      if (!groupedRecords.containsKey(record.trainingId)) {
        groupedRecords[record.trainingId ?? "0"] = [];
      }
      groupedRecords[record.trainingId]!.add(record);
    }
    // 转换为 TrainingGroup 对象
    var group = <TrainingGroup>[].obs;
    for (final entry in groupedRecords.entries) {
      final trainingId = entry.key;
      final records = entry.value;

      // 计算统计信息
      // final latestDate =
      //     records.map((r) => r.shootTime).reduce((a, b) => a! > b! ? a : b);
      final latestDate = records.first.createdAt;
      final recordCount = records.length;
      group.add(TrainingGroup(
        trainingId: trainingId,
        records: records,
        latestDate: DateTime.fromMillisecondsSinceEpoch(latestDate!.toInt()),
        recordCount: recordCount,
        siteId: recordCount > 0 ? records.first.venueId.toString() : '',
        siteName: recordCount > 0 ? records.first.venueName.toString() : '',
      ));
    }
    // 按最新日期排序
    group.sort((a, b) => b.latestDate.compareTo(a.latestDate));

    if (isLoad) {
      dataList.addAll(group);
      dataList.refresh();
      if (group.length < 10) {
        controller.loadNoData();
        //  controller.loadComplete();
      } else {
        controller.loadComplete();
      }
    } else {
      controller.resetNoData();
      dataList.assignAll(group);
      controller.refreshCompleted();
    }

    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
