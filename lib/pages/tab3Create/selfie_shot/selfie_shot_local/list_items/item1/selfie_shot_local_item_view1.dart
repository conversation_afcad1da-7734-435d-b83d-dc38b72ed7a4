import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/database/model/TrainingGroup.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/selfie_shot_local/list_items/item1/selfie_shot_local_item_logic1.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/DateTimeUtils.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///半场自由投篮 单人模式
class SelfieShotLocalItemPage1 extends StatelessWidget {
  SelfieShotLocalItemPage1({super.key});

  final logic = Get.put(SelfieShotLocalItemLogic1());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _listWidget1(context),
    );
  }

  /// 列表数据
  _listWidget1(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: logic.controller,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: false, // logic.dataList.isNotEmpty,
        onRefresh: () {
          logic.getdataList(isLoad: false);
        },

        physics: const AlwaysScrollableScrollPhysics(),
        //  physics: const NeverScrollableScrollPhysics(),
        child: (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : logic.dataList.isEmpty
                ? Container(
                    margin: EdgeInsets.only(top: 15.w, bottom: 70.w),
                    child: myNoDataView(
                      context,
                      msg: "暂无视频",
                      textColor: Colours.color5C5C6E,
                      height: 10.w,
                      imagewidget: Container(
                        margin: EdgeInsets.only(left: 10.w),
                        child: WxAssets.images.noVideos
                            .image(width: 96.w, height: 60.w, fit: BoxFit.fill),
                      ),
                    ),
                  )
                : ListView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    padding: EdgeInsets.only(bottom: 40.w),
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: logic.dataList.length,
                    itemBuilder: (context, position) {
                      return _listItemWidget(logic.dataList[position]);
                    }),
      );
    });
  }

  /// 构建列表项
  Widget _listItemWidget(TrainingGroup item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 20.w),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      DateTimeUtils.formatDateTimeWithSeconds(item.latestDate),
                      style: TextStyle(
                        color: Colours.color5C5C6E,
                        height: 1,
                        fontSize: 14.sp,
                      ),
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Text(
                      "${item.siteName == "" ? "即刻创作" : item.siteName}",
                      style: TextStyle(
                        color: Colours.colorA8A8BC,
                        height: 1,
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  //自由半场 本地视频 单个比赛详情
                  AppPage.to(Routes.SelfieShotLocalInfoPage, arguments: {
                    "latestDate": DateTimeUtils.formatDateTimeWithSeconds(
                        item.latestDate),
                    "siteId": item.siteId,
                    "siteName": item.siteName,
                    "trainingId": item.trainingId,
                    "type": "1",
                  });
                },
                child: Container(
                  height: 30.w,
                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                  decoration: BoxDecoration(
                      image: DecorationImage(
                    image: WxAssets.images.moreHiglightsBg.provider(),
                    fit: BoxFit.fill,
                  )),
                  child: Row(
                    children: [
                      Text(
                        S.current.view_more,
                        style: TextStyle(color: Colours.white, fontSize: 12.sp),
                      ),
                      WxAssets.images.icArrowRight.image(
                          width: 12.w,
                          height: 12.w,
                          color: Colours.white,
                          fit: BoxFit.fill),
                    ],
                  ),
                ),
              )
            ],
          ),
          GridView.builder(
              scrollDirection: Axis.vertical,
              // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
              shrinkWrap: true,
              physics:
                  const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 15.w,
                mainAxisSpacing: 15.w,
                childAspectRatio: 165 / 94,
              ),
              padding: EdgeInsets.only(bottom: 10.w, top: 15.w),
              itemCount: item.records.length,
              itemBuilder: (context, index) {
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    // 0 待合成；1 合成中 ； 2 合成完成；3 失败
                    AppPage.to(Routes.videoPath, arguments: {
                      "videoPath": (item.records[index].filePath ?? ""),
                      "teamName": "",
                      "isShowShareUpdate": "3",
                      "shotRecordModel": item.records[index],
                    });
                  },
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          // (item.records[index].cover ?? "") != ""
                          //     ? MyImage(
                          //         item.records[index].cover ?? "",
                          //         width: double.infinity,
                          //         height: 93.w,
                          //         radius: 8.r,
                          //         fit: BoxFit.fill,
                          //         errorImage: "error_image.png",
                          //         placeholderImage: "error_image.png",
                          //       )
                          //     :
                          MyImage(
                            "error_shot_video.png",
                            width: 165.w,
                            isAssetImage: true,
                            height: 94.w,
                            radius: 8.r,
                            fit: BoxFit.fill,
                            errorImage: "error_shot_video.png",
                            placeholderImage: "error_shot_video.png",
                          ),
                          Positioned(
                            bottom: 5.w,
                            right: 5.w,
                            child: Container(
                              padding: EdgeInsets.only(
                                  left: 5.w, right: 5.w, top: 2.w, bottom: 2.w),
                              decoration: BoxDecoration(
                                  color: Colours.color65000000,
                                  borderRadius: BorderRadius.circular(2.r)),
                              child: Text(
                                DateTimeUtils.formatDateTimeWithSeconds(
                                    DateTime.fromMillisecondsSinceEpoch(
                                        (item.records[index].shootTime ?? 0.0)
                                            .toInt())),
                                textAlign: TextAlign.right,
                                style: TextStyles.medium.copyWith(
                                    fontSize: 10.sp, color: Colours.white),
                              ),
                            ),
                          ),
                          WxAssets.images.selfieShotPlay
                              .image(width: 25.w, height: 25.w)
                        ],
                      ),
                    ],
                  ),
                );
              }),
        ],
      ),
    );
  }
}
