// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:developer';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/half_shooting_records_model.dart';
import 'package:shoot_z/network/model/my_training_achievements_model.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:ui_packages/ui_packages.dart';

class SelfieShotItemLogic2 extends GetxController
    with GetSingleTickerProviderStateMixin {
  static const platform =
      MethodChannel('com.example.my_flutter_app/native_method');
  Future<void> _showNativeView(String trainingId, String sampleVideoUrl) async {
    try {
      await platform.invokeMethod('showNativeView', {
        "trainingId": trainingId,
        "type": 2,
        "sampleVideoUrl": sampleVideoUrl
      });
    } on PlatformException catch (e) {
      print("native_method：Failed to invoke native method: '${e.message}'.");
    }
  }

  var dataFag = {
    "isFrist": true,
  }.obs;
  var halfShootingRecordsModel = HalfShootingRecordsModel().obs;
  var sampleVideoUrl = "";
  var videoPath = "".obs;
  var imgPath = "".obs;
  var videosDataList = <MyTrainingAchievementsModel>[].obs;
  var trainingId = "".obs;

  var selectHalfCourt = [].obs; //场地中的半场 最多2个
  var venueName = ''.obs; //场地名称
  var venueId = 0.obs; //场地id
  late AnimationController _controller;
  RxDouble progress = (0.0).obs;
  @override
  void onInit() {
    super.onInit();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..addListener(() async {
        progress.value = _controller.value;
        if (progress.value >= 1) {
          getHalfShootingRecordingEnd2(trainingId.value);
        }
      });
  }

  setVenueData(var venueId2, var venueName2, RxList<dynamic> selectHalfCourt2) {
    venueName.value = venueName2;
    venueId.value = venueId2;
    selectHalfCourt.assignAll(selectHalfCourt2);
    getHalfShootingRecords();
    getVideoList();
  }

  setVideos(var videoPath2, var imgPath2) {
    imgPath.value = imgPath2;
    videoPath.value = videoPath2;
  }

  //开始录制
  getHalfShootingRecording() async {
    Map<String, dynamic> param = {
      'trainType': 2, //1 单人；2 多人
      "venueCourtId":
          selectHalfCourt.isEmpty ? 0 : selectHalfCourt.first["id"], //场地半场id
      'venueId': venueId.value, //场地id
    };
    var res = await Api().post(ApiUrl.halfShootingRecording, data: param);
    if (sampleVideoUrl == "") {
      var videoRes = await Api().get(ApiUrl.sampleVideo, queryParameters: {});
      if (videoRes.isSuccessful()) {
        sampleVideoUrl = videoRes.data["path"];
        log("videoRes!!!!!!!!:$sampleVideoUrl");
      }
    }
    if (res.isSuccessful()) {
      UserManager.instance.postChannelSubmit(0,
          channelParam: "shots"); //埋点'0 通用， 1 注册，2 登陆，3 跳转'
      trainingId.value = res.data["trainingId"].toString();
      log("halfShootingRecording:${jsonEncode(res.data)}");
      //记录当前时间
      if (res.data["createdTime"] != null) {
        final String createTime = res.data["createdTime"].toString();
        WxStorage.instance
            .setDouble('shotCreateTime', stringToDoubleTimestamp(createTime));
      }
      _showNativeView(trainingId.value, sampleVideoUrl);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  double stringToDoubleTimestamp(String dateString,
      {String format = 'yyyy-MM-dd HH:mm:ss'}) {
    try {
      // 解析字符串为DateTime
      DateTime dateTime = DateFormat(format).parse(dateString);
      // 转换为double类型时间戳（毫秒级）
      return dateTime.millisecondsSinceEpoch.toDouble();
    } catch (e) {
      throw FormatException('无法解析时间字符串: $dateString');
    }
  }

  //弹出"版本更新"对话框
  showUploadListDialog(BuildContext context) async {
    return showDialog<void>(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context2) {
          return WillPopScope(
              onWillPop: () async {
                print('返回了');
                return false;
              },
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
                child: Material(
                  type: MaterialType.transparency,
                  color: Colors.transparent,
                  child: Center(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Container(
                            color: Colors.transparent,
                            child: Column(
                              children: <Widget>[
                                Container(
                                  alignment: Alignment.topLeft,
                                  height: 144.w,
                                  decoration: const BoxDecoration(
                                    color: Colours.color191921,
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 18, vertical: 2),
                                  width: double.infinity,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: <Widget>[
                                      const SizedBox(
                                        height: 30,
                                      ),
                                      Center(
                                        child: Text(
                                          ("提示"), //"1.修复了一些BUG;\n2.更新部分内容",//
                                          //  "1.修复了一些BUG;\n2.更新部分内容" * 20,
                                          style: TextStyles.titleSemiBold16,
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 20,
                                      ),
                                      Obx(() {
                                        return LinearPercentIndicator(
                                          lineHeight: 4,
                                          linearGradient: const LinearGradient(
                                              colors: [
                                                Colours.color7732ED,
                                                Colours.colorA555EF
                                              ]),
                                          percent: progress.value, //??
                                          //     controller.progressNotifier
                                          //         .value, //uploadController.progress, //
                                          backgroundColor: Colours.color000000,
                                          //    progressColor: Colours.colorA555EF, // 已完成的任务为绿色，否则根据进度设置颜色
                                          barRadius: const Radius.circular(3),
                                          animation: true,
                                          animateFromLastPercent: true,
                                          animationDuration: 500,
                                        );
                                      }),
                                      const SizedBox(
                                        height: 20,
                                      ),
                                      Text(
                                        ("正在AI分析视频中，即将生成数据报告"), //"1.修复了一些BUG;\n2.更新部分内容",//
                                        //  "1.修复了一些BUG;\n2.更新部分内容" * 20,
                                        style: TextStyles.regular.copyWith(
                                            color: Colours.colorA8A8BC,
                                            fontSize: 14.sp),
                                        maxLines: 20,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      const SizedBox(
                                        height: 20,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ));
        });
  }

  getHalfShootingRecordingEnd(String trainingId1) async {
    trainingId.value = trainingId1;
    showUploadListDialog(Get.context!);
    startAnimation();
  }

  void startAnimation() {
    _controller.reset(); // 重置到0
    progress.value = 0; // 重置进度
    _controller.forward();
  }

//结束录制
  getHalfShootingRecordingEnd2(String trainingId) async {
    await Future.delayed(const Duration(microseconds: 100));
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final selfieShotDao = database.selfieShotDao;
    List<ShotRecordModel> filteredNumbers = await selfieShotDao.findAllShot(
        trainingId, UserManager.instance.userInfo.value?.userId ?? "", "2");
    var list2 = [];
    for (var shotRecordModel in filteredNumbers) {
      int? number = int.tryParse((shotRecordModel.goalTime ?? "0").toString());
      int? number2 =
          int.tryParse((shotRecordModel.shootTime ?? "0").toString());
      Map<String, dynamic> param = {
        "eventId": shotRecordModel.eventId,
        "goalTime": (number != null && number > 0)
            ? number.toString()
            : shotRecordModel.startTime ?? "",
        "hit": shotRecordModel.isGoal ?? false,
        "playerImage": "",
        "shootTime": (number2 != null && number2 > 0)
            ? number2.toString()
            : shotRecordModel.startTime ?? "",
        "shootX": (shotRecordModel.shootCoord?.length ?? 0) > 0
            ? shotRecordModel.shootCoord?.first
            : 0.0,
        "shootY": (shotRecordModel.shootCoord?.length ?? 0) > 1
            ? shotRecordModel.shootCoord?.last
            : 0.0,
        "shotType": 1,
        "videoPath": "",
        "timestamp": shotRecordModel.startTime ?? "",
      };

      list2.add(param);
    }
    log("halfShootingRecordingEnd1:${trainingId}-${jsonEncode(list2)}");
    var url2 = await ApiUrl.halfShootingRecordingEnd2(trainingId);
    try {
      var res = await Api().post(url2, data: list2);
      log("halfShootingRecordingEnd2:${jsonEncode(res.data)}");
      if (res.isSuccessful()) {
        Get.back();
        await Future.delayed(const Duration(microseconds: 200));
        AppPage.to(Routes.selfieShotInfoPage, arguments: {
          "type": "2",
          "trainingId": trainingId,
          "halfCourt": selectHalfCourt,
          "venueId": venueId.value,
          "venueName": venueName.value,
        }).then((onValue) {
          getHalfShootingRecords();
          getVideoList();
        });
      } else {
        WxLoading.showToast(res.message);
      }
    } catch (e) {
      log("halfShootingRecordingEnd21:${e}");
    }
  }

  //获得投篮记录列表
  getHalfShootingRecords() async {
    Map<String, dynamic> param = {
      'pageIndex': 1,
      'pageSize': 1,
      'trainingType': 2, //1单人 2多人
      'venueId': -1, //场地id
    };
    var res =
        await Api().get(ApiUrl.halfShootingRecords, queryParameters: param);
    log(jsonEncode(res.data));
    if (res.isSuccessful()) {
      halfShootingRecordsModel.value =
          HalfShootingRecordsModel.fromJson(res.data);
      halfShootingRecordsModel.refresh();
    } else {
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  //获得集锦列表
  getVideoList() async {
    Map<String, dynamic> param = {
      'userId': UserManager.instance.user?.userId,
      'pageIndex': dataFag["page"] ?? 1,
      'pageSize': 4,
      'trainingType': "2", //训练类型：0 不限；1 单人；2 多人
      'venueId': "-1", // 场地id，-1或不传：查所有；0查即刻创作；具体id查场地集锦
      "trainingId": "0" //训练id，0或不传：查所有；查具体训练生成的集锦
    };
    var res =
        await Api().get(ApiUrl.myTrainingAchievements, queryParameters: param);
    log(jsonEncode(res.data));
    if (res.isSuccessful()) {
      // halfShootingRecordsModel.value =ShootingVideosModel
      List list = res.data["result"] ?? [];
      List<MyTrainingAchievementsModel> modelList =
          list.map((e) => MyTrainingAchievementsModel.fromJson(e)).toList();
      log("zzzzzz12removeAt-${res.data}");
      videosDataList.assignAll(modelList);
      videosDataList.refresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  Future<void> getDeleteVideo(MyTrainingAchievementsModel data) async {
    Map<String, dynamic> param2 = {"id": data.id};
    var url = await ApiUrl.getDeleteVideo(data.id ?? "");
    var res = await Api().delete(url, data: param2);
    log("$param2");
    log(jsonEncode(res.data));
    if (res.isSuccessful()) {
      getVideoList();
    } else {
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }
}
