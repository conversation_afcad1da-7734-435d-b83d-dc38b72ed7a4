// ignore_for_file: use_build_context_synchronously

import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/venue_goal_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab3Create/shoot_composite/venue_goal_tab/item2/venue_goal_item_logic2.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:intl/intl.dart';

///场地->三级页面  选择场地进球
class VenueGoalItemPage2 extends StatelessWidget {
  VenueGoalItemPage2({super.key});
  final logic = Get.put(VenueGoalItemLogic2());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : KeepAliveWidget(
                child: SafeArea(
                  bottom: false,
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        //视频播放器
                        _videoWidget(),
                        //选择时间
                        _chooseTime(context),
                        Container(
                          width: double.infinity,
                          alignment: Alignment.center,
                          color: Colours.bg_color,
                          child: TabBar(
                              controller: logic.tabController,
                              unselectedLabelColor: Colours.color5C5C6E,
                              unselectedLabelStyle: TextStyle(
                                  fontSize: 18.sp,
                                  color: Colours.color5C5C6E,
                                  fontWeight: FontWeight.w600),
                              labelColor: Colours.white,
                              labelStyle: TextStyle(
                                  fontSize: 20.sp,
                                  color: Colours.white,
                                  fontWeight: FontWeight.w600),
                              isScrollable: true,
                              tabAlignment: TabAlignment.center,
                              // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                              indicatorPadding: EdgeInsets.zero,
                              dividerColor: Colors.transparent,
                              indicatorColor: Colors.transparent, // 设置指示器颜色为透明
                              indicator: const BoxDecoration(
                                  color: Colors.transparent), // 使用空装饰完全移除指示器
                              dividerHeight: 0,
                              labelPadding: const EdgeInsets.symmetric(
                                  horizontal: 4.0), // 调整标签间的间距
                              indicatorSize: TabBarIndicatorSize.label,
                              padding: EdgeInsets.zero,
                              physics: const NeverScrollableScrollPhysics(),
                              tabs: List.generate(logic.halfCourt.length,
                                  (index) {
                                return SizedBox(
                                  width: 70.w,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      ShaderMask(
                                        shaderCallback: (bounds) =>
                                            const LinearGradient(
                                          colors: [
                                            Colours.colorFFF9DC,
                                            Colours.colorE4C8FF,
                                            Colours.colorE5F3FF,
                                          ],
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                        ).createShader(bounds),
                                        child: Text(
                                          "${logic.halfCourt[index]["name"]}", //    logic.halfCourt[index]["name"]
                                          style: TextStyles.regular.copyWith(
                                            fontWeight:
                                                logic.tabbarIndex.value == index
                                                    ? FontWeight.w600
                                                    : FontWeight.w400,
                                            fontSize:
                                                logic.tabbarIndex.value == index
                                                    ? 16.sp
                                                    : 14.sp,
                                            color:
                                                logic.tabbarIndex.value == index
                                                    ? Colours.white
                                                    : Colours.color5C5C6E,
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 5.w,
                                      ),
                                      if (logic.tabbarIndex.value == index)
                                        Container(
                                            width: 16.w,
                                            height: 3.w,
                                            margin: EdgeInsets.only(top: 4.w),
                                            decoration: BoxDecoration(
                                              gradient: const LinearGradient(
                                                colors: [
                                                  Colours.color7732ED,
                                                  Colours.colorA555EF,
                                                ],
                                                begin: Alignment.centerLeft,
                                                end: Alignment.centerRight,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(2.r),
                                            )),
                                    ],
                                  ),
                                );
                              })),
                        ),
                        SizedBox(
                          height: 10.w,
                        ),
                        Expanded(
                          child: TabBarView(
                              controller: logic.tabController,
                              children: List.generate(logic.halfCourt.length,
                                  (index) {
                                return index == 0
                                    ? _optionGoalWidget(
                                        context, logic.state.dataList1, 0)
                                    : _optionGoalWidget(
                                        context, logic.state.dataList2, 1);
                              })),
                        ),
                        //选球
                        //   Expanded(child: _optionGoalWidget(context)),
                      ]),
                ),
              );
      }),
      bottomNavigationBar: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? const SizedBox()
            : Container(
                width: double.infinity,
                padding: EdgeInsets.only(
                    bottom: 25.w, left: 5.w, right: 15.w, top: 10.w),
                child: Row(
                  children: [
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () async {
                        // logic.compositeVideo([]);
                        // return;
                        // 方法2：使用JSON序列化（100%隔离）
                        var list2 = logic.state.checkedList.where((v) {
                          return v.isCheck.value == true;
                        }).toList();
                        if (list2.isEmpty || list2.length <= 1) {
                          WxLoading.showToast("请先选择两个视频");
                          return;
                        }
                        if (list2.length > 20) {
                          WxLoading.showToast("合成视频最多只能选择20个进球片段");
                          return;
                        }

                        logic.state.checkedList3
                            .assignAll(list2.map((e) => e.copyWith()).toList());
                        //素材库
                        showLibraryDialog(context);
                      },
                      child: Stack(
                        children: [
                          WxAssets.images.optionDocument
                              .image(width: 40.w, height: 40.w),
                          if (logic.state.totalCheckedCount.value > 0)
                            Positioned(
                              right: 0,
                              child: Transform.translate(
                                offset: const Offset(5, 0), // 移动50像素到右和下
                                child: Container(
                                  padding: EdgeInsets.only(
                                      left: 5.w,
                                      right: 5.w,
                                      top: 1.w,
                                      bottom: 1.w),
                                  decoration: BoxDecoration(
                                    color: Colours.red,
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(28.r)),
                                  ),
                                  child: Text(
                                    "${logic.state.totalCheckedCount.value}",
                                    style: TextStyles.titleMedium18
                                        .copyWith(fontSize: 10.sp),
                                  ),
                                ),
                              ),
                            )
                        ],
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () async {
                        var list2 = logic.state.checkedList.where((v) {
                          return v.isCheck.value == true;
                        }).toList();
                        if (list2.isEmpty || list2.length <= 1) {
                          WxLoading.showToast("请先选择两个视频");
                          return;
                        }
                        if (list2.length > 20) {
                          WxLoading.showToast("合成视频最多只能选择20个进球片段");
                          return;
                        }
                        logic.state.checkedList3
                            .assignAll(list2.map((e) => e.copyWith()).toList());
                        //素材库
                        showLibraryDialog(context);
                      },
                      child: Container(
                        height: 46.w,
                        width: 295.w,
                        alignment: Alignment.center,
                        margin: EdgeInsets.only(left: 14.w, right: 0),
                        padding: EdgeInsets.only(
                            left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                        decoration: BoxDecoration(
                          color: Colours.color282735,
                          borderRadius: BorderRadius.all(Radius.circular(28.r)),
                          gradient: const LinearGradient(
                            colors: [Colours.color7732ED, Colours.colorA555EF],
                            begin: Alignment.bottomLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Text(
                          S.current.desynthesis,
                          style: TextStyles.semiBold14,
                        ),
                      ),
                    ),
                  ],
                ),
              );
      }),
      floatingActionButtonLocation:
          FloatingActionButtonLocation.endFloat, // 默认位置
    );
  }

  Container _chooseTime(BuildContext context) {
    return Container(
      height: 50.w,
      alignment: Alignment.centerLeft,
      margin: EdgeInsets.only(top: 5.w, bottom: 5.w, right: 10.w, left: 15.w),
      child: Row(
        children: [
          // Text(
          //   logic.courtName.value,
          //   style: TextStyle(color: Colours.white, fontSize: 15.sp),
          // ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              //选择进球的时间
              showDateDialog(context);
            },
            child: Container(
              height: 50.w,
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  Text(
                    "${logic.state.videoStartTime.value.trim().contains(logic.todayDate) ? S.current.today : logic.state.videoStartTime.value.trim().contains(logic.yesterdayDate) ? S.current.yesterday : logic.state.videoStartTime.value.trim().length >= 10 ? logic.state.videoStartTime.trim().substring(5, 10) : logic.state.videoDate.value}\t\t\t${logic.state.videoStartTime.value.trim().length >= 16 ? logic.state.videoStartTime.trim().substring(11, 16) : logic.state.videoStartTime}～${logic.state.videoEndTime.value.trim().length >= 16 ? logic.state.videoEndTime.trim().substring(11, 16) : logic.state.videoEndTime}",
                    textAlign: TextAlign.left,
                    style: TextStyle(
                      color: Colours.white,
                      fontSize: 15.sp,
                    ),
                  ),
                  SizedBox(
                    width: 3.w,
                  ),
                  const Icon(
                    Icons.keyboard_arrow_down,
                    color: Colours.white,
                    size: 22,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            width: 30.w,
          ),
          const Spacer(),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              MyShareH5.getShareH5(ShareGoalVideos(
                  fragmentId: logic.state.indexVideoId.value,
                  sharedFrom:
                      UserManager.instance.userInfo.value?.userId ?? ""));
            },
            child: SizedBox(
                width: 42.w,
                height: 50.w,
                child: WxAssets.images.optionShare
                    .image(height: 22.w, width: 22.w, color: Colours.white)),
          ),
          GestureDetector(
            onTap: () {
              logic.getDownLoadList();
            },
            child: SizedBox(
                width: 42.w,
                height: 50.w,
                child: WxAssets.images.optionDownload
                    .image(height: 22.w, width: 22.w, color: Colours.white)),
          ),
        ],
      ),
    );
  }

  Widget _videoWidget() {
    return SizedBox(
      width: double.infinity,
      height: ScreenUtil().screenWidth / 375 * 211,
      child: AspectRatio(
        aspectRatio: 375 / 211, // 宽高比
        child: VideoView(
          controller: logic.videoController,
        ),
      ),
    );
  }

  Widget _optionGoalWidget(
      BuildContext context, RxList<VenueGoalModel> dataList, int tabIndex) {
    return Obx(() {
      return dataList.isEmpty
          ? Column(
              children: [
                Expanded(
                  child: myNoDataView(
                    context,
                    msg: S.current.no_goal,
                    imagewidget: WxAssets.images.noGoal
                        .image(width: 100.w, height: 84.w),
                  ),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    logic.chooseTime(logic.state.videoNextStartTime.value,
                        logic.state.videoNextEndTime.value, 3);
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 10.w),
                    margin: EdgeInsets.symmetric(vertical: 40.w),
                    child: RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                          text: S.current.option_next_time,
                          style: TextStyle(
                              color: Colours.color5D5D6E,
                              fontSize: 13.sp,
                              height: 1,
                              fontWeight: FontWeight.w400),
                          children: <InlineSpan>[
                            TextSpan(
                                text:
                                    "\t\t${logic.state.videoNextStartTime.value.trim().length >= 16 ? logic.state.videoNextStartTime.value.trim().substring(11, 16) : logic.state.videoNextStartTime.value}～${logic.state.videoNextEndTime.value.trim().length >= 16 ? logic.state.videoNextEndTime.value.trim().substring(11, 16) : logic.state.videoNextEndTime.value}",
                                style: TextStyle(
                                    color: Colours.app_main,
                                    fontSize: 13.sp,
                                    height: 1,
                                    fontWeight: FontWeight.w400)),
                          ]),
                    ),
                  ),
                ),
              ],
            )
          : Padding(
              padding: EdgeInsets.only(left: 15.w, right: 15.w),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    GridView.builder(
                        scrollDirection: Axis.vertical,
                        // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        //  const AlwaysScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          crossAxisSpacing: 15,
                          mainAxisSpacing: 15,
                          childAspectRatio: 105 / 59,
                        ),
                        padding: EdgeInsets.only(bottom: 20.w),
                        itemCount: dataList.length,
                        itemBuilder: (context, position) {
                          return Obx(() {
                            return GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () async {
                                if (dataList[position].id ==
                                    logic.state.indexVideoId.value) {
                                  logic.checkVideo(position, tabIndex,
                                      isToNext: true);
                                } else {
                                  logic.changeVideoIndex(position, tabIndex);
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  // color: (dataList[position].id ==
                                  //         logic.state.indexVideoId.value)
                                  //     ? Colours.color291A3B
                                  //     : Colours.color191921,
                                  border: (dataList[position].id ==
                                          logic.state.indexVideoId.value)
                                      ? Border.all(
                                          width: 2.w, color: Colours.white)
                                      : null,
                                  borderRadius: BorderRadius.circular(8.r),
                                  image: (dataList[position].isCheck.value ||
                                          dataList[position].id ==
                                              logic.state.indexVideoId.value)
                                      ? const DecorationImage(
                                          image: AssetImage(
                                              "assets/images/goal_bg3.png"),
                                          fit: BoxFit.fill)
                                      : const DecorationImage(
                                          image: AssetImage(
                                              "assets/images/goal_bg4.png"),
                                          fit: BoxFit.fill),
                                ),
                                child: Stack(
                                  children: [
                                    Positioned(
                                      top: 0.w,
                                      right: 0.w,
                                      child: GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () async {
                                          logic.checkVideo(position, tabIndex);
                                        },
                                        child: Padding(
                                          padding: EdgeInsets.all(8.w),
                                          child: (dataList[position]
                                                  .isCheck
                                                  .value)
                                              ? WxAssets
                                                  .images.checkWhiteBorderYes
                                                  .image(
                                                      height: 14.w, width: 14.w)
                                              : WxAssets.images.checkWhiteBorder
                                                  .image(
                                                      height: 14.w,
                                                      width: 14.w),
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                        bottom: 10.w,
                                        child: Container(
                                          width: 103.w,
                                          padding: EdgeInsets.only(
                                              left: 10.w, right: 10.w),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              // if (dataList[position].id ==
                                              //     logic
                                              //         .state.indexVideoId.value)
                                              //   Container(
                                              //     height: 10.w,
                                              //     width: 10.w,
                                              //     alignment: Alignment.center,
                                              //     child: const LoadingIndicator(
                                              //       pathBackgroundColor:
                                              //           Colors.black26,
                                              //       indicatorType: Indicator
                                              //           .lineScaleParty,
                                              //       colors: [Colours.white],
                                              //     ),
                                              //   ),
                                              Expanded(
                                                child: Text(
                                                  dataList[position]
                                                      .shotTime, // dataList[position].id,//    dataList[position].videoTime,
                                                  textAlign: TextAlign.right,
                                                  style: TextStyles.medium
                                                      .copyWith(
                                                          fontSize: 12.sp,
                                                          color: Colours
                                                              .colorA8A8BC,
                                                          fontFamily: "DIN"),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ))
                                  ],
                                ),
                              ),
                            );
                          });
                        }),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        logic.chooseTime(logic.state.videoNextStartTime.value,
                            logic.state.videoNextEndTime.value, 3);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 10.w),
                        margin: EdgeInsets.symmetric(vertical: 40.w),
                        child: RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                              text: S.current.option_next_time,
                              style: TextStyle(
                                  color: Colours.color5D5D6E,
                                  fontSize: 13.sp,
                                  height: 1,
                                  fontWeight: FontWeight.w400),
                              children: <InlineSpan>[
                                TextSpan(
                                    text:
                                        "\t\t${logic.state.videoNextStartTime.value.trim().length >= 16 ? logic.state.videoNextStartTime.trim().substring(11, 16) : logic.state.videoNextStartTime}～${logic.state.videoNextEndTime.value.trim().length >= 16 ? logic.state.videoNextEndTime.trim().substring(11, 16) : logic.state.videoNextEndTime}",
                                    style: TextStyle(
                                        color: Colours.app_main,
                                        fontSize: 13.sp,
                                        height: 1,
                                        fontWeight: FontWeight.w400)),
                              ]),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
    });
  }

  void showDateDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      builder: (context) {
        return Container(
          height: 410.w,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24.r),
            color: Colours.color191921,
          ),
          child: Column(
            children: [
              Container(
                width: 38.w,
                height: 4,
                margin: EdgeInsets.only(top: 8.w),
                decoration: BoxDecoration(
                    color: Colours.color10D8D8D8,
                    borderRadius: BorderRadius.circular(4.r)),
              ),
              Container(
                width: double.infinity,
                height: 50.w,
                padding: EdgeInsets.only(top: 10.w, left: 9.w),
                alignment: Alignment.bottomCenter,
                child: Container(
                  width: Get.width,
                  height: 44,
                  //Screen.navigationBarHeight,
                  padding: EdgeInsets.only(top: 0.w, left: 50, right: 50),
                  decoration: const BoxDecoration(),
                  child: TabBar(
                    controller: logic.tabDialogController,
                    unselectedLabelColor: Colours.color9393A5,
                    unselectedLabelStyle: TextStyles.textBold16
                        .copyWith(color: Colours.color9393A5),
                    labelColor: Colours.white,
                    labelStyle: TextStyles.textBold16,
                    isScrollable: false,
                    // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                    indicatorPadding: EdgeInsets.zero,
                    dividerColor: Colors.transparent,
                    dividerHeight: 0,
                    indicatorColor: Colors.transparent,
                    // indicatorSize: TabBarIndicatorSize.tab,
                    // // dragStartBehavior: DragStartBehavior.start,
                    // indicatorWeight: 1,
                    // indicator: RoundUnderlineTabIndicator(
                    //   borderSide: BorderSide(
                    //     width: 3.5,
                    //     color: Colors.white,
                    //   ),
                    // ),
                    tabs: [S.current.Recommended_period, S.current.Exact_search]
                        .map((e) => gradientTab(
                            e,
                            [
                              S.current.Recommended_period,
                              S.current.Exact_search
                            ].indexOf(e),
                            logic.tabDialogController!))
                        .toList(),
                    // tabs: [S.current.Recommended_period, S.current.Exact_search]
                    //     .map((type) => Tab(text: "${type}"))
                    //     .toList(),
                  ),
                ),
              ),
              SizedBox(
                height: 10.w,
              ),
              Expanded(
                child: TabBarView(
                    controller: logic.tabDialogController,
                    children: [
                      recommendedTimeWidget1(),
                      recommendedTimeWidget2(context),
                    ]),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 自定义 Tab，选中时渐变色，未选中时普通颜色
  Widget gradientTab(String text, int index, TabController controller) {
    return AnimatedBuilder(
      animation: controller.animation!,
      builder: (context, child) {
        bool isSelected = controller.index == index;
        return Container(
          padding: EdgeInsets.only(bottom: 4.w),
          child: ShaderMask(
            shaderCallback: (bounds) {
              return LinearGradient(
                colors: isSelected
                    ? [
                        Colours.colorFFECC1,
                        Colours.colorE7CEFF,
                        Colours.colorD1EAFF
                      ]
                    : [Colours.colorD6D6D6, Colours.colorD6D6D6],
              ).createShader(bounds);
            },
            child: Text(
              text,
              style: TextStyle(
                fontSize: isSelected ? 16.sp : 14.sp,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: Colors.white,
              ),
            ),
          ),
        );
      },
    );
  }

  void showChooseGoalDialog(BuildContext context, List<VenueGoalModel> list2) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            color: Colours.color191921,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 25.w, bottom: 15.w),
                  child: Text(
                    "视频效果与个性化",
                    style: TextStyles.medium
                        .copyWith(fontSize: 14.sp, color: Colours.white),
                  ),
                ),
                Wrap(
                  spacing: 10.w,
                  runSpacing: 10.w,
                  children: List.generate(3, (index2) {
                    int index = index2 + 1;
                    return GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        //单个视频片段时长
                        logic.state.compositeOption2[index] =
                            logic.state.compositeOption2[index] == "0"
                                ? "1"
                                : "0";
                      },
                      child: Container(
                        width: 105.w,
                        height: 40.w,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            color: Colours.color22222D,
                            borderRadius: BorderRadius.circular(22.r),
                            gradient: logic.state.compositeOption2[index] == "1"
                                ? const LinearGradient(colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ])
                                : null,
                            border: logic.state.compositeOption2[index] != "1"
                                ? Border.all(width: 1, color: Colours.white)
                                : null),
                        child: Text(
                          index == 0
                              ? S.current.composite_video_dialog_tips4
                              : index == 1
                                  ? S.current.composite_video_dialog_tips5
                                  : index == 2
                                      ? S.current.composite_video_dialog_tips6
                                      : S.current.composite_video_dialog_tips7,
                          style: TextStyles.medium
                              .copyWith(fontSize: 14.sp, color: Colours.white),
                        ),
                      ),
                    );
                  }),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    logic.state.isShare.value = !logic.state.isShare.value;
                  },
                  child: Container(
                    padding: EdgeInsets.only(top: 20.w, bottom: 20.w),
                    child: Row(
                      children: [
                        logic.state.isShare.value
                            ? WxAssets.images.checkOn3
                                .image(width: 16.w, height: 16.w)
                            : WxAssets.images.checkOn3Wihte
                                .image(width: 16.w, height: 16.w),
                        SizedBox(
                          width: 6.w,
                        ),
                        Text(
                          "共享到场地展示为精彩视频",
                          style: TextStyles.medium
                              .copyWith(fontSize: 14.sp, color: Colours.white),
                        )
                      ],
                    ),
                  ),
                ),
                Container(
                  height: 46.w,
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                    color: Colours.color0F0F16,
                    borderRadius: BorderRadius.circular(23.r),
                  ),
                  margin: EdgeInsets.only(bottom: 5.w, top: 20.w),
                  padding: EdgeInsets.only(
                      left: 8.w, right: 5.w, top: 4.w, bottom: 3.w),
                  child: TextField(
                    controller: logic.nickNameController,
                    style: TextStyle(
                      color: Colours.white,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.deny(
                          RegExp(r'[" "]')), // 只允许输入数字
                      LengthLimitingTextInputFormatter(20), // 限制输入长度为8
                    ],
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: S.current.video_name,
                      contentPadding: EdgeInsets.only(left: 10.w, bottom: 10.w),
                      hintStyle: TextStyle(
                        color: Colours.color999999,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () async {
                    //一键成片
                    Get.back();
                    await Future.delayed(const Duration(milliseconds: 100));
                    logic.compositeVideo(list2);
                  },
                  child: Container(
                    height: 46.w,
                    width: double.infinity,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(top: 25.w, bottom: 15.w),
                    padding: EdgeInsets.only(
                        left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                    decoration: BoxDecoration(
                      color: Colours.color282735,
                      borderRadius: BorderRadius.all(Radius.circular(28.r)),
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Text(
                      S.current.One_click_make_piece,
                      style: TextStyles.semiBold14,
                    ),
                  ),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    logic.state.rememberOption.value =
                        logic.state.rememberOption.value == "0" ? "1" : "0";
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 3.w),
                    alignment: Alignment.center,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                            width: 14.w,
                            height: 14.w,
                            margin: EdgeInsets.only(right: 8.w, bottom: 3.w),
                            child: logic.state.rememberOption.value == "0"
                                ? MyImage(
                                    "check_on_3.png",
                                    width: 16.w,
                                    height: 16.w,
                                    isAssetImage: true,
                                  )
                                : MyImage(
                                    "check_on_3_wihte.png",
                                    width: 16.w,
                                    height: 16.w,
                                    isAssetImage: true,
                                    imageColor: Colours.color353542,
                                    bgColor: Colours.color353542,
                                    radius: 8.w,
                                  )

                            // Icon(
                            //   logic.state.rememberOption.value == "0"
                            //       ? Icons.check_circle
                            //       : Icons.radio_button_unchecked,
                            //   color: logic.state.rememberOption.value == "0"
                            //       ? Colours.white
                            //       : Colours.color9393A5,
                            //   size: 17,
                            // ),
                            ),
                        Text(
                          S.current.composite_video_dialog_tips12,
                          style: TextStyles.medium.copyWith(
                              fontSize: 12.sp, color: Colours.color9393A5),
                        )
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 30.w,
                ),
                SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
              ],
            ),
          );
        });
      },
    );
  }

  Widget recommendedTimeWidget1() {
    return Obx(() {
      return SingleChildScrollView(
        child: Column(
          children: [
            Wrap(
              runSpacing: 15.w,
              spacing: 14.w,
              children:
                  List.generate(logic.state.dateFristList2.length, (index) {
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    Get.back();
                    logic.chooseTime(
                        logic.state.dateFristList2[index]["startTime"].trim(),
                        logic.state.dateFristList2[index]["endTime"].trim(),
                        0);
                  },
                  child: Container(
                    width: 105.w,
                    height: 40.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        border: Border.all(width: 1.w, color: Colours.white),
                        borderRadius: BorderRadius.circular(20.r),
                        color: Colours.color22222D),
                    child: Text(
                      "${logic.state.dateFristList2[index]["startTime"].trim().length >= 16 ? logic.state.dateFristList2[index]["startTime"].trim().substring(10, 16) : logic.state.dateFristList2[index]["startTime"]}～${logic.state.dateFristList2[index]["endTime"].trim().length >= 16 ? logic.state.dateFristList2[index]["endTime"].trim().substring(10, 16) : logic.state.dateFristList2[index]["endTime"]}",
                      textAlign: TextAlign.center,
                      style: TextStyles.medium.copyWith(
                          fontSize: 14.sp,
                          color: Colours.white,
                          fontFamily: "DIN"),
                    ),
                  ),
                );
              }),
            ),
            Center(
              child: Container(
                margin: EdgeInsets.only(top: 20.w, bottom: 20.w),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(30.r)),
                    border: Border.all(width: 1, color: Colours.color2F2F3B)),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        logic.state.todayRecommended.value = true;
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 6.w, horizontal: 18.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(30.r)),
                          gradient: logic.state.todayRecommended.value
                              ? const LinearGradient(
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ],
                                  begin: Alignment.bottomLeft,
                                  end: Alignment.bottomRight,
                                )
                              : null,
                        ),
                        child: Text(
                          S.current.today,
                          style: TextStyles.medium.copyWith(
                              fontSize: 14.sp, fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        logic.state.todayRecommended.value = false;
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 6.w, horizontal: 18.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(30.r)),
                          gradient: logic.state.todayRecommended.value
                              ? null
                              : const LinearGradient(
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ],
                                  begin: Alignment.bottomLeft,
                                  end: Alignment.bottomRight,
                                ),
                        ),
                        child: Text(
                          S.current.yesterday,
                          style: TextStyles.medium.copyWith(
                              fontSize: 14.sp, fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Wrap(
              runSpacing: 15.w,
              spacing: 14.w,
              children: List.generate(logic.state.dateRecommendedList.length,
                  (index) {
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    Get.back();
                    if (!(logic.state.dateRecommendedList[index]
                            .contains("~") &&
                        logic.state.dateRecommendedList[index].contains(":"))) {
                      log("dateRecommendedList1=${logic.state.dateRecommendedList[index]}");
                      return;
                    }
                    log("dateRecommendedList2");
                    DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
                    try {
                      // 解析日期时间字符串
                      DateTime originalTime = DateTime.now();
                      //      DateTime.parse(logic.state.videoDate.value);
                      if (!logic.state.todayRecommended.value) {
                        //昨天减去一天
                        originalTime = originalTime.subtract(Duration(days: 1));
                      }

                      //今天
                      var list =
                          logic.state.dateRecommendedList[index].split("~");

                      var list1 = list[0].split(":");
                      DateTime startDateTime1 = DateTime(
                          originalTime.year,
                          originalTime.month,
                          originalTime.day,
                          int.tryParse(list1[0]) ?? 0,
                          int.tryParse(list1[1]) ?? 0,
                          00);
                      var list2 = list[1].split(":");
                      DateTime endDdteTime2 = DateTime(
                          originalTime.year,
                          originalTime.month,
                          originalTime.day,
                          int.tryParse(list2[0]) ?? 0,
                          int.tryParse(list2[1]) ?? 0,
                          00);
                      logic.chooseTime(dateFormat.format(startDateTime1),
                          dateFormat.format(endDdteTime2), 1);
                    } catch (e) {
                      print('Error parsing date string: $e');
                    }
                  },
                  child: Container(
                    width: 105.w,
                    height: 40.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        border: Border.all(width: 1.w, color: Colours.white),
                        borderRadius: BorderRadius.circular(20.r),
                        color: Colours.color22222D),
                    child: Text(
                      logic.state.dateRecommendedList[index],
                      textAlign: TextAlign.center,
                      style: TextStyles.medium.copyWith(
                          fontSize: 14.sp,
                          color: Colours.white,
                          fontFamily: "DIN"),
                    ),
                  ),
                );
              }),
            ),
            SizedBox(
              height: 30.w,
            )
          ],
        ),
      );
    });
  }

  Widget recommendedTimeWidget2(BuildContext context) {
    return Obx(() {
      return SingleChildScrollView(
        child: SizedBox(
          child: Column(
            children: [
              SizedBox(
                height: 5.w,
              ),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: List.generate(logic.state.datePrecisionList.length,
                      (index) {
                    return GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        logic.state.videoDate1.value =
                            (logic.state.datePrecisionList[index]["date"] ??
                                "");
                        log("videoDate112=${logic.state.videoDate1.value}");
                      },
                      child: Container(
                        width: 70.w,
                        height: 70.w,
                        margin: EdgeInsets.only(
                            right: 10.w, left: index == 0 ? 20.w : 0),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20.r),
                            gradient: LinearGradient(
                                colors: (logic.state.videoDate1.value ==
                                        (logic.state.datePrecisionList[index]
                                                ["date"] ??
                                            ""))
                                    ? [Colours.color7732ED, Colours.colorA555EF]
                                    : [
                                        Colours.color0F0F16,
                                        Colours.color0F0F16
                                      ]),
                            color: Colours.color22222D),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  (logic.state.datePrecisionList[index]["week"] ?? "")
                                              .toString()
                                              .replaceAll("星期", "") ==
                                          "一"
                                      ? S.current.Mon
                                      : (logic.state.datePrecisionList[index]["week"] ?? "")
                                                  .toString()
                                                  .replaceAll("星期", "") ==
                                              "二"
                                          ? S.current.Tue
                                          : (logic.state.datePrecisionList[index]["week"] ?? "")
                                                      .toString()
                                                      .replaceAll("星期", "") ==
                                                  "三"
                                              ? S.current.Wed
                                              : (logic.state.datePrecisionList[index]
                                                                  ["week"] ??
                                                              "")
                                                          .toString()
                                                          .replaceAll(
                                                              "星期", "") ==
                                                      "四"
                                                  ? S.current.Thu
                                                  : (logic.state.datePrecisionList[index]["week"] ?? "")
                                                              .toString()
                                                              .replaceAll("星期", "") ==
                                                          "五"
                                                      ? S.current.Fri
                                                      : (logic.state.datePrecisionList[index]["week"] ?? "").toString().replaceAll("星期", "") == "六"
                                                          ? S.current.Sat
                                                          : (logic.state.datePrecisionList[index]["week"] ?? "").toString().replaceAll("星期", "") == "日"
                                                              ? S.current.Sun
                                                              : "",
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp,
                                      color: logic.state.videoDate1.value ==
                                              (logic.state.datePrecisionList[
                                                      index]["date"] ??
                                                  "")
                                          ? Colours.white
                                          : Colours.color9393A5),
                                ),
                                SizedBox(
                                  height: 10.w,
                                ),
                                Text(
                                  (logic.state.datePrecisionList[index]
                                                  ["datestr"] ??
                                              "") !=
                                          ""
                                      ? logic.state.datePrecisionList[index]
                                          ["datestr"]
                                      : (logic.state.datePrecisionList[index]
                                                          ["date"] ??
                                                      "")
                                                  .length >=
                                              10
                                          ? (logic.state.datePrecisionList[
                                                      index]["date"] ??
                                                  "")
                                              .substring(5, 10)
                                          : logic.state.datePrecisionList[index]
                                                  ["date"] ??
                                              "",
                                  style: TextStyles.regular.copyWith(
                                      color: logic.state.videoDate1.value ==
                                              (logic.state.datePrecisionList[
                                                      index]["date"] ??
                                                  "")
                                          ? Colours.white
                                          : Colours.colorA8A8BC),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
                ),
              ),
              Container(
                padding: EdgeInsets.only(left: 20.w, right: 20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(top: 30.w, bottom: 20.w),
                      child: Text(
                        S.current.Select_time_period,
                        style: TextStyles.regular.copyWith(
                            fontSize: 14.sp, color: Colours.color5C5C6E),
                      ),
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () {
                              logic.showDatePicker(context, 0);
                            },
                            child: Container(
                              width: double.infinity,
                              height: 50.w,
                              margin: EdgeInsets.only(right: 10.w),
                              padding: EdgeInsets.only(left: 20.w, right: 10.w),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(16.r),
                                  color: Colours.color22222D),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: Text(
                                      logic.state.videoStartTime1.value
                                              .isNotEmpty
                                          ? logic.state.videoStartTime1.value
                                          : S.current.Please_select_start_time,
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 14.sp,
                                          color: logic.state.videoStartTime1
                                                  .value.isNotEmpty
                                              ? Colours.white
                                              : Colours.color5C5C6E),
                                      textAlign: TextAlign.left,
                                    ),
                                  ),
                                  const Icon(Icons.arrow_drop_down,
                                      color: Colours.white)
                                ],
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () {
                              logic.showDatePicker(context, 1);
                            },
                            child: Container(
                              width: double.infinity,
                              height: 50.w,
                              padding: EdgeInsets.only(left: 20.w, right: 10.w),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(16.r),
                                  color: Colours.color22222D),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: Text(
                                      logic.state.videoEndTime1.value.isNotEmpty
                                          ? logic.state.videoEndTime1.value
                                          : S.current.Please_select_end_time,
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 14.sp,
                                          color: logic.state.videoEndTime1.value
                                                  .isNotEmpty
                                              ? Colours.white
                                              : Colours.color5C5C6E),
                                      textAlign: TextAlign.left,
                                    ),
                                  ),
                                  const Icon(Icons.arrow_drop_down,
                                      color: Colours.white)
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 15.w, bottom: 30.w),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          WxAssets.images.tips.image(height: 12.w, width: 12.w),
                          SizedBox(
                            width: 2.w,
                          ),
                          Expanded(
                            child: Text(
                              S.current.select_time_tips,
                              style: TextStyles.regular.copyWith(
                                  fontSize: 12.sp, color: Colours.color5C5C6E),
                            ),
                          ),
                        ],
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        logic.sureTimeSearch(context);
                      },
                      child: Container(
                        height: 46.w,
                        width: double.infinity,
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(
                            left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                        decoration: BoxDecoration(
                          color: Colours.color282735,
                          borderRadius: BorderRadius.all(Radius.circular(28.r)),
                          gradient: const LinearGradient(
                            colors: [Colours.color7732ED, Colours.colorA555EF],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                        ),
                        child: Text(
                          S.current.sure,
                          style: TextStyles.semiBold14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  //选中的素材
  void showLibraryDialog(
    BuildContext context,
  ) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            height: 520.w,
            color: Colours.color0F0F16,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4.w,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                SizedBox(
                  width: double.infinity,
                  height: 50.w,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      GestureDetector(
                        onTap: () {
                          logic.state.toggleSelectDeleteLibrary();
                        },
                        child: Container(
                          height: 50.w,
                          alignment: Alignment.center,
                          width: 70.w,
                          padding: EdgeInsets.symmetric(
                            horizontal: 15.w,
                          ),
                          child: Text(
                            S.current.delete,
                            style: TextStyles.regular.copyWith(
                                color: Colours.cFF3F3F,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                      const Spacer(),
                      GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          log("isAllSelectedLibrary=${!logic.state.isAllSelectedLibrary.value}");
                          logic.state.toggleSelectAllLibrary(
                              !logic.state.isAllSelectedLibrary.value);
                        },
                        child: Container(
                          height: 50.w,
                          alignment: Alignment.center,
                          padding: EdgeInsets.symmetric(
                              horizontal: 15.w, vertical: 10.w),
                          child: Row(
                            children: [
                              logic.state.isAllSelectedLibrary.value &&
                                      logic.state.checkedList3.isNotEmpty
                                  ? WxAssets.images.checkOn3
                                      .image(width: 16.w, height: 16.w)
                                  : WxAssets.images.checkOn3Wihte
                                      .image(width: 16.w, height: 16.w),
                              SizedBox(
                                width: 5.w,
                              ),
                              Text(
                                logic.state.isAllSelectedLibrary.value &&
                                        logic.state.checkedList3.isNotEmpty
                                    ? S.current.Deselect_all
                                    : S.current.select_all,
                                style: TextStyles.regular.copyWith(
                                    color: Colours.white,
                                    fontWeight: FontWeight.w600),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                logic.state.checkedList3.isEmpty
                    ? myNoDataView(context,
                        msg: S.current.no_goal,
                        imagewidget: WxAssets.images.noGoal
                            .image(width: 100.w, height: 84.w),
                        margin: EdgeInsets.only(top: 150.w))
                    : Expanded(
                        child: GridView.builder(
                            scrollDirection: Axis.vertical,
                            // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                            shrinkWrap: true,
                            physics: const AlwaysScrollableScrollPhysics(), //
                            //   const NeverScrollableScrollPhysics(),
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3,
                              crossAxisSpacing: 15,
                              mainAxisSpacing: 15,
                              childAspectRatio: 105 / 59,
                            ),
                            padding: EdgeInsets.only(
                                bottom: 30.w, left: 15.w, right: 15.w),
                            itemCount: logic.state.checkedList3.length,
                            itemBuilder: (context, position) {
                              return Obx(() {
                                return GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () async {
                                    if (position ==
                                        logic.state.indexVideoLibrary.value) {
                                      logic.state.checkedList3[position].isCheck
                                              .value =
                                          !logic.state.checkedList3[position]
                                              .isCheck.value;
                                      logic.state.checkedList3.refresh();
                                    } else {
                                      logic.changeVideoIndexLibrary(
                                          logic.state.checkedList3[position],
                                          position);
                                    }
                                  },
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: position ==
                                              logic
                                                  .state.indexVideoLibrary.value
                                          ? Colours.color291A3B
                                          : Colours.color191921,
                                      borderRadius: BorderRadius.circular(8.r),
                                      image: (logic.state.checkedList3[position]
                                              .isCheck.value)
                                          ? const DecorationImage(
                                              image: AssetImage(
                                                  "assets/images/goal_bg3.png"),
                                              fit: BoxFit.fill)
                                          : const DecorationImage(
                                              image: AssetImage(
                                                  "assets/images/goal_bg4.png"),
                                              fit: BoxFit.fill),
                                    ),
                                    child: Stack(
                                      children: [
                                        Positioned(
                                          top: 0.w,
                                          right: 0.w,
                                          child: GestureDetector(
                                            behavior:
                                                HitTestBehavior.translucent,
                                            onTap: () async {
                                              logic.state.checkedList3[position]
                                                      .isCheck.value =
                                                  !logic
                                                      .state
                                                      .checkedList3[position]
                                                      .isCheck
                                                      .value;
                                              logic.state.checkedList3
                                                  .refresh();
                                            },
                                            child: Padding(
                                              padding: EdgeInsets.all(8.w),
                                              child: (logic
                                                      .state
                                                      .checkedList3[position]
                                                      .isCheck
                                                      .value)
                                                  ? WxAssets.images
                                                      .checkWhiteBorderYes
                                                      .image(
                                                          height: 14.w,
                                                          width: 14.w)
                                                  : WxAssets
                                                      .images.checkWhiteBorder
                                                      .image(
                                                          height: 14.w,
                                                          width: 14.w),
                                            ),
                                          ),
                                        ),
                                        Positioned(
                                            bottom: 10.w,
                                            child: Container(
                                              width: 103.w,
                                              padding: EdgeInsets.only(
                                                  left: 10.w, right: 10.w),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  if ( //logic.videostate.value == 1 &&
                                                      position ==
                                                          logic
                                                              .state
                                                              .indexVideoLibrary
                                                              .value)
                                                    Container(
                                                      height: 10.w,
                                                      width: 10.w,
                                                      alignment:
                                                          Alignment.center,
                                                      child:
                                                          const LoadingIndicator(
                                                        pathBackgroundColor:
                                                            Colors.black26,
                                                        indicatorType: Indicator
                                                            .lineScaleParty,
                                                        colors: [Colours.white],
                                                      ),
                                                    ),
                                                  Expanded(
                                                    child: Text(
                                                      logic
                                                          .state
                                                          .checkedList3[
                                                              position]
                                                          .shotTime, // dataList[position].id,//    dataList[position].videoTime,
                                                      textAlign:
                                                          TextAlign.right,
                                                      style: TextStyles.medium
                                                          .copyWith(
                                                              fontSize: 12.sp,
                                                              color: Colours
                                                                  .colorA8A8BC,
                                                              fontFamily:
                                                                  "DIN"),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ))
                                      ],
                                    ),
                                  ),
                                );
                              });
                            }),
                      ),
                if (logic.state.checkedList3.isNotEmpty)
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () async {
                      var list2 = logic.state.checkedList3.where((v) {
                        return v.isCheck.value == true;
                      }).toList();
                      if (list2.isEmpty || list2.length <= 1) {
                        WxLoading.showToast("请先选择两个视频");
                        return;
                      }
                      if (list2.length > 20) {
                        WxLoading.showToast("合成视频最多只能选择20个进球片段");
                        return;
                      }
                      AppPage.back();
                      //去合成
                      showChooseGoalDialog(context, list2);
                    },
                    child: Container(
                      height: 46.w,
                      width: double.infinity,
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(
                          left: 15.w, right: 15.w, bottom: 34.w, top: 10.w),
                      decoration: BoxDecoration(
                        color: Colours.color282735,
                        borderRadius: BorderRadius.all(Radius.circular(28.r)),
                        gradient: const LinearGradient(
                          colors: [Colours.color7732ED, Colours.colorA555EF],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: Text(
                        S.current.One_click_make_piece,
                        style: TextStyles.semiBold14,
                      ),
                    ),
                  ),
              ],
            ),
          );
        });
      },
    );
  }
}
