import 'dart:convert';
import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';

class ShootGoalLogicState {
  TextEditingController codeController = TextEditingController();

  var compositeOption2 = ["0", "0", "0", "0"].obs; //视频效果与个性化 0选中  1选中 多选
  var rememberOption = "0".obs; //记住我的选择
  var deleteOption = true.obs; //记住我的选择
  var isShare = true.obs; //是否可以分享 0不能 1可以
  var indexVideoId = "".obs;
  var indexVideoLibrary = 9999.obs; //选择视频下标

  // 响应式列表
  final RxList<ShotRecordModel> dataList1 = <ShotRecordModel>[].obs;

  // 存储选中项目的唯一列表（自动去重）
  final RxList<ShotRecordModel> checkedList = <ShotRecordModel>[].obs;
  RxList<ShotRecordModel> checkedList3 = <ShotRecordModel>[].obs;
  // 计算选中总数（直接使用checkedList长度）
  RxInt get totalCheckedCount => checkedList.length.obs;

// 是否全部选中（计算属性）
  RxBool get isAllSelectedLibrary {
    final selectedCount =
        checkedList3.where((item) => item.isCheck == "1").length;
    return (selectedCount == checkedList3.length).obs;
  }

  // 全选/取消全选
  void toggleSelectAllLibrary(bool select) {
    for (var item in checkedList3) {
      item.isCheck = select ? "1" : "0";
    }
    checkedList3.refresh();
  }

  void toggleSelectDeleteLibrary() {
    // 1. 获取checkedList2中所有选中的ID
    final checkedIds = checkedList3
        .where((item) => item.isCheck == "1")
        .map((item) => item.eventId)
        .toSet();
    final checkedIds2 = dataList1
        .where((item) => item.isCheck == "1")
        .map((item) => item.eventId)
        .toSet();
    log("toggleSelectDeleteLibrary0=${checkedIds2.join(",")}");
    checkedList3.removeWhere((item) => checkedIds.contains(item.eventId));
    // 2. 从checkedList中移除匹配项
    checkedList.removeWhere((item) => checkedIds.contains(item.eventId));
    for (final item in dataList1) {
      if (checkedIds.contains(item.eventId)) {
        log("toggleSelectDeleteLibrary01=${item.eventId}");
        item.isCheck = "0"; // 同步选中状态
      }
    }

    dataList1.refresh();
    log("toggleSelectDeleteLibrary1=${checkedIds.join(",")}");
    log("toggleSelectDeleteLibrary2=${jsonEncode(dataList1)}");
    log("toggleSelectDeleteLibrary3=${jsonEncode(checkedList3)}");
    log("toggleSelectDeleteLibrary4=${jsonEncode(checkedList)}");
  }
  // // 加载数据并同步选中状态
  // void loadDataWithCheckedState(List<VenueGoalModel> newData) {
  //   // 1. 保存当前所有选中ID
  //   final checkedIds = checkedList.map((item) => item.id).toSet();

  //   // 2. 更新数据列表并恢复选中状态
  //   dataList1.assignAll(newData.map((newItem) {
  //     // 如果新数据的ID在已选中列表中，恢复选中状态
  //     if (checkedIds.contains(newItem.id)) {
  //       return newItem.copyWith(isCheck: true);
  //     }
  //     return newItem;
  //   }));

  //   // 3. 更新checkedList（移除不存在的数据）
  //   checkedList.removeWhere((checkedItem) =>
  //       !newData.any((newItem) => newItem.id == checkedItem.id));
  // }

  // // 切换选中状态
  // void toggleCheck(VenueGoalModel item) {
  //   if (checkedList.any((e) => e.id == item.id)) {
  //     checkedList.removeWhere((e) => e.id == item.id);
  //     item.isCheck.value = false;
  //   } else {
  //     checkedList.add(item);
  //     item.isCheck.value = true;
  //   }
  // }
}
