// ignore_for_file: avoid_print, prefer_final_fields

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab/upload/CosUploadServiceSingle.dart';
import 'package:shoot_z/pages/tab3Create/shoot_composite/shoot_goal/shoot_goal_state.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/exclusive_dialog.dart';
import 'package:shoot_z/widgets/merge/video_editor_service.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'package:ui_packages/ui_packages.dart';

class ShootGoalLogic extends GetxController
    with WidgetsBindingObserver, GetTickerProviderStateMixin {
  final ShootGoalLogicState state = ShootGoalLogicState();
  TextEditingController nickNameController = TextEditingController();

  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  late final VideoController videoController;

  var isFrist = true.obs;
  // var videostate = 0.obs; //0初始 1播放  2暂停  3完成
  var todayDate = "";
  var isFullScreen = false.obs;
  var type = "1".obs; //1单人  2多人

  var trainingId = "".obs;
  var venueId = 0.obs;
  var index3 = 0;

  late final CosUploadServiceSingle _uploadService;
  var _progress = 0.0.obs;
  var _uploadProgress = 0.0.obs;
  bool _isProcessing = false;
  String? _outputPath;
  var total2 = 0.obs;
  var index2 = 0.obs;
  var uploadImg = ''.obs;
  var uploadVideo = ''.obs;
  var a = 0;
  File? selectedWatermark;
  File? selectedBGM;
  var isProcessing = false.obs;
  // 切片预览相关变量 - 移除这些变量，让对话框自己管理
  // 编辑选项
  bool enableSlowMotion = true;
  double slowMotionFactor = 2.0; // 2倍慢放（第2-4秒慢动作）
  double watermarkOpacity = 0.8;
  String watermarkPosition = 'top-right';
  double bgmVolume = 0.5; //背景音里
  late final VideoEditorService _editorService;
  @override
  Future<void> onInit() async {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    if (Get.arguments != null && Get.arguments.containsKey('trainingId')) {
      trainingId.value = Get.arguments['trainingId'];
    }
    if (Get.arguments != null && Get.arguments.containsKey('venueId')) {
      venueId.value = Get.arguments['venueId'];

      videoController =
          VideoController(pushDisposeOnAndroid: true, isNetwork: false);
    }

    if (Get.arguments != null && Get.arguments.containsKey('type')) {
      type.value = Get.arguments['type'];
    }

    state.rememberOption.value =
        await WxStorage.instance.getString("rememberOptionMerge") ?? "0";
    if (state.rememberOption.value == "0") {
      state.compositeOption2[1] =
          (await WxStorage.instance.getInt("compositeOptionMerge1") ?? 0) == 1
              ? "1"
              : "0"; //慢动作视频效果与个性化 0选中  1选中 多选
      state.compositeOption2[2] =
          (await WxStorage.instance.getInt("compositeOptionMerge2") ?? 0) == 1
              ? "1"
              : "0"; //保留原生 0选中  1选中 多选
      state.compositeOption2[3] =
          (await WxStorage.instance.getInt("compositeOptionMerge3") ?? 0) == 1
              ? "1"
              : "0"; //去除水印 0选中  1选中 多选

      state.isShare.value =
          (await WxStorage.instance.getInt("isShareOptionMerge") ?? 0) == 1
              ? true
              : false;
    }
    _editorService = VideoEditorService(
        onProgressBack: (progress, errorMsg, {bool isError = false}) {
      a++;
      if (a % 3 == 0 || progress >= 1) {
        _progress.value = progress > 1
            ? 1
            : _progress.value > progress
                ? _progress.value
                : progress;
        index2.value = (progress * total2.value).toInt();
        log('_mergeVideoszzz=$progress-${_progress.value}');
      }
      if (isError) {
        checkDialog();
        _editorService.cancelAll();
      }
      //合成进度回调
      log("_mergeVideoszzz=progress:${progress} errorMsg:${errorMsg} isError:${isError}");
    });
    // _merger = VideoMergeTool(
    //     onProgress: (p, index, total, msg, {bool isError = false}) {
    //   a++;
    //   if (a % 3 == 0 || p >= 1) {
    //     _progress.value = p > 1
    //         ? 1
    //         : _progress.value > p
    //             ? _progress.value
    //             : p;
    //     total2.value = total ~/ 3;
    //     index2.value = index ~/ 3;
    //     log('VideoMergePagemergedPath133301=$p-${_progress.value}');
    //   }
    //   if (isError) {
    //     log('VideoMergePagemergedPath133301=${msg}');
    //     //    Get.snackbar('错误', msg);
    //   }
    // }, onCompleted: (String url, String coverPath) {
    //   log('VideoMergePagemergedPath13330 ${state.compositeOption2[1] != "1"} onCompleted=$url');
    //   _merger.cancelAll();
    //   checkDialog();
    //   if (url != "") {
    //     _uploadFile(url, coverPath);
    //   }
    // });
    _uploadService = CosUploadServiceSingle(onProgress: (p) {
      _uploadProgress.value = p > 1 ? 1 : p;
      log('CosUploadServiceSingle1=$p');
    }, onCompleted: (String url, int type) {
      log('CosUploadServiceSingle2=  type=$type onCompleted=$url');
      if (type == 1) {
        uploadVideo.value = url;
      } else if (type == 2) {
        uploadImg.value = url;
      }
      if (uploadImg.value != "" && uploadVideo.value != "") {
        WxLoading.dismiss();
        getUpDateMergeVideos(
            uploadVideo.value, uploadImg.value == "1" ? "" : uploadImg.value);
      }
    }, onFailure: (String error, int type) {
      log('CosUploadServiceSingle3= error=$error');
      if (type == 2) {
        uploadImg.value = "1";
      } else if (type == 1) {
        WxLoading.showToast("视频上传失败，请检查网络后重试");
      }
      WxLoading.dismiss();
    });
  }

  Future<void> _uploadFile(String filePath2, String coverPath) async {
    WxLoading.show(status: "上传视频中...");
    // 生成任务ID
    final taskId =
        '${filePath2.hashCode}_${DateTime.now().millisecondsSinceEpoch}';
    // 准备上传数据
    final record = ShotRecordModel(
        filePath: filePath2,
        trainingId: trainingId.value,
        eventId: taskId,
        playerImagePath: coverPath);
    // 添加上传任务
    _uploadService.addUploadTask(record, taskId);
    final taskId2 =
        '${coverPath.hashCode}_${DateTime.now().millisecondsSinceEpoch}';
    // 准备上传数据
    final record2 = ShotRecordModel(
        filePath: filePath2,
        trainingId: trainingId.value,
        eventId: taskId,
        playerImagePath: coverPath);
    // 添加上传任务
    _uploadService.addUploadTask(record2, taskId2);
  }

  //合成视频
  getUpDateMergeVideos(String filePath2, String coverPath) async {
    if (state.rememberOption.value == "0") {
      await WxStorage.instance.setString(
          "rememberOptionMerge", state.rememberOption.value); //记住我的选择 0记住  1不记住
      await WxStorage.instance.setInt(
          "isShareOptionMerge", state.isShare.value ? 1 : 0); //是否 共享到场地展示为精彩视频
      await WxStorage.instance.setInt("compositeOptionMerge1",
          (state.compositeOption2[1] != "1") ? 0 : 1); //慢动作视频效果与个性化 0选中  1选中 多选
      await WxStorage.instance.setInt("compositeOptionMerge2",
          (state.compositeOption2[2] != "1") ? 0 : 1); //保留原声 0选中  1选中 多选
      await WxStorage.instance.setInt("compositeOptionMerge3",
          (state.compositeOption2[3] != "1") ? 0 : 1); //去除水印 0选中  1选中 多选
    }

    // compositeOption4 = 0.obs; //移除所选的球 0是  1否
    // var param = {
    //   "VideoIdList": [],
    //   "coverPath":
    //       "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/mobile/videos/293589/1314/mergeCoverImg/1757562086280.jpg",
    //   "effects": [1],
    //   "share": false,
    //   "title": "吧",
    //   "trainingId": "1314",
    //   "venueId": "0",
    //   "videoPath":
    //       "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/mobile/videos/293589/1314/mergevideo/1757562068551.mp4"
    // };
    List<int> effectList = [];
    for (int i = 0; i < 4; i++) {
      if (state.compositeOption2[i] == "1") {
        effectList.add(i + 1);
      }
    }
    var param = {
      "VideoIdList": [],
      "coverPath": coverPath,
      "effects": effectList,
      "share": state.isShare.value,
      "title": nickNameController.text.trim(), //合成名称
      "trainingId": trainingId.value,
      "venueId": venueId.value.toString(),
      "videoPath": filePath2
    };
    WxLoading.show();
    log("paramcompositeOption4=${jsonEncode(param)}");
    final res = await Api().post(ApiUrl.shootingAchievements, data: param);
    WxLoading.dismiss();
    log("message1");
    if (res.isSuccessful()) {
      nickNameController.text = "";
      uploadImg.value = "";
      uploadVideo.value = "";
      total2.value = 0;
      index2.value = 0;
      //  getMergeDialog(res.data["points"].toString(), isCheckAll);
      getMergeDialog("0", false);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //合成视频弹窗
  Future<void> getMergeDialog(var points, bool isCheckAll) async {
    getMergeDialog2(
        points == "0" || points == ""
            ? ""
            : S.current.merge_videos_dialog_tips8(points),
        S.current.sure, () {
      AppPage.back();
    });
  }

  void getMergeDialog2(
    String titltPoint,
    String sureText,
    //String sureText2,
    void Function()? onPressed,
    // void Function()? onPressed2
  ) {
    Get.dialog(
      Padding(
        padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
        child: Material(
          type: MaterialType.transparency,
          color: Colors.transparent,
          child: Center(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(
                    color: Colors.transparent,
                    child: Column(
                      children: <Widget>[
                        //upload_top_img
                        SizedBox(
                          height: 20.w,
                        ),
                        Container(
                          alignment: Alignment.topLeft,
                          constraints: BoxConstraints(
                            // maxHeight: titltPoint != "" ? 245.w : 185.w,
                            minHeight: 145.w,
                          ),
                          decoration: BoxDecoration(
                            color: Colours.color191921,
                            borderRadius: BorderRadius.all(
                              Radius.circular(25.r),
                            ),
                          ),
                          padding: EdgeInsets.symmetric(
                              vertical: 20.w, horizontal: 25.w),
                          width: double.infinity,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              Text(S.current.hint,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 18.sp,
                                    color: Colours.white,
                                    fontWeight: AppFontWeight.medium(),
                                    height: 1,
                                  )),
                              SizedBox(
                                height: 20.w,
                              ),
                              if (titltPoint != "")
                                Text(titltPoint,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 18.sp,
                                      color: Colours.colorA44EFF,
                                      fontWeight: AppFontWeight.regular(),
                                      height: 1,
                                    )),
                              if (titltPoint != "")
                                SizedBox(
                                  height: 25.w,
                                ),
                              RichText(
                                textAlign: TextAlign.center,
                                text: TextSpan(
                                    text: S.current.merge_videos_dialog_tips11,
                                    style: TextStyle(
                                        color: Colours.color9393A5,
                                        fontSize: 14.sp,
                                        height: 2,
                                        fontWeight: FontWeight.normal),
                                    children: <InlineSpan>[
                                      TextSpan(
                                        text:
                                            S.current.merge_videos_dialog_tips1,
                                        style: TextStyle(
                                            color: Colours.color9393A5,
                                            fontSize: 14.sp,
                                            height: 2,
                                            fontWeight: FontWeight.normal),
                                      ),
                                      TextSpan(
                                          text: " ${S.current.my_highlights} ",
                                          style: TextStyle(
                                              color: Colours.white,
                                              fontSize: 14.sp,
                                              height: 2,
                                              fontWeight: FontWeight.normal)),
                                      TextSpan(
                                          text: S.current
                                              .merge_videos_dialog_tips31,
                                          style: TextStyle(
                                              color: Colours.color9393A5,
                                              height: 2,
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.normal)),
                                    ]),
                              ),
                              SizedBox(
                                height: 30.w,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () async {
                                      // AppPage.back(
                                      //     page: Routes.selfieShotInfoPage,
                                      //     result: "1");
                                      //我的集锦
                                      AppPage.back();
                                      await Future.delayed(
                                          const Duration(milliseconds: 100));
                                      Future.delayed(
                                              const Duration(milliseconds: 700))
                                          .then((onValue) {
                                        BusUtils.instance.fire(EventAction(
                                            key: EventBusKey.changeMyVideo));
                                      });
                                      AppPage.to(
                                          Routes.careerHighlightsHomePage,
                                          closePreviousPage: true,
                                          arguments: {
                                            "type": 1,
                                          });
                                    },
                                    child: Container(
                                      height: 46.w,
                                      width: 125.w,
                                      alignment: Alignment.center,
                                      margin: EdgeInsets.only(
                                        top: 15.w,
                                      ),
                                      padding: EdgeInsets.only(
                                          left: 5.w,
                                          right: 5.w,
                                          top: 3.w,
                                          bottom: 3.w),
                                      decoration: BoxDecoration(
                                        color: Colours.color22222D,
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(28.r)),
                                      ),
                                      child: Text(
                                        S.current.merge_videos_dialog_tips5,
                                        style: TextStyles.semiBold
                                            .copyWith(fontSize: 14.sp),
                                      ),
                                    ),
                                  ),
                                  GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () {
                                      AppPage.back();
                                      //
                                      // AppPage.back(
                                      //     page: Routes.selfieShotInfoPage);
                                    },
                                    child: Container(
                                      height: 46.w,
                                      width: 125.w,
                                      alignment: Alignment.center,
                                      margin: EdgeInsets.only(
                                        top: 15.w,
                                      ),
                                      padding: EdgeInsets.only(
                                          left: 5.w,
                                          right: 5.w,
                                          top: 3.w,
                                          bottom: 3.w),
                                      decoration: BoxDecoration(
                                        color: Colours.color282735,
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(28.r)),
                                        gradient: const LinearGradient(
                                          colors: [
                                            Colours.color7732ED,
                                            Colours.colorA555EF
                                          ],
                                          begin: Alignment.bottomLeft,
                                          end: Alignment.bottomRight,
                                        ),
                                      ),
                                      child: Text(
                                        S.current.merge_videos_continue_tips6,
                                        style: TextStyles.semiBold
                                            .copyWith(fontSize: 14.sp),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                            ],
                          ),
                        ),

                        SizedBox(
                          height: 25.w,
                        ),
                        // GestureDetector(
                        //   behavior: HitTestBehavior.translucent,
                        //   onTap: () {
                        //     AppPage.back();
                        //   },
                        //   child: WxAssets.images.icCloseDialog
                        //       .image(width: 30.w, height: 30.w),
                        // ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      barrierColor: Colors.black.withOpacity(0.85),
    );
  }

  @override
  void onReady() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 这里的代码将在当前帧结束后执行
      refresh();
    });
    getdataList();
    super.onReady();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {}

  reloadVideo(String videoUrl) async {
    if (videoUrl.isEmpty) {
      log("betterPlayer-message11=$videoUrl");
      return;
    }
    videoController.setData(
      videoPath: videoUrl,
    );
  }

  getdataList() async {
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final selfieShotDao = database.selfieShotDao;
    List<ShotRecordModel> filteredNumbers = await selfieShotDao.findAllShot(
        trainingId.value,
        UserManager.instance.userInfo.value?.userId ?? "",
        type.value);
    // 1. 保存当前所有选中ID
    final checkedIds = state.checkedList.map((item) => item.eventId).toSet();
    // 2. 更新数据列表并恢复选中状态
    state.dataList1.assignAll(filteredNumbers.map((newItem) {
      // 如果新数据的ID在已选中列表中，恢复选中状态
      if (checkedIds.contains(newItem.eventId)) {
        return newItem.copyWith(isCheck: "1");
      }
      return newItem.copyWith(isCheck: "0");
    }));
    // //TODO 测试使用 全部选中
    // state.checkedList.assignAll(filteredNumbers);
    if (state.dataList1.isNotEmpty) {
      changeVideoIndex(
        0,
      );
    } else {
      state.indexVideoId.value = "";
      videoController.setData(videoPath: '');
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void onClose() {
    videoController.pause();
    videoController.videoPlayerController?.setVolume(0);
    videoController.videoPlayerController?.seekTo(const Duration(seconds: 0));
    http.Client().close(); // 如果使用http包
    rootBundle.evict('');
    WidgetsBinding.instance.removeObserver(this);
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
    // 强制GC（针对视频编解码器泄漏）
    SystemChannels.platform.invokeMethod('Memory.forceGC');
    _editorService.cancelAll();
    super.onClose();
  }

  @override
  void dispose() {
    // 停止所有动画
    videoController.dispose();
    // 释放视频编解码器
    SystemChannels.platform.invokeMethod('VideoPlayer.disposeAll');

    super.dispose();
  }

//改变选择视频index
  void changeVideoIndex(
    int index,
  ) {
    state.indexVideoId.value = state.dataList1[index].eventId ?? "";
    if (state.indexVideoId.value != "") {
      reloadVideo(state.dataList1[index].filePath ?? "");
    }
  }

//新增数据和删除数据
  Future<void> checkVideo(int index, //0 半场1   1半场2
      {bool isToNext = false}) async {
    var item = state.dataList1[index];
    if (state.checkedList.any((e) => e.eventId == item.eventId)) {
      // 已存在则移除
      state.checkedList.removeWhere((e) => e.eventId == item.eventId);
      state.dataList1[index].isCheck = "0";
    } else {
      // 不存在则添加
      state.checkedList.add(item);
      state.dataList1[index].isCheck = "1";
    }

    log("checkVideo2-1");
    if (state.dataList1[index].isCheck == "1" && isToNext) {
      if ((index + 1) < state.dataList1.length) {
        changeVideoIndex(index + 1);
      }
    }
    state.dataList1.refresh();
  }

//改变选择视频index
  void changeVideoIndexLibrary(ShotRecordModel shotRecordModel, int index) {
    state.indexVideoLibrary.value = index;
    reloadVideo(shotRecordModel.filePath ?? "");
  }

//合成视频
  Future<void> compositeVideo(List<ShotRecordModel> list2) async {
    if (list2.isEmpty) {
      WxLoading.showToast(S.current.merge_videos_tips1);
      return;
    }
    if (list2.length > 50) {
      WxLoading.showToast(S.current.merge_videos_tips3);
      return;
    }
    if (nickNameController.text.trim().isEmpty) {
      WxLoading.showToast(S.current.video_name);
      return;
    }

    index3 = 0;
    //List<ShotRecordModel> videos = [];
    // for (var item in list2) {
    //   index3++;
    //   if ((item.filePath != "")) {
    //     final item1 = item.copyWith(videoLoadOK: "1", imgLoadOK: "$index3-1");
    //     final item2 = item.copyWith(videoLoadOK: "2", imgLoadOK: "$index3-2");
    //     final item3 = item.copyWith(videoLoadOK: "3", imgLoadOK: "$index3-3");
    //     // 严格按顺序插入
    //     videos.addAll([item1, item2, item3]);
    //   }
    // }
    log('compositeVideo1');

    final sortedList = List<ShotRecordModel>.from(list2)
      ..sort((a, b) {
        final numA = int.tryParse(a.eventId ?? "") ?? 0;
        final numB = int.tryParse(b.eventId ?? "") ?? 0;
        return numA.compareTo(numB);
      });
    startAll(sortedList);
  }

// 替换您的startAll方法
  Future<void> startAll(
    List<ShotRecordModel> list,
  ) async {
    log('compositeVideo2');
    if (_isProcessing) {
      Get.snackbar('警告', '已有任务进行中');
      return;
    }
    try {
      log('compositeVideo3=最终合成');
      _isProcessing = true;
      //  _outputPath =
      showUpdateDialog2();
      // if (state.compositeOption2[1] != "1") {
      //   //无慢放
      //   await _merger.startAllNoSpecial(list);
      // } else {
      //   await _merger.startAll(list);
      // }

      _processVideo(list);
      log('compositeVideo4=合成视频路径: $_outputPath');
    } finally {
      _isProcessing = false;
      log('compositeVideo5=合成视频路径:');
    }
  }

  Future<void> _processVideo(List<ShotRecordModel> dataList2) async {
    print('=== UI: _processVideo 开始 ===');
    enableSlowMotion = state.compositeOption2[1] == "1"; //进球慢放
    var removeOriginalAudio = state.compositeOption2[2] != "1"; //保留原声
    var isWaterLogo = state.compositeOption2[3] == "1"; //去除水印
    if (dataList2.isEmpty) {
      print('UI: 错误 - 没有选择视频文件');
      Get.snackbar('警告', '请先选择视频文件');
      return;
    }
    if (!isWaterLogo) {
      try {
        File logoFile = await Utils.assetToFile(
            'assets/images/water_logo.png', 'water_logo.png');
        print('_processVideo文件已创建：${logoFile.path}');
        print('文件大小：${await logoFile.length()} 字节');
        selectedWatermark = logoFile;
      } catch (e) {
        print('转换失败：$e');
      }
    }
    List<File> selectedVideos = [];

    print('UI: 选择的视频数量: ${dataList2.length}');
    for (int i = 0; i < dataList2.length; i++) {
      final file = File(dataList2[i].filePath ?? "");
      selectedVideos.add(file);
      print('UI: 视频 $i: ${file.path}');
      log("UI: 视频 $i 存在:=${file.existsSync()}");
    }

    print('UI: 处理参数:');
    print('UI: - 启用慢动作: $enableSlowMotion');
    print('UI: - 水印文件: ${selectedWatermark?.path ?? 'null'}');
    print('UI: - BGM文件: ${selectedBGM?.path ?? 'null'}');
    print('UI: - 水印透明度: $watermarkOpacity');
    print('UI: - 水印位置: $watermarkPosition');
    print('UI: - BGM音量: $bgmVolume');

    isProcessing.value = true;
    total2.value = selectedVideos.length;
    try {
      Map? resultVideoPath = await _editorService.processVideos(
        videoFiles: selectedVideos, //合成视频
        watermarkFile: selectedWatermark, //水印文件
        bgmFile: selectedBGM, //背景文件
        enableSlowMotion: enableSlowMotion, //是否慢放
        slowMotionFactor: slowMotionFactor, //放慢倍数
        watermarkOpacity: watermarkOpacity, //水印透明度
        watermarkPosition: watermarkPosition, //水印位置
        removeOriginalAudio: removeOriginalAudio,
        bgmVolume: bgmVolume, //背景音量
      );
      print('UI: processVideos 完成，结果: $resultVideoPath');
      if (resultVideoPath?["video"] != null) {
        print('UI: 处理成功，输出文件: $resultVideoPath');
        _editorService.cancelAll();
        checkDialog();
        print('UI: 处理成功，输出文件2: $resultVideoPath    result:$resultVideoPath');
        _uploadFile(resultVideoPath?["video"], resultVideoPath?["thumbnail"]);
      } else {
        print('UI: 处理失败，result 为 null');
        Get.snackbar('视频处理失败', "");
      }
    } catch (e) {
      print('UI: 视频处理失败处理异常: $e');
      print('UI: 视频处理失败异常堆栈: ${StackTrace.current}');
      Get.snackbar('视频处理失败', "$e");
    } finally {
      isProcessing.value = false;
      print('UI: _processVideo 结束');
    }
  }

  void getDownLoad() {
    var list = state.dataList1.where((value) {
      return value.isCheck == "1";
    }).toList();
    if (list.isEmpty) {
      WxLoading.showToast("请选择至少一个视频");
      return;
    }
    Utils.localDownloadAndSaveToPhotoAlbum(list);
  }

// 基础文件存在检查
  static Future<bool> fileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      print('检查文件存在时出错: $e');
      return false;
    }
  }

// 显示弹窗（自动避免重复）
  void showUpdateDialog2() {
    ExclusiveDialog.show(
        context: Get.context!,
        builder: (_) => Padding(
              padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
              child: Material(
                type: MaterialType.transparency,
                color: Colors.transparent,
                child: Center(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Container(
                          color: Colors.transparent,
                          child: Column(
                            children: <Widget>[
                              Container(
                                alignment: Alignment.topLeft,
                                height: 204.w,
                                decoration: BoxDecoration(
                                  color: Colours.color191921,
                                  borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(25.r),
                                    bottomRight: Radius.circular(25.r),
                                  ),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 18, vertical: 2),
                                width: double.infinity,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: <Widget>[
                                    const SizedBox(
                                      height: 30,
                                    ),
                                    Center(
                                      child: Text(
                                        ("提示"), //"1.修复了一些BUG;\n2.更新部分内容",//
                                        //  "1.修复了一些BUG;\n2.更新部分内容" * 20,
                                        style: TextStyles.regular.copyWith(
                                            color: Colours.white,
                                            fontSize: 18.sp),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Obx(() {
                                      return LinearPercentIndicator(
                                        lineHeight: 4,
                                        linearGradient: const LinearGradient(
                                            colors: [
                                              Colours.color7732ED,
                                              Colours.colorA555EF
                                            ]),
                                        percent: _progress.value, //??
                                        //     controller.progressNotifier
                                        //         .value, //uploadController.progress, //
                                        backgroundColor: Colours.color000000,
                                        //    progressColor: Colours.colorA555EF, // 已完成的任务为绿色，否则根据进度设置颜色
                                        barRadius: const Radius.circular(3),
                                        animation: true,
                                        animateFromLastPercent: true,
                                        animationDuration: 500,
                                      );
                                    }),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Obx(() {
                                      return RichText(
                                        textAlign: TextAlign.left,
                                        text: TextSpan(
                                            text: "合成视频中，请稍等",
                                            style: TextStyle(
                                                color: Colours.colorA8A8BC,
                                                fontSize: 14.sp,
                                                height: 1,
                                                fontWeight: FontWeight.w400),
                                            children: <InlineSpan>[
                                              TextSpan(
                                                  text:
                                                      "${((_progress.value * total2.value * 10) ~/ 10)}/${total2.value}",
                                                  style: TextStyle(
                                                      color: Colours.white,
                                                      fontSize: 14.sp,
                                                      height: 1,
                                                      fontWeight:
                                                          FontWeight.w400)),
                                            ]),
                                      );
                                    }),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    //  if (_progress < 1)
                                    GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () {
                                        checkDialog();
                                        _editorService.cancelAll();
                                      },
                                      child: Container(
                                        width: double.infinity,
                                        height: 40.w,
                                        alignment: Alignment.center,
                                        margin: EdgeInsets.symmetric(
                                            horizontal: 20.w),
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(22.w),
                                            border: Border.all(
                                                width: 1.w,
                                                color: Colours.white)),
                                        child: Text(
                                          "取消合成",
                                          style: TextStyles.regular.copyWith(
                                              fontWeight: FontWeight.w600),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ));
  }

// 在任意位置检查状态
  void checkDialog() {
    if (ExclusiveDialog.isShowing) {
      print("弹窗正在显示");
      ExclusiveDialog.forceClose(); // 强制关闭
    }
  }
}
