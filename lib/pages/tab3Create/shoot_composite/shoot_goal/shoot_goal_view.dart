// ignore_for_file: use_build_context_synchronously

import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/database/model/shot_record_model.dart';
import 'package:shoot_z/pages/tab3Create/shoot_composite/shoot_goal/shoot_goal_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/DateTimeUtils.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///场地->三级页面  自由半场进球
class ShootGoalPage extends StatelessWidget {
  ShootGoalPage({super.key});
  final logic = Get.put(ShootGoalLogic());

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        appBar: MyAppBar(
          title:
              const Text("选择视频"), //S.current.Option_goal ${logic.venueId.value}
          actions: [
            GestureDetector(
              onTap: () {
                logic.getDownLoad();
              },
              child: SizedBox(
                  width: 42.w,
                  height: 50.w,
                  child: WxAssets.images.optionDownload
                      .image(height: 22.w, width: 22.w, color: Colours.white)),
            ),
          ],
        ),
        body: Obx(() {
          return (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : KeepAliveWidget(
                  child: SafeArea(
                    bottom: false,
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          //视频播放器
                          _videoWidget(),
                          //选择时间
                          // _chooseTime(context),
                          SizedBox(
                            height: 20.w,
                          ),
                          Expanded(
                            child: _optionGoalWidget(
                                context, logic.state.dataList1, 0),
                          ),
                          //选球
                          //   Expanded(child: _optionGoalWidget(context)),
                        ]),
                  ),
                );
        }),
        bottomNavigationBar: Obx(() {
          return (logic.dataFag["isFrist"] as bool)
              ? const SizedBox()
              : Container(
                  width: double.infinity,
                  padding: EdgeInsets.only(
                      bottom: 25.w, left: 5.w, right: 15.w, top: 10.w),
                  child: Row(
                    children: [
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () async {
                          // showDateDialog(context, []);
                          // return
                          // logic.getMergeDialog("0", false);
                          // return;
                          // 方法2：使用JSON序列化（100%隔离）
                          logic.state.checkedList3.clear();

                          logic.state.checkedList3.assignAll(logic
                              .state.checkedList
                              .map((e) => e.copyWith())
                              .toList());

                          //素材库
                          showLibraryDialog(context);
                        },
                        child: Stack(
                          children: [
                            WxAssets.images.optionDocument
                                .image(width: 40.w, height: 40.w),
                            if (logic.state.totalCheckedCount.value > 0)
                              Positioned(
                                right: 0,
                                child: Transform.translate(
                                  offset: const Offset(5, 0), // 移动50像素到右和下
                                  child: Container(
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 1.w,
                                        bottom: 1.w),
                                    decoration: BoxDecoration(
                                      color: Colours.red,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                    ),
                                    child: Text(
                                      "${logic.state.totalCheckedCount.value}",
                                      style: TextStyles.titleMedium18
                                          .copyWith(fontSize: 10.sp),
                                    ),
                                  ),
                                ),
                              )
                          ],
                        ),
                      ),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () async {
                          logic.state.checkedList3.clear();
                          logic.state.checkedList3
                              .addAll(logic.state.checkedList);
                          if (logic.state.checkedList3.length < 2) {
                            WxLoading.showToast("请先选择两个视频");
                            return;
                          }
                          if (logic.state.checkedList3.length > 20) {
                            WxLoading.showToast("合成视频最多只能选择20个进球片段");
                            return;
                          }
                          //素材库
                          showLibraryDialog(context);
                        },
                        child: Container(
                          height: 46.w,
                          width: 295.w,
                          alignment: Alignment.center,
                          margin: EdgeInsets.only(left: 14.w, right: 0),
                          padding: EdgeInsets.only(
                              left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                          decoration: BoxDecoration(
                            color: Colours.color282735,
                            borderRadius:
                                BorderRadius.all(Radius.circular(28.r)),
                            gradient: const LinearGradient(
                              colors: [
                                Colours.color7732ED,
                                Colours.colorA555EF
                              ],
                              begin: Alignment.bottomLeft,
                              end: Alignment.bottomRight,
                            ),
                          ),
                          child: Text(
                            S.current.desynthesis,
                            style: TextStyles.semiBold14,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
        }),
        floatingActionButtonLocation:
            FloatingActionButtonLocation.endFloat, // 默认位置
      ),
    );
  }

  Widget _videoWidget() {
    return SizedBox(
      width: double.infinity,
      height: ScreenUtil().screenWidth / 375 * 211,
      child: AspectRatio(
        aspectRatio: 375 / 211, // 宽高比
        child: VideoView(
          controller: logic.videoController,
        ),
      ),
    );
  }

  Widget _optionGoalWidget(
      BuildContext context, RxList<ShotRecordModel> dataList, int tabIndex) {
    return Obx(() {
      return dataList.isEmpty
          ? Expanded(
              child: myNoDataView(
                context,
                msg: S.current.no_goal,
                imagewidget:
                    WxAssets.images.noGoal.image(width: 100.w, height: 84.w),
              ),
            )
          : Padding(
              padding: EdgeInsets.only(left: 15.w, right: 15.w),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    GridView.builder(
                        scrollDirection: Axis.vertical,
                        // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        //  const AlwaysScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          crossAxisSpacing: 15,
                          mainAxisSpacing: 15,
                          childAspectRatio: 105 / 59,
                        ),
                        padding: EdgeInsets.only(bottom: 20.w),
                        itemCount: dataList.length,
                        itemBuilder: (context, position) {
                          return Obx(() {
                            return GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () async {
                                if (dataList[position].eventId ==
                                    logic.state.indexVideoId.value) {
                                  logic.checkVideo(position, isToNext: true);
                                } else {
                                  logic.changeVideoIndex(position);
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  // color: (dataList[position].eventId ==
                                  //         logic.state.indexVideoId.value)
                                  //     ? Colours.color291A3B
                                  //     : Colours.color191921,
                                  border: (dataList[position].eventId ==
                                          logic.state.indexVideoId.value)
                                      ? Border.all(
                                          width: 2.w, color: Colours.white)
                                      : null,
                                  borderRadius: BorderRadius.circular(8.r),
                                  image: (dataList[position].isCheck == "1" ||
                                          dataList[position].eventId ==
                                              logic.state.indexVideoId.value)
                                      ? const DecorationImage(
                                          image: AssetImage(
                                              "assets/images/goal_bg3.png"),
                                          fit: BoxFit.fill)
                                      : const DecorationImage(
                                          image: AssetImage(
                                              "assets/images/goal_bg4.png"),
                                          fit: BoxFit.fill),
                                ),
                                child: Stack(
                                  children: [
                                    Positioned(
                                      top: 0.w,
                                      right: 0.w,
                                      child: GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () async {
                                          logic.checkVideo(position);
                                        },
                                        child: Padding(
                                          padding: EdgeInsets.all(8.w),
                                          child: (dataList[position].isCheck ==
                                                  "1")
                                              ? WxAssets
                                                  .images.checkWhiteBorderYes
                                                  .image(
                                                      height: 14.w, width: 14.w)
                                              : WxAssets.images.checkWhiteBorder
                                                  .image(
                                                      height: 14.w,
                                                      width: 14.w),
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                        bottom: 10.w,
                                        child: Container(
                                          width: 103.w,
                                          padding: EdgeInsets.only(
                                              left: 10.w, right: 10.w),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                child: Text(
                                                  DateTimeUtils.formatTimeWithSeconds(
                                                      DateTime.fromMillisecondsSinceEpoch(
                                                          (dataList[position]
                                                                      .shootTime ??
                                                                  0.0)
                                                              .toInt())), //    dataList[position].videoTime,
                                                  textAlign: TextAlign.right,
                                                  style: TextStyles.medium
                                                      .copyWith(
                                                          fontSize: 12.sp,
                                                          color: Colours
                                                              .colorA8A8BC,
                                                          fontFamily: "DIN"),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ))
                                  ],
                                ),
                              ),
                            );
                          });
                        }),
                  ],
                ),
              ),
            );
    });
  }

  /// 自定义 Tab，选中时渐变色，未选中时普通颜色
  Widget gradientTab(String text, int index, TabController controller) {
    return AnimatedBuilder(
      animation: controller.animation!,
      builder: (context, child) {
        bool isSelected = controller.index == index;
        return Container(
          padding: EdgeInsets.only(bottom: 4.w),
          child: ShaderMask(
            shaderCallback: (bounds) {
              return LinearGradient(
                colors: isSelected
                    ? [
                        Colours.colorFFECC1,
                        Colours.colorE7CEFF,
                        Colours.colorD1EAFF
                      ]
                    : [Colours.colorD6D6D6, Colours.colorD6D6D6],
              ).createShader(bounds);
            },
            child: Text(
              text,
              style: TextStyle(
                fontSize: isSelected ? 16.sp : 14.sp,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: Colors.white,
              ),
            ),
          ),
        );
      },
    );
  }

  //选中的素材
  void showLibraryDialog(
    BuildContext context,
  ) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            height: 520.w,
            color: Colours.color0F0F16,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4.w,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                SizedBox(
                  width: double.infinity,
                  height: 50.w,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      GestureDetector(
                        onTap: () {
                          logic.state.toggleSelectDeleteLibrary();
                        },
                        child: Container(
                          height: 50.w,
                          alignment: Alignment.center,
                          width: 70.w,
                          padding: EdgeInsets.symmetric(
                            horizontal: 15.w,
                          ),
                          child: Text(
                            S.current.delete,
                            style: TextStyles.regular.copyWith(
                                color: Colours.cFF3F3F,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                      const Spacer(),
                      GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          log("isAllSelectedLibrary=${!logic.state.isAllSelectedLibrary.value}");
                          logic.state.toggleSelectAllLibrary(
                              !logic.state.isAllSelectedLibrary.value);
                        },
                        child: Container(
                          height: 50.w,
                          alignment: Alignment.center,
                          padding: EdgeInsets.symmetric(
                              horizontal: 15.w, vertical: 10.w),
                          child: Row(
                            children: [
                              logic.state.isAllSelectedLibrary.value &&
                                      logic.state.checkedList3.isNotEmpty
                                  ? WxAssets.images.checkOn3
                                      .image(width: 16.w, height: 16.w)
                                  : WxAssets.images.checkOn3Wihte
                                      .image(width: 16.w, height: 16.w),
                              SizedBox(
                                width: 5.w,
                              ),
                              Text(
                                logic.state.isAllSelectedLibrary.value &&
                                        logic.state.checkedList3.isNotEmpty
                                    ? S.current.Deselect_all
                                    : S.current.select_all,
                                style: TextStyles.regular.copyWith(
                                    color: Colours.white,
                                    fontWeight: FontWeight.w600),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                logic.state.checkedList3.isEmpty
                    ? myNoDataView(context,
                        msg: S.current.no_goal,
                        imagewidget: WxAssets.images.noGoal
                            .image(width: 100.w, height: 84.w),
                        margin: EdgeInsets.only(top: 150.w))
                    : Expanded(
                        child: GridView.builder(
                            scrollDirection: Axis.vertical,
                            // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                            shrinkWrap: true,
                            physics: const AlwaysScrollableScrollPhysics(), //
                            //   const NeverScrollableScrollPhysics(),
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3,
                              crossAxisSpacing: 15,
                              mainAxisSpacing: 15,
                              childAspectRatio: 105 / 59,
                            ),
                            padding: EdgeInsets.only(
                                bottom: 30.w, left: 15.w, right: 15.w),
                            itemCount: logic.state.checkedList3.length,
                            itemBuilder: (context, position) {
                              return Obx(() {
                                return GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () async {
                                    if (position ==
                                        logic.state.indexVideoLibrary.value) {
                                      logic.state.checkedList3[position]
                                          .isCheck = logic
                                                  .state
                                                  .checkedList3[position]
                                                  .isCheck ==
                                              "1"
                                          ? "0"
                                          : "1";
                                      logic.state.checkedList3.refresh();
                                    } else {
                                      logic.changeVideoIndexLibrary(
                                          logic.state.checkedList3[position],
                                          position);
                                    }
                                  },
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: position ==
                                              logic
                                                  .state.indexVideoLibrary.value
                                          ? Colours.color291A3B
                                          : Colours.color191921,
                                      borderRadius: BorderRadius.circular(8.r),
                                      image: (logic.state.checkedList3[position]
                                                  .isCheck ==
                                              "1")
                                          ? const DecorationImage(
                                              image: AssetImage(
                                                  "assets/images/goal_bg3.png"),
                                              fit: BoxFit.fill)
                                          : const DecorationImage(
                                              image: AssetImage(
                                                  "assets/images/goal_bg4.png"),
                                              fit: BoxFit.fill),
                                    ),
                                    child: Stack(
                                      children: [
                                        Positioned(
                                          top: 0.w,
                                          right: 0.w,
                                          child: GestureDetector(
                                            behavior:
                                                HitTestBehavior.translucent,
                                            onTap: () async {
                                              logic.state.checkedList3[position]
                                                  .isCheck = logic
                                                          .state
                                                          .checkedList3[
                                                              position]
                                                          .isCheck ==
                                                      "1"
                                                  ? "0"
                                                  : "1";
                                              logic.state.checkedList3
                                                  .refresh();
                                            },
                                            child: Padding(
                                              padding: EdgeInsets.all(8.w),
                                              child: (logic
                                                          .state
                                                          .checkedList3[
                                                              position]
                                                          .isCheck ==
                                                      "1")
                                                  ? WxAssets.images
                                                      .checkWhiteBorderYes
                                                      .image(
                                                          height: 14.w,
                                                          width: 14.w)
                                                  : WxAssets
                                                      .images.checkWhiteBorder
                                                      .image(
                                                          height: 14.w,
                                                          width: 14.w),
                                            ),
                                          ),
                                        ),
                                        Positioned(
                                            bottom: 10.w,
                                            child: Container(
                                              width: 103.w,
                                              padding: EdgeInsets.only(
                                                  left: 10.w, right: 10.w),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  if ( //logic.videostate.value == 1 &&
                                                      position ==
                                                          logic
                                                              .state
                                                              .indexVideoLibrary
                                                              .value)
                                                    Container(
                                                      height: 10.w,
                                                      width: 10.w,
                                                      alignment:
                                                          Alignment.center,
                                                      child:
                                                          const LoadingIndicator(
                                                        pathBackgroundColor:
                                                            Colors.black26,
                                                        indicatorType: Indicator
                                                            .lineScaleParty,
                                                        colors: [Colours.white],
                                                      ),
                                                    ),
                                                  Expanded(
                                                    child: Text(
                                                      DateTimeUtils.formatTimeWithSeconds(
                                                          DateTime.fromMillisecondsSinceEpoch((logic
                                                                      .state
                                                                      .checkedList3[
                                                                          position]
                                                                      .shootTime ??
                                                                  0.0)
                                                              .toInt())), // dataList[position].id,//    dataList[position].videoTime,
                                                      textAlign:
                                                          TextAlign.right,
                                                      style: TextStyles.medium
                                                          .copyWith(
                                                              fontSize: 12.sp,
                                                              color: Colours
                                                                  .colorA8A8BC,
                                                              fontFamily:
                                                                  "DIN"),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ))
                                      ],
                                    ),
                                  ),
                                );
                              });
                            }),
                      ),
                if (logic.state.checkedList3.isNotEmpty)
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () async {
                      var list2 = logic.state.checkedList3.where((v) {
                        return v.isCheck == "1";
                      }).toList();
                      if (list2.isEmpty || list2.length <= 1) {
                        WxLoading.showToast("请先选择两个视频");
                        return;
                      }
                      if (list2.length > 20) {
                        WxLoading.showToast("合成视频最多只能选择20个进球片段");
                        return;
                      }
                      AppPage.back();
                      //去合成
                      showDateDialog(context, list2);
                    },
                    child: Container(
                      height: 46.w,
                      width: double.infinity,
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(
                          left: 15.w, right: 15.w, bottom: 34.w, top: 10.w),
                      decoration: BoxDecoration(
                        color: Colours.color282735,
                        borderRadius: BorderRadius.all(Radius.circular(28.r)),
                        gradient: const LinearGradient(
                          colors: [Colours.color7732ED, Colours.colorA555EF],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: Text(
                        S.current.One_click_make_piece,
                        style: TextStyles.semiBold14,
                      ),
                    ),
                  ),
              ],
            ),
          );
        });
      },
    );
  }

  void showDateDialog(BuildContext context, List<ShotRecordModel> list2) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            color: Colours.color191921,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                // Container(
                //     width: double.infinity,
                //     padding: EdgeInsets.only(top: 18.w, bottom: 10.w),
                //     alignment: Alignment.center,
                //     child: Text(
                //       S.current.composite_video_dialog_tips1,
                //       style: TextStyles.medium.copyWith(fontSize: 16.sp),
                //     )),
                // Padding(
                //   padding: EdgeInsets.only(top: 25.w, bottom: 15.w),
                //   child: Text(
                //     S.current.composite_video_dialog_tips2,
                //     style: TextStyles.medium
                //         .copyWith(fontSize: 14.sp, color: Colours.color5C5C6E),
                //   ),
                // ),
                // Wrap(
                //   spacing: 15.w,
                //   runSpacing: 15.w,
                //   children: List.generate(2, (index) {
                //     return GestureDetector(
                //       behavior: HitTestBehavior.translucent,
                //       onTap: () {
                //         //单个视频片段时长
                //         logic.state.compositeOption1.value = index;
                //       },
                //       child: Container(
                //         width: 100.w,
                //         height: 44.w,
                //         alignment: Alignment.center,
                //         decoration: BoxDecoration(
                //             color: Colours.color22222D,
                //             borderRadius: BorderRadius.circular(12.r),
                //             border: logic.state.compositeOption1.value == index
                //                 ? Border.all(
                //                     width: 1, color: Colours.color9393A5)
                //                 : null),
                //         child: Row(
                //           mainAxisSize: MainAxisSize.min,
                //           children: [
                //             WxAssets.images.optionGoalTime.image(
                //                 width: 20.w,
                //                 height: 20.w,
                //                 color:
                //                     logic.state.compositeOption1.value == index
                //                         ? Colours.white
                //                         : Colours.color5C5C6E),
                //             SizedBox(
                //               width: 5.w,
                //             ),
                //             Text(
                //               index == 0 ? "5s" : "10s",
                //               style: TextStyles.medium.copyWith(
                //                   fontSize: 14.sp,
                //                   color: logic.state.compositeOption1.value ==
                //                           index
                //                       ? Colours.white
                //                       : Colours.color5C5C6E),
                //             ),
                //           ],
                //         ),
                //       ),
                //     );
                //   }),
                // ),

                Padding(
                  padding: EdgeInsets.only(top: 25.w, bottom: 15.w),
                  child: Text(
                    "视频效果与个性化",
                    style: TextStyles.medium
                        .copyWith(fontSize: 14.sp, color: Colours.white),
                  ),
                ),
                Wrap(
                  spacing: 10.w,
                  runSpacing: 10.w,
                  children: List.generate(3, (index2) {
                    int index = index2 + 1;
                    return GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        //单个视频片段时长
                        logic.state.compositeOption2[index] =
                            logic.state.compositeOption2[index] == "0"
                                ? "1"
                                : "0";
                      },
                      child: Container(
                        width: 105.w,
                        height: 40.w,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            color: Colours.color22222D,
                            borderRadius: BorderRadius.circular(22.r),
                            gradient: logic.state.compositeOption2[index] == "1"
                                ? const LinearGradient(colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ])
                                : null,
                            border: logic.state.compositeOption2[index] != "1"
                                ? Border.all(width: 1, color: Colours.white)
                                : null),
                        child: Text(
                          index == 0
                              ? S.current.composite_video_dialog_tips4
                              : index == 1
                                  ? S.current.composite_video_dialog_tips5
                                  : index == 2
                                      ? S.current.composite_video_dialog_tips6
                                      : S.current.composite_video_dialog_tips7,
                          style: TextStyles.medium
                              .copyWith(fontSize: 14.sp, color: Colours.white),
                        ),
                      ),
                    );
                  }),
                ),

                if (logic.venueId.value != 0)
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      logic.state.isShare.value = !logic.state.isShare.value;
                    },
                    child: Container(
                      padding: EdgeInsets.only(top: 20.w, bottom: 0.w),
                      child: Row(
                        children: [
                          logic.state.isShare.value
                              ? WxAssets.images.checkOn3
                                  .image(width: 16.w, height: 16.w)
                              : WxAssets.images.checkOn3Wihte
                                  .image(width: 16.w, height: 16.w),
                          SizedBox(
                            width: 6.w,
                          ),
                          Text(
                            "共享到场地展示为精彩视频",
                            style: TextStyles.medium.copyWith(
                                fontSize: 14.sp, color: Colours.white),
                          )
                        ],
                      ),
                    ),
                  ),
                Container(
                  height: 46.w,
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                    color: Colours.color0F0F16,
                    borderRadius: BorderRadius.circular(23.r),
                  ),
                  margin: EdgeInsets.only(bottom: 5.w, top: 20.w),
                  padding: EdgeInsets.only(
                      left: 8.w, right: 5.w, top: 4.w, bottom: 3.w),
                  child: TextField(
                    controller: logic.nickNameController,
                    style: TextStyle(
                      color: Colours.white,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.deny(
                          RegExp(r'[" "]')), // 只允许输入数字
                      LengthLimitingTextInputFormatter(20), // 限制输入长度为8
                    ],
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: S.current.video_name,
                      contentPadding: EdgeInsets.only(left: 10.w, bottom: 10.w),
                      hintStyle: TextStyle(
                        color: Colours.color999999,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),

                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () async {
                    if (logic.isProcessing.value) {
                      return;
                    }
                    //一键成片
                    Get.back();
                    await Future.delayed(const Duration(milliseconds: 100));
                    logic.compositeVideo(list2);
                  },
                  child: Container(
                    height: 46.w,
                    width: double.infinity,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(top: 25.w, bottom: 15.w),
                    padding: EdgeInsets.only(
                        left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                    decoration: BoxDecoration(
                      color: Colours.color282735,
                      borderRadius: BorderRadius.all(Radius.circular(28.r)),
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Text(
                      S.current.One_click_make_piece,
                      style: TextStyles.semiBold14,
                    ),
                  ),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    logic.state.rememberOption.value =
                        logic.state.rememberOption.value == "0" ? "1" : "0";
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 3.w),
                    alignment: Alignment.center,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                            width: 14.w,
                            height: 14.w,
                            margin: EdgeInsets.only(right: 8.w, bottom: 3.w),
                            child: logic.state.rememberOption.value == "0"
                                ? MyImage(
                                    "check_on_3.png",
                                    width: 16.w,
                                    height: 16.w,
                                    isAssetImage: true,
                                  )
                                : MyImage(
                                    "check_on_3_wihte.png",
                                    width: 16.w,
                                    height: 16.w,
                                    isAssetImage: true,
                                    imageColor: Colours.color353542,
                                    bgColor: Colours.color353542,
                                    radius: 8.w,
                                  )

                            // Icon(
                            //   logic.state.rememberOption.value == "0"
                            //       ? Icons.check_circle
                            //       : Icons.radio_button_unchecked,
                            //   color: logic.state.rememberOption.value == "0"
                            //       ? Colours.white
                            //       : Colours.color9393A5,
                            //   size: 17,
                            // ),
                            ),
                        Text(
                          S.current.composite_video_dialog_tips12,
                          style: TextStyles.medium.copyWith(
                              fontSize: 12.sp, color: Colours.color9393A5),
                        )
                      ],
                    ),
                  ),
                ),

                SizedBox(
                  height: 30.w,
                ),
                SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
              ],
            ),
          );
        });
      },
    );
  }
}
