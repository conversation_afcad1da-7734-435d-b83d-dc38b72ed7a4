import 'dart:developer';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab3Create/ptz/camera_shot_logic.dart';
import 'package:ui_packages/ui_packages.dart';

class CameraShotPage extends StatelessWidget {
  final CameraShotLogic logic = Get.put(CameraShotLogic());

  CameraShotPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Obx(() {
        if (logic.hasError.value) {
          return _buildErrorScreen();
        }

        if (!logic.isCameraInitialized.value) {
          return _buildLoadingScreen();
        }

        return _buildCameraScreen();
      }),
    );
  }

  Widget _buildErrorScreen() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 64),
            SizedBox(height: 20),
            Text(
              '相机初始化失败',
              style: TextStyle(color: Colors.white, fontSize: 20),
            ),
            SizedBox(height: 10),
            Text(
              logic.errorMessage.value,
              style: TextStyle(color: Colors.white70),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 30),
            ElevatedButton(
              onPressed: logic.reloadCamera,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
              child: Text('重试', style: TextStyle(fontSize: 16)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
          SizedBox(height: 20),
          Text(
            '初始化相机中...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCameraScreen() {
    return Stack(
      children: [
        // 相机预览
        SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: CameraPreview(logic.cameraController)),

        // 控制界面
        _buildControlsOverlay(),

        // 调试信息（可选）
        // _buildDebugInfo(),
      ],
    );
  }

  Widget _buildControlsOverlay() {
    return Column(
      children: [
        // 顶部控制栏
        _buildTopControls(logic.isLandscape),

        Expanded(child: Container()),

        // 底部控制栏
        _buildBottomControls(logic.isLandscape),
      ],
    );
  }

  Widget _buildTopControls(bool isLandscape) {
    return Container(
      height: ScreenUtil().statusBarHeight + 40.w,
      width: double.infinity,
      alignment: Alignment.center,
      color: Colours.colorCC0F0F16,
      padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          WxAssets.images.cameraSetting1080.image(),
          InkWell(
              onTap: () {
                print('开始录制');
                logic.toggleRecording();
              },
              child: WxAssets.images.cameraSettingRecord.image()),
          WxAssets.images.cameraSettingHome.image(),
          WxAssets.images.cameraSettingSet.image()
        ],
      ),
    );
  }

  Widget _buildBottomControls(bool isLandscape) {
    return Column(
      children: [
        if (!logic.isRecording.value)
          InkWell(
              onTap: () {
                print('开始录制');
                logic.toggleRecording();
              },
              child: WxAssets.images.recordingIcon.image()),
        if (logic.isRecording.value)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Opacity(
                opacity: 0,
                child: WxAssets.images.finishRecordingIcon.image(),
              ),
              InkWell(
                  onTap: () {
                    // print('暂停录制');
                    // logic.toggleRecording();
                  },
                  child: WxAssets.images.recordingStopIcon.image()),
              InkWell(
                  onTap: () {
                    print('结束录制');
                    logic.toggleRecording();
                  },
                  child: WxAssets.images.finishRecordingIcon.image()),
            ],
          ).marginSymmetric(horizontal: 20.w),
        SizedBox(
          height: 20.w,
        ),
        Container(
          height: 72.w,
          width: double.infinity,
          alignment: Alignment.center,
          color: Colours.colorCC0F0F16,
          padding: EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              InkWell(
                onTap: () {
                  logic.setGimbalDirection(true);
                },
                child: Text(
                  '半场竖屏',
                  style: TextStyles.titleSemiBold16,
                ),
              ),
              SizedBox(
                width: 20.w,
              ),
              InkWell(
                  onTap: () {
                    logic.setGimbalDirection(false);
                  },
                  child: Text(
                    '全场横屏',
                    style: TextStyles.regular,
                  )),
            ],
          ),
        ),
      ],
    );
  }
}
