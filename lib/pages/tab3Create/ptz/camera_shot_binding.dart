import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/ptz/config/ptz_dependencies.dart';
import 'package:shoot_z/pages/tab3Create/ptz/camera_shot_logic.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/gimbal_service.dart';

/// CameraShotPage 页面的依赖绑定
class CameraShotBinding extends Bindings {
  @override
  void dependencies() async {
    // 确保 PTZ 依赖已初始化
    if (!Get.isRegistered<GimbalService>()) {
      await PtzDependencies.init();
    }
    
    // 注册控制器
    Get.lazyPut(() => CameraShotLogic());
  }
}
