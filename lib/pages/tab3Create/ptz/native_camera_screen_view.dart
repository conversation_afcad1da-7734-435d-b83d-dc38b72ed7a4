import 'dart:developer';

import 'package:floor/floor.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab/logic.dart';
import 'package:shoot_z/pages/tab2Venue/tab_venue_logic.dart';
import 'package:shoot_z/pages/tab3Create/ptz/native_camera_screen_logic.dart';
import 'package:shoot_z/pages/tab3Create/ptz/widget/native_camera_view.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

/// 原生相机界面 - 使用iOS原生CameraView
class NativeCameraScreen extends StatelessWidget {
  final VoidCallback? onBack;
  final logic = Get.put(NativeCameraScreenLogic());
  NativeCameraScreen({
    super.key,
    this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: Colors.black,
      body: Obx(() {
        return logic.showLandscapeMode.value
            ? //竖屏
            Stack(
                children: [
                  // 原生相机视图
                  nativeCameraViewWidget(true),
                  // Flutter UI覆盖层
                  // _buildFlutterOverlay(logic),
                  Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      child: _buildTopControls(true)),
                  Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: _buildBottomControls(true)),
                  Center(
                    child: logic.operationStep.value ==
                            InstructionsSteps.effectPreview
                        ? _buildFirstView(true)
                        : logic.operationStep.value ==
                                InstructionsSteps.recommendedPosition
                            ? _buildRecommandPosition(true)
                            : logic.operationStep.value ==
                                    InstructionsSteps.limitPositionsTips
                                ? _buildLimitPositionsTips(true)
                                : logic.operationStep.value ==
                                        InstructionsSteps.leftLimitSetting
                                    ? _buildLeftLimit(true)
                                    : logic.operationStep.value ==
                                            InstructionsSteps.rightLimitSetting
                                        ? _buildRightLimit(true)
                                        : const SizedBox(),
                  ),
                  if (logic.showSetting.value) _buildSettingView(true),
                  if (logic.isPaused)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          alignment: Alignment.center,
                          width: 80.w,
                          height: 30.w,
                          margin: EdgeInsets.only(top: 110.w),
                          decoration: BoxDecoration(
                              color: Colours.colorFF3F3F,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(15.r))),
                          child: Text(
                            '已暂停',
                            style: TextStyles.display12
                                .copyWith(color: Colors.white),
                          ),
                        ),
                      ],
                    ),
                  if (logic.showLeftLimitSetting.value)
                    Center(
                      child: _buildLeftLimit(true),
                    ),
                  if (logic.showRightLimitSetting.value)
                    Center(
                      child: _buildRightLimit(true),
                    )
                ],
              )
            : //横屏
            Stack(
                alignment: AlignmentDirectional.topCenter,
                children: [
                  // 原生相机视图
                  nativeCameraViewWidget(false),
                  // Flutter UI覆盖层
                  // _buildFlutterOverlay(logic),
                  Positioned(
                      top: 0,
                      left: 0,
                      bottom: 0,
                      child: _buildTopControls(false)),
                  Positioned(
                      bottom: 0,
                      top: 0,
                      right: 0,
                      child: _buildBottomControls(false)),
                  if (logic.isRecording)
                    Positioned(
                        top: 20 * logic.scanW.value,
                        child: Container(
                          width: 120 * logic.scanW.value,
                          height: 40 * logic.scanW.value,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              color: Colours.color70000000,
                              borderRadius: BorderRadius.circular(
                                  32 * logic.scanW.value)),
                          child: Text(
                            logic.recordingTime,
                            style: TextStyles.numberDin16
                                .copyWith(fontSize: 16 * logic.scanW.value),
                          ),
                        )),
                  if (logic.isPaused)
                    Positioned(
                      top: 75 * logic.scanW.value,
                      child: Container(
                        alignment: Alignment.center,
                        width: 80 * logic.scanW.value,
                        height: 30 * logic.scanW.value,
                        decoration: BoxDecoration(
                            color: Colours.colorFF3F3F,
                            borderRadius: BorderRadius.all(Radius.circular(
                              15 * logic.scanW.value,
                            ))),
                        child: Text(
                          '已暂停',
                          style: TextStyles.display12.copyWith(
                            color: Colors.white,
                            fontSize: 12 * logic.scanW.value,
                          ),
                        ),
                      ),
                    ),
                  Center(
                    child: logic.operationStep.value ==
                            InstructionsSteps.effectPreview
                        ? _buildFirstView(false)
                        : logic.operationStep.value ==
                                InstructionsSteps.recommendedPosition
                            ? _buildRecommandPosition(false)
                            : logic.operationStep.value ==
                                    InstructionsSteps.limitPositionsTips
                                ? _buildLimitPositionsTips(false)
                                : logic.operationStep.value ==
                                        InstructionsSteps.leftLimitSetting
                                    ? _buildLeftLimit(false)
                                    : logic.operationStep.value ==
                                            InstructionsSteps.rightLimitSetting
                                        ? _buildRightLimit(false)
                                        // : logic.isRecording
                                        //     ? Container(
                                        //         padding: EdgeInsets.symmetric(
                                        //             horizontal:
                                        //                 27 * logic.scanW.value,
                                        //             vertical:
                                        //                 14 * logic.scanW.value),
                                        //         decoration: BoxDecoration(
                                        //             color:
                                        //                 Colours.color70000000,
                                        //             borderRadius:
                                        //                 BorderRadius.circular(
                                        //                     22 *
                                        //                         logic.scanW
                                        //                             .value)),
                                        //         child: Text(
                                        //           logic.recordingTime,
                                        //           style: TextStyles.numberDin16
                                        //               .copyWith(
                                        //                   fontSize: 16 *
                                        //                       logic
                                        //                           .scanW.value),
                                        //         ),
                                        //       )
                                        : const SizedBox(),
                  ),
                  if (logic.showSetting.value) _buildSettingView(false),

                  if (logic.showLeftLimitSetting.value)
                    Center(
                      child: _buildLeftLimit(false),
                    ),
                  if (logic.showRightLimitSetting.value)
                    Center(
                      child: _buildRightLimit(false),
                    )
                ],
              );
      }),
    );
  }

  Widget nativeCameraViewWidget(bool isLandscape) {
    return isLandscape
        ? NativeCameraView(
            width: double.infinity,
            height: double.infinity,
            onCameraReady: () {
              log('Native camera ready');
            },
          )
        : Container(
            color: Colors.transparent,
            child: NativeCameraView(
              width: ScreenUtil().screenWidth,
              height: ScreenUtil().screenHeight,
              onCameraReady: () {
                log('Native camera ready');
              },
            ),
          );
  }

  Widget _buildSettingView(bool isLandscape) {
    return isLandscape
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                margin: EdgeInsets.only(left: 20.w, right: 20.w, top: 100.w),
                width: ScreenUtil().screenWidth - 30.w,
                padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 20.w),
                decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  children: [
                    Text(
                      "云台设置",
                      style: TextStyles.titleSemiBold16,
                    ),
                    SizedBox(
                      height: 24.w,
                    ),
                    // SizedBox(
                    //     height: 50.w,
                    //     child: Row(
                    //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //       children: [
                    //         Text(
                    //           "云台提示音与振动",
                    //           style: TextStyles.semiBold14,
                    //         ),
                    //         WxAssets.images.switchOff.image()
                    //       ],
                    //     )),
                    // const Divider(
                    //   color: Colours.color1AFFFFFF,
                    //   height: 0,
                    //   thickness: 0.5,
                    // ),
                    InkWell(
                      onTap: () {
                        logic.showLeftLimitSetting.value = true;
                        logic.showSetting.value = false;
                      },
                      child: SizedBox(
                        height: 50.w,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "云台限位设置",
                              style: TextStyles.semiBold14,
                            ),
                            WxAssets.images.icArrowRight
                                .image(color: Colors.white)
                          ],
                        ),
                      ),
                    ),
                    // const Divider(
                    //   color: Colours.color1AFFFFFF,
                    //   height: 0,
                    //   thickness: 0.5,
                    // ),
                    // SizedBox(
                    //   height: 50.w,
                    //   child: Row(
                    //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //     children: [
                    //       Text(
                    //         "恢复出厂设置",
                    //         style: TextStyles.semiBold14,
                    //       ),
                    //       WxAssets.images.icArrowRight.image(color: Colors.white)
                    //     ],
                    //   ),
                    // ),
                  ],
                ),
              )
            ],
          )
        : Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                margin: EdgeInsets.only(
                  left: 20 * logic.scanW.value,
                  right: 20 * logic.scanW.value,
                  top: 100 * logic.scanW.value,
                ),
                width: 345 * logic.scanW.value,
                padding: EdgeInsets.only(
                  left: 20 * logic.scanW.value,
                  right: 20 * logic.scanW.value,
                  top: 20 * logic.scanW.value,
                ),
                decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(
                    8 * logic.scanW.value,
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      "云台设置",
                      style: TextStyles.titleSemiBold16,
                    ),
                    SizedBox(
                      height: 24 * logic.scanW.value,
                    ),
                    // SizedBox(
                    //     height: 50.w,
                    //     child: Row(
                    //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //       children: [
                    //         Text(
                    //           "云台提示音与振动",
                    //           style: TextStyles.semiBold14,
                    //         ),
                    //         WxAssets.images.switchOff.image()
                    //       ],
                    //     )),
                    // const Divider(
                    //   color: Colours.color1AFFFFFF,
                    //   height: 0,
                    //   thickness: 0.5,
                    // ),
                    InkWell(
                      onTap: () {
                        logic.showLeftLimitSetting.value = true;
                        logic.showSetting.value = false;
                      },
                      child: SizedBox(
                        height: 50 * logic.scanW.value,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "云台限位设置",
                              style: TextStyles.semiBold14,
                            ),
                            WxAssets.images.icArrowRight
                                .image(color: Colors.white)
                          ],
                        ),
                      ),
                    ),
                    // const Divider(
                    //   color: Colours.color1AFFFFFF,
                    //   height: 0,
                    //   thickness: 0.5,
                    // ),
                    // SizedBox(
                    //   height: 50.w,
                    //   child: Row(
                    //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //     children: [
                    //       Text(
                    //         "恢复出厂设置",
                    //         style: TextStyles.semiBold14,
                    //       ),
                    //       WxAssets.images.icArrowRight.image(color: Colors.white)
                    //     ],
                    //   ),
                    // ),
                  ],
                ),
              )
            ],
          );
  }

  Widget _buildFirstView(bool isLandscape) {
    return isLandscape
        ? Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: ScreenUtil().screenWidth - 80.w,
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  children: [
                    WxAssets.images.portraitExampleImage.image(),
                    SizedBox(
                      height: 24.w,
                    ),
                    Text(
                      "半场竖屏模式效果预览",
                      style: TextStyles.display12,
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 20.w,
              ),
              TextButton(
                onPressed: () async {
                  logic.nextTimeNoPop.value = !logic.nextTimeNoPop.value;
                  await WxStorage.instance.setBool(
                      "rememberCameraShotOption", logic.nextTimeNoPop.value);
                },
                style: TextButton.styleFrom(
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  padding: EdgeInsets.zero,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    logic.nextTimeNoPop.value
                        ? WxAssets.images.selectIcon.image()
                        : WxAssets.images.unselectIcon.image(),
                    SizedBox(width: 6.w),
                    Text("下次不再弹出",
                        style: TextStyles.regular.copyWith(fontSize: 12.sp)),
                  ],
                ),
              ),
              SizedBox(
                height: 20.w,
              ),
              InkWell(
                  onTap: () => logic.operationStep.value =
                      InstructionsSteps.recommendedPosition,
                  child: Container(
                    width: 200.w,
                    height: 50.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Colours.color7732ED, Colours.colorA555EF],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(25.r)),
                    child: Text(
                      '开始拍摄',
                      style: TextStyles.semiBold14,
                    ),
                  ))
            ],
          )
        : Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 320 * logic.scanW.value,
                padding: EdgeInsets.all(
                  20 * logic.scanW.value,
                ),
                decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(
                    8 * logic.scanW.value,
                  ),
                ),
                child: Column(
                  children: [
                    WxAssets.images.yuntaiJiaozhun3.image(),
                    SizedBox(
                      height: 24 * logic.scanW.value,
                    ),
                    Text(
                      "半场竖屏模式效果预览",
                      style: TextStyles.display12.copyWith(
                        fontSize: 12 * logic.scanW.value,
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 20 * logic.scanW.value,
              ),
              TextButton(
                onPressed: () async {
                  logic.nextTimeNoPop.value = !logic.nextTimeNoPop.value;
                  await WxStorage.instance.setBool(
                      "rememberCameraShotOption", logic.nextTimeNoPop.value);
                },
                style: TextButton.styleFrom(
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  padding: EdgeInsets.zero,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    logic.nextTimeNoPop.value
                        ? WxAssets.images.selectIcon.image()
                        : WxAssets.images.unselectIcon.image(),
                    SizedBox(
                      width: 6 * logic.scanW.value,
                    ),
                    Text("下次不再弹出",
                        style: TextStyles.regular.copyWith(
                          fontSize: 12 * logic.scanW.value,
                        )),
                  ],
                ),
              ),
              SizedBox(
                height: 20 * logic.scanW.value,
              ),
              InkWell(
                  onTap: () => logic.operationStep.value =
                      InstructionsSteps.recommendedPosition,
                  child: Container(
                    width: 200 * logic.scanW.value,
                    height: 50 * logic.scanW.value,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Colours.color7732ED, Colours.colorA555EF],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(
                          25 * logic.scanW.value,
                        )),
                    child: Text(
                      '开始拍摄',
                      style: TextStyles.semiBold14
                          .copyWith(fontSize: 14 * logic.scanW.value),
                    ),
                  ))
            ],
          );
  }

  Widget _buildRecommandPosition(bool isLandscape) {
    return isLandscape
        ? Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Container(
              width: ScreenUtil().screenWidth - 30.w,
              padding: EdgeInsets.all(15.w),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                children: [
                  Text(
                    textAlign: TextAlign.center,
                    "半场拍摄前请尽量将云台置于推荐位置以保证最佳拍摄效果",
                    style: TextStyles.semiBold14.copyWith(height: 1.71),
                  ),
                  SizedBox(
                    height: 30.w,
                  ),
                  WxAssets.images.recommandPositionImage.image(),
                  SizedBox(
                    height: 17.w,
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 42.w,
            ),
            InkWell(
                onTap: () => logic.operationStep.value =
                    InstructionsSteps.limitPositionsTips,
                child: Container(
                  width: 200.w,
                  height: 50.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(25.r)),
                  child: Text(
                    '知道了',
                    style: TextStyles.semiBold14,
                  ),
                ))
          ])
        : Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Column(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                      vertical: 12 * logic.scanW.value,
                      horizontal: 26 * logic.scanW.value),
                  decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.circular(20 * logic.scanW.value),
                  ),
                  child: Text(
                    textAlign: TextAlign.center,
                    "请将云台放置在地上，并将扳机键对准中线",
                    style: TextStyles.semiBold14
                        .copyWith(height: 0, fontSize: 14 * logic.scanW.value),
                  ),
                ),
                SizedBox(
                  height: 8 * logic.scanW.value,
                ),
                WxAssets.images.yuntaiJiaozhun2.image(),
              ],
            ),
            SizedBox(
              height: 23 * logic.scanW.value,
            ),
            InkWell(
                onTap: () => logic.operationStep.value =
                    InstructionsSteps.limitPositionsTips,
                child: Container(
                  width: 200 * logic.scanW.value,
                  height: 50 * logic.scanW.value,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius:
                          BorderRadius.circular(25 * logic.scanW.value)),
                  child: Text(
                    '确认',
                    style: TextStyles.semiBold14
                        .copyWith(fontSize: 14 * logic.scanW.value),
                  ),
                ))
          ]);
  }

  Widget _buildLimitPositionsTips(bool isLandscape) {
    return isLandscape
        ? Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Container(
              width: ScreenUtil().screenWidth - 30.w,
              padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 20.w),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                textAlign: TextAlign.center,
                "在拍摄前，请预先设置云台的左右限位。限位点定义了云台的最大旋转角度，以确保拍摄采集的最佳效果",
                style: TextStyles.semiBold14.copyWith(height: 1.71),
              ),
            ),
            SizedBox(
              height: 28 * logic.scanW.value,
            ),
            InkWell(
                onTap: () => logic.operationStep.value =
                    InstructionsSteps.leftLimitSetting,
                child: Container(
                  width: 200.w,
                  height: 50.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(25.r)),
                  child: Text(
                    '开始设置',
                    style: TextStyles.semiBold14,
                  ),
                ))
          ])
        : Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Container(
              width: 345 * logic.scanW.value,
              padding: EdgeInsets.symmetric(
                  horizontal: 30 * logic.scanW.value,
                  vertical: 20 * logic.scanW.value),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(8 * logic.scanW.value),
              ),
              child: Text(
                textAlign: TextAlign.center,
                "在拍摄前，请预先设置云台的左右限位。限位点定义了云台的最大旋转角度，以确保拍摄采集的最佳效果",
                style: TextStyles.semiBold14.copyWith(height: 1.71),
              ),
            ),
            SizedBox(
              height: 28 * logic.scanW.value,
            ),
            InkWell(
                onTap: () => logic.operationStep.value =
                    InstructionsSteps.leftLimitSetting,
                child: Container(
                  width: 200 * logic.scanW.value,
                  height: 50 * logic.scanW.value,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius:
                          BorderRadius.circular(25 * logic.scanW.value)),
                  child: Text(
                    '开始设置',
                    style: TextStyles.semiBold14,
                  ),
                ))
          ]);
  }

  Widget _buildLeftLimit(bool isLandscape) {
    return isLandscape
        ? Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Container(
              width: ScreenUtil().screenWidth - 30.w,
              padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.w),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                children: [
                  Text(
                    textAlign: TextAlign.center,
                    "请通过云台遥控器向左移动云台，选择起始位置 后点击“下一步”",
                    style: TextStyles.semiBold14.copyWith(height: 1.71),
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  WxAssets.images.leftLimit.image(),
                ],
              ),
            ),
            SizedBox(
              height: 28.w,
            ),
            InkWell(
                onTap: () {
                  logic.setLeftLimit();
                  if (logic.showLeftLimitSetting.value) {
                    logic.showLeftLimitSetting.value = false;
                    logic.showRightLimitSetting.value = true;
                    return;
                  }
                  logic.operationStep.value =
                      InstructionsSteps.rightLimitSetting;
                },
                child: Container(
                  width: 200.w,
                  height: 50.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(25.r)),
                  child: Text(
                    '下一步',
                    style: TextStyles.semiBold14,
                  ),
                ))
          ])
        : Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Container(
              width: 345 * logic.scanW.value,
              padding: EdgeInsets.symmetric(
                  horizontal: 25 * logic.scanW.value,
                  vertical: 20 * logic.scanW.value),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(
                  8 * logic.scanW.value,
                ),
              ),
              child: Column(
                children: [
                  Text(
                    textAlign: TextAlign.center,
                    "请通过云台遥控器向左移动云台，选择起始位置 后点击“下一步”",
                    style: TextStyles.semiBold14.copyWith(height: 1.71),
                  ),
                  SizedBox(
                    height: 20 * logic.scanW.value,
                  ),
                  WxAssets.images.leftLimit.image(),
                ],
              ),
            ),
            SizedBox(
              height: 28 * logic.scanW.value,
            ),
            InkWell(
                onTap: () {
                  logic.setLeftLimit();
                  if (logic.showLeftLimitSetting.value) {
                    logic.showLeftLimitSetting.value = false;
                    logic.showRightLimitSetting.value = true;
                    return;
                  }
                  logic.operationStep.value =
                      InstructionsSteps.rightLimitSetting;
                },
                child: Container(
                  width: 200 * logic.scanW.value,
                  height: 50 * logic.scanW.value,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(
                        25 * logic.scanW.value,
                      )),
                  child: Text(
                    '下一步',
                    style: TextStyles.semiBold14,
                  ),
                ))
          ]);
  }

  Widget _buildRightLimit(bool isLandscape) {
    return isLandscape
        ? Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Container(
              width: ScreenUtil().screenWidth - 30.w,
              padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.w),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                children: [
                  Text(
                    textAlign: TextAlign.center,
                    "继续用遥控器向右移动云台，选择目标位置后点击“确认”完成设置",
                    style: TextStyles.semiBold14.copyWith(height: 1.71),
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  WxAssets.images.rightLimit.image(),
                ],
              ),
            ),
            SizedBox(
              height: 28.w,
            ),
            InkWell(
                onTap: () {
                  logic.setRightLimit();
                  if (logic.showRightLimitSetting.value) {
                    logic.showRightLimitSetting.value = false;
                    return;
                  }
                  logic.operationStep.value = InstructionsSteps.recordingStart;
                },
                child: Container(
                  width: 200.w,
                  height: 50.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(25.r)),
                  child: Text(
                    '确定',
                    style: TextStyles.semiBold14,
                  ),
                ))
          ])
        : Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Container(
              width: 345 * logic.scanW.value,
              padding: EdgeInsets.symmetric(
                horizontal: 25 * logic.scanW.value,
                vertical: 20 * logic.scanW.value,
              ),
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(
                  8 * logic.scanW.value,
                ),
              ),
              child: Column(
                children: [
                  Text(
                    textAlign: TextAlign.center,
                    "继续用遥控器向右移动云台，选择目标位置后点击“确认”完成设置",
                    style: TextStyles.semiBold14.copyWith(height: 1.71),
                  ),
                  SizedBox(
                    height: 20 * logic.scanW.value,
                  ),
                  WxAssets.images.rightLimit.image(),
                ],
              ),
            ),
            SizedBox(
              height: 28 * logic.scanW.value,
            ),
            InkWell(
                onTap: () {
                  logic.setRightLimit();
                  if (logic.showRightLimitSetting.value) {
                    logic.showRightLimitSetting.value = false;
                    return;
                  }
                  logic.operationStep.value = InstructionsSteps.recordingStart;
                },
                child: Container(
                  width: 200 * logic.scanW.value,
                  height: 50 * logic.scanW.value,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(
                        25 * logic.scanW.value,
                      )),
                  child: Text(
                    '确定',
                    style: TextStyles.semiBold14,
                  ),
                ))
          ]);
  }

  Widget _buildTopControls(bool isLandscape) {
    return isLandscape
        ? Container(
            height: ScreenUtil().statusBarHeight + 40.w,
            width: double.infinity,
            alignment: Alignment.center,
            color: Colours.colorCC0F0F16,
            padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                WxAssets.images.cameraSetting1080.image(
                    width: 32 * logic.scanW.value,
                    height: 32 * logic.scanW.value),
                // InkWell(
                //     onTap: () {
                //       //直播
                //     },
                //     child: WxAssets.images.cameraSettingRecord.image()),
                InkWell(
                    onTap: () {
                      if (logic.isRecording) {
                        getMyDialog(
                          '结束拍摄',
                          S.current.sure,
                          content: '确定要结束拍摄吗？结束后视频不会保存',
                          () {
                            AppPage.back();
                            AppPage.back();
                          },
                          scanw: logic.showLandscapeMode.value
                              ? null
                              : logic.scanW.value,
                          isShowClose: false,
                          btnIsHorizontal: true,
                          btnText2: S.current.cancel,
                          onPressed2: () {
                            AppPage.back();
                          },
                        );
                      } else {
                        AppPage.back();
                      }
                    },
                    child: WxAssets.images.cameraSettingHome.image(
                        width: 32 * logic.scanW.value,
                        height: 32 * logic.scanW.value)),
                InkWell(
                    onTap: () {
                      logic.showSetting.value = !logic.showSetting.value;
                    },
                    child: logic.showSetting.value
                        ? WxAssets.images.cameraSettingSet.image(
                            color: Colours.color7732ED,
                            width: 32.w,
                            height: 32.w)
                        : WxAssets.images.cameraSettingSet
                            .image(width: 32.w, height: 32.w))
              ],
            ),
          )
        : Container(
            height: double.infinity,
            //  width: 72 * logic.scanW.value,
            alignment: Alignment.center,
            color: Colours.colorCC0F0F16,
            padding: EdgeInsets.only(
              top: ScreenUtil().statusBarHeight,
              left: 38 * logic.scanW.value,
              right: 10 * logic.scanW.value,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                // InkWell(
                //     onTap: () {
                //       //直播
                //     },
                //     child: WxAssets.images.cameraSettingRecord.image()),

                InkWell(
                    onTap: () {
                      logic.showSetting.value = !logic.showSetting.value;
                    },
                    child: logic.showSetting.value
                        ? WxAssets.images.cameraSettingSet.image(
                            color: Colours.color7732ED,
                            width: 32 * logic.scanW.value,
                            height: 32 * logic.scanW.value)
                        : WxAssets.images.cameraSettingSet.image(
                            width: 32 * logic.scanW.value,
                            height: 32 * logic.scanW.value)),

                InkWell(
                    onTap: () {
                      if (logic.isRecording) {
                        getMyDialog(
                          '结束拍摄',
                          S.current.sure,
                          content: '确定要结束拍摄吗？结束后视频不会保存',
                          () {
                            AppPage.back();
                            AppPage.back();
                          },
                          scanw: logic.showLandscapeMode.value
                              ? null
                              : logic.scanW.value,
                          isShowClose: false,
                          btnIsHorizontal: true,
                          btnText2: S.current.cancel,
                          onPressed2: () {
                            AppPage.back();
                          },
                        );
                      } else {
                        AppPage.back();
                      }
                    },
                    child: WxAssets.images.cameraSettingHome.image(
                        width: 32 * logic.scanW.value,
                        height: 32 * logic.scanW.value)),
                WxAssets.images.cameraSetting1080.image(
                    width: 32 * logic.scanW.value,
                    height: 32 * logic.scanW.value),
              ],
            ),
          );
  }

  Widget _buildBottomControls(bool isLandscape) {
    return isLandscape
        ? Column(
            children: [
              if (!logic.isRecording &&
                  logic.operationStep.value == InstructionsSteps.recordingStart)
                InkWell(
                    onTap: () {
                      log('开始录制');
                      logic.toggleRecording();
                    },
                    child: WxAssets.images.recordingIcon.image()),
              if (logic.isRecording)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Opacity(
                      opacity: 0,
                      child: WxAssets.images.finishRecordingIcon.image(),
                    ),
                    InkWell(
                        onTap: () {
                          // print('暂停录制');
                          logic.togglePaused();
                        },
                        child: logic.isPaused
                            ? WxAssets.images.recordingIcon.image()
                            : WxAssets.images.recordingStopIcon.image()),
                    InkWell(
                        onTap: () {
                          getMyDialog(
                            '结束拍摄',
                            S.current.sure,
                            content: '确定要结束拍摄吗？',
                            () {
                              AppPage.back();
                              if (!logic.isPaused) {
                                logic.toggleRecording();
                              }
                              AppPage.returnRoot();
                              final tabVenueLogic = Get.find<TabVenueLogic>();
                              final tabLogic = Get.find<TabLogic>();
                              tabLogic.barOnTap(3);
                              tabVenueLogic.tabController?.animateTo(3);
                            },
                            isShowClose: false,
                            btnIsHorizontal: true,
                            scanw: logic.showLandscapeMode.value
                                ? null
                                : logic.scanW.value,
                            btnText2: S.current.cancel,
                            onPressed2: () {
                              AppPage.back();
                            },
                          );
                        },
                        child: WxAssets.images.finishRecordingIcon.image()),
                  ],
                ).marginSymmetric(horizontal: 20.w),
              SizedBox(
                height: 20.w,
              ),
              Container(
                height: 72.w,
                width: double.infinity,
                alignment: Alignment.center,
                color: Colours.colorCC0F0F16,
                padding: EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight),
                child: logic.isRecording
                    ? Text(
                        logic.recordingTime,
                        style: TextStyles.numberDin16,
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          InkWell(
                            onTap: () {
                              // logic.setGimbalDirection(true);
                              log("!!!!!!!!${logic.gimbalManager.connectionStatus}");
                              logic.setGimbalDirection(true);
                            },
                            child: Text(
                              '半场竖屏',
                              style: logic.showLandscapeMode.value
                                  ? TextStyles.titleSemiBold16
                                  : TextStyles.regular,
                            ),
                          ),
                          SizedBox(
                            width: 20.w,
                          ),
                          InkWell(
                              onTap: () {
                                logic.setGimbalDirection(false);
                              },
                              child: Text(
                                '全场横屏',
                                style: !logic.showLandscapeMode.value
                                    ? TextStyles.titleSemiBold16
                                    : TextStyles.regular,
                              )),
                        ],
                      ),
              ),
            ],
          )
        : Row(
            children: [
              if (!logic.isRecording &&
                  logic.operationStep.value == InstructionsSteps.recordingStart)
                InkWell(
                    onTap: () {
                      log('开始录制');
                      logic.toggleRecording();
                    },
                    child: WxAssets.images.recordingIcon.image()),
              if (logic.isRecording)
                Padding(
                  padding:
                      EdgeInsets.symmetric(vertical: 20 * logic.scanW.value),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                          onTap: () {
                            getMyDialog(
                              '结束拍摄',
                              S.current.sure,
                              content: '确定要结束拍摄吗？',
                              () {
                                AppPage.back();
                                if (!logic.isPaused) {
                                  logic.toggleRecording();
                                }
                                AppPage.returnRoot();
                                final tabVenueLogic = Get.find<TabVenueLogic>();
                                final tabLogic = Get.find<TabLogic>();
                                tabLogic.barOnTap(3);
                                tabVenueLogic.tabController?.animateTo(3);
                              },
                              scanw: logic.showLandscapeMode.value
                                  ? null
                                  : logic.scanW.value,
                              isShowClose: false,
                              btnIsHorizontal: true,
                              btnText2: S.current.cancel,
                              onPressed2: () {
                                AppPage.back();
                              },
                            );
                          },
                          child: WxAssets.images.finishRecordingIcon.image()),
                      InkWell(
                          onTap: () {
                            // print('暂停录制');
                            logic.togglePaused();
                          },
                          child: logic.isPaused
                              ? WxAssets.images.recordingIcon.image()
                              : WxAssets.images.recordingStopIcon.image()),
                      Opacity(
                        opacity: 0,
                        child: WxAssets.images.finishRecordingIcon.image(),
                      ),
                    ],
                  ).marginSymmetric(horizontal: 20 * logic.scanW.value),
                ),
              SizedBox(
                height: 20 * logic.scanW.value,
              ),
              Container(
                height: double.infinity,
                //  width: 72 * logic.scanW.value,
                alignment: Alignment.center,
                color: Colours.colorCC0F0F16,
                padding: EdgeInsets.only(
                    bottom: ScreenUtil().bottomBarHeight,
                    left: 12 * logic.scanW.value,
                    right: 46 * logic.scanW.value),
                child: logic.isRecording
                    ? const SizedBox()
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          InkWell(
                              onTap: () {
                                logic.setGimbalDirection(false);
                              },
                              child: RotatedBox(
                                quarterTurns: 3, // 顺时针旋
                                child: Text(
                                  '全场横屏',
                                  style: !logic.showLandscapeMode.value
                                      ? TextStyles.titleSemiBold16
                                      : TextStyles.regular,
                                ),
                              )),
                          SizedBox(
                            height: 20 * logic.scanW.value,
                          ),
                          InkWell(
                            onTap: () {
                              // logic.setGimbalDirection(true);
                              log("!!!!!!!!${logic.gimbalManager.connectionStatus}");
                              logic.setGimbalDirection(true);
                            },
                            child: RotatedBox(
                              quarterTurns: 3, // 顺时针旋
                              child: Text(
                                '半场竖屏',
                                style: logic.showLandscapeMode.value
                                    ? TextStyles.titleSemiBold16
                                    : TextStyles.regular,
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
            ],
          );
  }
}
