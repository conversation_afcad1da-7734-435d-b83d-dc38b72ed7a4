import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/camera_view_service.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/gimbal_manager.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/gimbal_service.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/tracking_manager.dart';

enum InstructionsSteps {
  effectPreview, //效果预览
  recommendedPosition, //推荐位置示例
  limitPositionsTips, //左右限位说明
  leftLimitSetting, //左限位设置
  rightLimitSetting, //右限位设置
  recordingStart //开始拍摄
}

class NativeCameraScreenLogic extends GetxController {
  late CameraViewService _cameraService;
  // late TrackingManager _trackingManager;
  var operationStep = InstructionsSteps.effectPreview.obs;
  var showSetting = false.obs;
  var showLeftLimitSetting = false.obs;
  var showRightLimitSetting = false.obs;
  // 录制状态
  final _isRecording = false.obs;
  final _isPaused = false.obs;
  final _recordingTime = "00:00:00".obs;
  Duration _totalRecordedDuration = Duration.zero;
  DateTime? _recordingStartTime;
  Timer? _recordingTimer;
  var nextTimeNoPop = false.obs;
  // 回调函数
  VoidCallback? _onBack;

  // Getters
  bool get isRecording => _isRecording.value;
  bool get isPaused => _isPaused.value;
  String get recordingTime => _recordingTime.value;
  // 云台管理器 - 使用共享实例
  GimbalManager get gimbalManager => GimbalService.gimbal;
  // TrackingManager get trackingManager => _trackingManager;
  CameraViewService get cameraService => _cameraService;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
  }

  @override
  void onClose() {
    _recordingTimer?.cancel();
    // 清理方法通道监听器
    _cameraService.setOnBackPressed(null);
    super.onClose();
  }

  void _initializeServices() {
    log("!!!!!!!!!!!_initializeServices");
    // 直接实例化服务
    _cameraService = CameraViewService();
    // _trackingManager = TrackingManager();

    // 初始化相机
    _cameraService.initialize();

    // 初始化方法通道监听
    _cameraService.initializeMethodChannel();
  }

  void setOnBack(VoidCallback? onBack) {
    _onBack = onBack;
    // 设置返回回调
    _cameraService.setOnBackPressed(() {
      print('Back button pressed, calling onBack callback');
      _onBack?.call();
    });
  }

  void refreshMethodChannel() {
    // 每次依赖变化时重新设置监听器，确保回调有效
    _cameraService.setOnBackPressed(() {
      print('Back button pressed in refresh, calling onBack callback');
      _onBack?.call();
    });
    _cameraService.initializeMethodChannel();
  }

  void toggleRecording() {
    if (_isRecording.value) {
      stopRecording();
    } else {
      startRecording();
    }
  }

  void togglePaused() {
    if (_isPaused.value) {
      resumeRecording();
    } else {
      pauseRecording();
    }
  }

  void startRecording() {
    // _cameraService.startRecording();
    _isRecording.value = true;
    _isPaused.value = false;
    _totalRecordedDuration = Duration.zero;
    _recordingStartTime = DateTime.now();
    _pauseStartTime = null;
    _startRecordingTimer();
  }

  void stopRecording() {
    // _cameraService.stopRecording();
    _isRecording.value = false;
    _isPaused.value = false;
    _recordingTime.value = "00:00:00";
    _totalRecordedDuration = Duration.zero;
    _recordingStartTime = null;
    _pauseStartTime = null;
    _stopRecordingTimer();
  }

  void pauseRecording() {
    if (_recordingTimer != null && !_isPaused.value) {
      _stopRecordingTimer();
      _isPaused.value = true;
      _pauseStartTime = DateTime.now();

      // 累计已录制的时间
      if (_recordingStartTime != null) {
        final currentSessionDuration =
            DateTime.now().difference(_recordingStartTime!);
        _totalRecordedDuration += currentSessionDuration;
      }
    }
    // _cameraService.stopRecording();
  }

  void resumeRecording() {
    // _cameraService.startRecording();
    if (_isPaused.value) {
      _isPaused.value = false;
      _recordingStartTime = DateTime.now(); // 重新设置开始时间
      _pauseStartTime = null;
      _startRecordingTimer();
    }
  }

  void _startRecordingTimer() {
    _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_recordingStartTime != null && !_isPaused.value) {
        // 计算当前录制会话的时长
        final currentSessionDuration =
            DateTime.now().difference(_recordingStartTime!);
        // 总时长 = 之前累计的时长 + 当前会话时长
        final totalDuration = _totalRecordedDuration + currentSessionDuration;

        final hours = totalDuration.inHours.toString().padLeft(2, '0');
        final minutes =
            (totalDuration.inMinutes % 60).toString().padLeft(2, '0');
        final seconds =
            (totalDuration.inSeconds % 60).toString().padLeft(2, '0');

        _recordingTime.value = "$hours:$minutes:$seconds";
      }
    });
  }

  void _stopRecordingTimer() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
  }

  // void toggleTracking() {
  // if (_trackingManager.isTracking) {
  //   _trackingManager.stopTracking();
  // } else {
  //   _trackingManager.startTracking();
  // }
  // }

  // ========== 云台控制功能 ==========

  /// 云台居中
  Future<void> gimbalCentering() async {
    await gimbalManager.gimbalCentering();
    Get.log('云台居中');
  }

  /// 云台左转
  Future<void> gimbalTurnLeft() async {
    await gimbalManager.gimbalTurnLeft();
    Get.log('云台左转');
  }

  /// 云台右转
  Future<void> gimbalTurnRight() async {
    await gimbalManager.gimbalTurnRight();
    Get.log('云台右转');
  }

  /// 设置左限位
  Future<void> setLeftLimit() async {
    await gimbalManager.setLeftLimit();
    Get.log('设置左限位');
  }

  /// 设置右限位
  Future<void> setRightLimit() async {
    await gimbalManager.setRightLimit();
    Get.log('设置右限位');
  }

  /// 设置云台方向（横屏/竖屏）
  Future<void> setGimbalDirection(bool isPortrait) async {
    await gimbalManager.setDirection(isPortrait);
  }

  /// 获取云台连接状态
  bool get isGimbalConnected =>
      gimbalManager.connectionStatus.name == 'connected';

  /// 获取云台连接状态流
  Stream get gimbalConnectionStatusStream =>
      gimbalManager.connectionStatusStream;
  void clearScores() {
    // _trackingManager.clearScores();
  }

  void takePhoto() {
    _cameraService.takePhoto();
  }
}
