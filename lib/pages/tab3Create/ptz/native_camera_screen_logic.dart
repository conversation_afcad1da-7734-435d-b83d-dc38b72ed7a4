import 'dart:async';
import 'dart:developer';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/camera_view_service.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/gimbal_manager.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/gimbal_service.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/tracking_manager.dart';

enum InstructionsSteps {
  effectPreview, //效果预览
  recommendedPosition, //推荐位置示例
  limitPositionsTips, //左右限位说明
  leftLimitSetting, //左限位设置
  rightLimitSetting, //右限位设置
  recordingStart //开始拍摄
}

class NativeCameraScreenLogic extends GetxController {
  late CameraViewService _cameraService;
  var showLandscapeMode = true.obs; //true 竖屏  false横屏
  var scanW = 1.0.obs; //屏幕缩放比
  late TrackingManager _trackingManager;
  var operationStep = InstructionsSteps.effectPreview.obs;
  var showSetting = false.obs;
  var showLeftLimitSetting = false.obs;
  var showRightLimitSetting = false.obs;
  // 录制状态
  final _isRecording = false.obs;
  final _isPaused = false.obs;
  final _recordingTime = "00:00:00".obs;
  Duration _totalRecordedDuration = Duration.zero;
  DateTime? _recordingStartTime;
  DateTime? _pauseTime;
  Timer? _recordingTimer;
  var nextTimeNoPop = false.obs;
  // 回调函数
  VoidCallback? _onBack;
  String trainingId = "";
  // Getters
  bool get isRecording => _isRecording.value;
  bool get isPaused => _isPaused.value;
  String get recordingTime => _recordingTime.value;
  // 云台管理器 - 使用共享实例
  GimbalManager get gimbalManager => GimbalService.gimbal;
  TrackingManager get trackingManager => _trackingManager;
  CameraViewService get cameraService => _cameraService;

  @override
  void onInit() async {
    super.onInit();
    scanW.value = 1.w;
    final result = await WxStorage.instance.getBool('rememberCameraShotOption');
    if (result == true) {
      operationStep.value = InstructionsSteps.recommendedPosition;
    }
    _initializeServices();
  }

  @override
  void onClose() {
    _recordingTimer?.cancel();
    // 清理方法通道监听器
    _cameraService.setOnBackPressed(null);
    // 恢复所有方向（退出页面时）
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.onClose();
  }

  void _initializeServices() {
    log("!!!!!!!!!!!_initializeServices");
    // 直接实例化服务
    _cameraService = CameraViewService();
    _trackingManager = TrackingManager();

    // 初始化相机
    _cameraService.initialize();

    // 初始化方法通道监听
    _cameraService.initializeMethodChannel();
  }

  void setOnBack(VoidCallback? onBack) {
    _onBack = onBack;
    // 设置返回回调
    _cameraService.setOnBackPressed(() {
      print('Back button pressed, calling onBack callback');
      _onBack?.call();
    });
  }

  void refreshMethodChannel() {
    // 每次依赖变化时重新设置监听器，确保回调有效
    _cameraService.setOnBackPressed(() {
      print('Back button pressed in refresh, calling onBack callback');
      _onBack?.call();
    });
    _cameraService.initializeMethodChannel();
  }

  void toggleRecording() {
    if (_isRecording.value) {
      stopRecording();
    } else {
      startRecording();
    }
  }

  void togglePaused() {
    if (_isPaused.value) {
      resumeRecording();
    } else {
      pauseRecording();
    }
  }

  Future<void> postStartRecording() async {
    //shootType. 1:半场拍摄， 2:全场拍摄， 3:全场赛事
    //matchId:赛事id 竖屏默认“0”
    Map<String, dynamic> param2 = {"shootType": 1};
    var url = ApiUrl.ptzRecordStart();
    var res = await Api().post(url, data: param2);
    if (res.isSuccessful()) {
      trainingId = res.data["trainingId"].toString();
      log("!!!!!!postStartRecording=$trainingId");
    } else {}
  }

  Future<void> startRecording() async {
    if (_isPaused.value == false) {
      //开始录制
      await postStartRecording();
    }
    _cameraService.startRecording(trainingId);
    _trackingManager.initializeSDK();
    _isRecording.value = true;
    _isPaused.value = false;
    _totalRecordedDuration = Duration.zero;
    _recordingStartTime = DateTime.now();
    _pauseTime = null;
    _startRecordingTimer();
  }

  void stopRecording() {
    _cameraService.stopRecording();
    _isRecording.value = false;
    _isPaused.value = false;
    _recordingTime.value = "00:00:00";
    _totalRecordedDuration = Duration.zero;
    _recordingStartTime = null;
    _pauseTime = null;
    _stopRecordingTimer();
  }

  void pauseRecording() {
    if (_recordingTimer != null && !_isPaused.value) {
      _pauseTime = DateTime.now();
      _isPaused.value = true;
      // 不停止计时器，让它继续运行但在暂停状态下不更新显示
    }
    _cameraService.stopRecording();
  }

  void setPortraitMode(bool isPortrait) {
    _cameraService.setPortraitMode(isPortrait);
  }

  void resumeRecording() {
    _cameraService.startRecording(trainingId);
    if (_isPaused.value && _pauseTime != null) {
      // 计算暂停的时长
      final pauseDuration = DateTime.now().difference(_pauseTime!);
      // 调整录制开始时间，补偿暂停的时长
      if (_recordingStartTime != null) {
        _recordingStartTime = _recordingStartTime!.add(pauseDuration);
      }
      _isPaused.value = false;
      _pauseTime = null;
    }
  }

  void _startRecordingTimer() {
    _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_recordingStartTime != null) {
        if (!_isPaused.value) {
          // 只在非暂停状态下更新显示时间
          final currentDuration =
              DateTime.now().difference(_recordingStartTime!);
          final totalDuration = _totalRecordedDuration + currentDuration;

          final hours = totalDuration.inHours.toString().padLeft(2, '0');
          final minutes =
              (totalDuration.inMinutes % 60).toString().padLeft(2, '0');
          final seconds =
              (totalDuration.inSeconds % 60).toString().padLeft(2, '0');

          _recordingTime.value = "$hours:$minutes:$seconds";
        }
        // 暂停状态下保持当前显示的时间不变
      }
    });
  }

  void _stopRecordingTimer() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
  }

  // void toggleTracking() {
  //   if (_trackingManager.isTracking) {
  //     _trackingManager.stopTracking();
  //   } else {
  //     _trackingManager.startTracking();
  //   }
  // }

  // ========== 云台控制功能 ==========

  /// 云台居中
  Future<void> gimbalCentering() async {
    await gimbalManager.gimbalCentering();
    Get.log('云台居中');
  }

  /// 云台左转
  Future<void> gimbalTurnLeft() async {
    await gimbalManager.gimbalTurnLeft();
    Get.log('云台左转');
  }

  /// 云台右转
  Future<void> gimbalTurnRight() async {
    await gimbalManager.gimbalTurnRight();
    Get.log('云台右转');
  }

  /// 设置左限位
  Future<void> setLeftLimit() async {
    await gimbalManager.setLeftLimit();
    Get.log('设置左限位');
  }

  /// 设置右限位
  Future<void> setRightLimit() async {
    await gimbalManager.setRightLimit();
    Get.log('设置右限位');
  }

  /// 设置云台方向（横屏/竖屏）
  Future<void> setGimbalDirection(bool isPortrait) async {
    await gimbalManager.setDirection(isPortrait);
    showLandscapeMode.value = isPortrait;

    if (isPortrait) {
      // 恢复所有方向（退出页面时）
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    } else {
      // 锁定为横屏
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }
    setCarmerOrientation(isPortrait);
  }

  setCarmerOrientation(bool isPortrait) {
    _cameraService.setPortraitMode(isPortrait);
  }

  /// 获取云台连接状态
  bool get isGimbalConnected =>
      gimbalManager.connectionStatus.name == 'connected';

  /// 获取云台连接状态流
  Stream get gimbalConnectionStatusStream =>
      gimbalManager.connectionStatusStream;
  void clearScores() {
    //_trackingManager.clearScores();
  }

  void takePhoto() {
    _cameraService.takePhoto();
  }
}
