import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/creation_way_logic.dart';
import 'package:shoot_z/pages/tab3Create/ptz/ptz_binding/ptz_binding_logic.dart';
import 'package:shoot_z/pages/tab3Create/ptz/widget/custom_dialog.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:permission_handler/permission_handler.dart';

/// 对外方法：展示 PTZ 绑定面板（底部弹出）
Future<void> showPTZBindingSheet(BuildContext context,
    {bool isSelectMatch = false}) async {
  // 显式创建带 tag 的实例，便于在关闭后正确销毁
  const tag = 'ptz_binding';
  if (!Get.isRegistered<PtzBindingLogic>(tag: tag)) {
    final logic = PtzBindingLogic();
    logic.isSelectMatch = isSelectMatch;
    Get.put<PtzBindingLogic>(logic, tag: tag, permanent: false);
  }
  try {
    await showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      isScrollControlled: true,
      isDismissible: false,
      builder: (context) => _PtzBindingContent(),
    );
  } finally {
    if (Get.isRegistered<PtzBindingLogic>(tag: tag)) {
      Get.delete<PtzBindingLogic>(tag: tag, force: true);
    }
  }
}

class _PtzBindingContent extends StatelessWidget {
  _PtzBindingContent();
  final PtzBindingLogic logic = Get.find<PtzBindingLogic>(tag: 'ptz_binding');
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final step = logic.coursStep.value;
      return SafeArea(
        bottom: true,
        child: BottomFixedPanel(
          showTwoBottomBtn: step == CoursStep.showDevice,
          showBottomBtn: step != CoursStep.connectingPtz,
          showBottomText: step == CoursStep.connectingPtz,
          title: logic.panelTitle(),
          buttonText: logic.mainButtonText(),
          onButtonPressed: logic.onMainButtonPressed,
          leftButtonPressed: () => logic.onLeftButtonPressed(),
          rightButtonPressed: () => logic.onRightButtonPressed(),
          content: _getWidgetWithStatus(context),
        ),
      );
    });
  }

  _getWidgetWithStatus(BuildContext context) {
    switch (logic.coursStep.value) {
      case CoursStep.placePtz:
        return _getWidgetPlacePtz();
      case CoursStep.connectPtz:
        return _getWidgetConnectPtz();
      case CoursStep.openBluetooth:
        return _getWidgetOpenBlueTooth();
      case CoursStep.showDevice:
        return _getWidgetShowDevive();
      case CoursStep.connectingPtz:
        return _getWidgetConnectingPtz();
      case CoursStep.permissionNotEnabled:
        return _getWidgetPermission(context);
      case CoursStep.permissionGranted:
        return _getWidgetPermission(context);
    }
  }

  _getWidgetPlacePtz() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.all(15.w),
          height: 173.w,
          decoration: BoxDecoration(
              border: Border.all(color: const Color(0xffEA0000), width: 2.0.w),
              borderRadius: BorderRadius.circular(20.r)),
          child: const Center(
            child: Text(
              '请将此红色区域卡在云台，保持平衡，摄像头不被遮挡以保证高质量的拍摄效果',
              style: TextStyle(fontSize: 14, color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        SizedBox(height: 20.w),
        Image.asset(
          "assets/images/goal_bg.png",
          width: 186.w,
          height: 105.w,
        ),
        SizedBox(height: 20.w),
        InkWell(
          onTap: () {
            logic.checked.value = !logic.checked.value;
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                logic.checked.value
                    ? Icons.check_circle_outline
                    : Icons.radio_button_unchecked,
                color: Colours.white,
                size: 16,
              ),
              // Image.asset(
              //     "assets/images/ptz_checked.png"
              //         : 'assets/images/choiceNo.png',
              //     width: 16,
              //     height: 16,
              //   ),
              SizedBox(
                width: 4.w,
              ),
              Text(
                '下次不再弹出',
                style: TextStyles.display12.copyWith(color: Colors.white),
                textAlign: TextAlign.center,
              )
            ],
          ),
        )
        // 可以添加更多自定义内容
      ],
    );
  }

  _getWidgetOpenBlueTooth() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 20.w),
        // Image.asset(
        //   "assets/images/goal_bg.png",
        //   width: 160,
        //   height: 160,
        // ),
        SizedBox(
          width: 160.w,
          height: 160.w,
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children: List.generate(logic.imageUrls.length, (index) {
                return AnimatedBuilder(
                    animation: logic.animationController,
                    builder: (context, child) {
                      return Opacity(
                        opacity: index == 0 ? 1 : logic.animations[index].value,
                        child: Image.asset(logic.imageUrls[index]),
                      );
                    });
              }),
            ),
          ),
        ),
        SizedBox(height: 30.w),
        Padding(
          padding: EdgeInsets.only(left: 18.w, right: 18.w, bottom: 25.w),
          child: const Text(
              '如果设备仍连接不上，请打开系统设置，进入“隐私”选择蓝牙，确保球秀APP已打开蓝牙权限然后再次开启手机蓝牙连接设备。',
              style: TextStyle(fontSize: 14, color: Colors.white, height: 1.71),
              textAlign: TextAlign.left),
        )
      ],
    );
  }

  _getWidgetConnectPtz() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 20.w),
        SizedBox(
          width: 160.w,
          height: 160.w,
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children: List.generate(logic.linkImageUrls.length, (index) {
                return AnimatedBuilder(
                    animation: logic.animationController,
                    builder: (context, child) {
                      return Opacity(
                        opacity: index == 0 ? 1 : logic.animations[index].value,
                        child: Image.asset(logic.linkImageUrls[index]),
                      );
                    });
              }),
            ),
          ),
        ),
        SizedBox(height: 30.w),
        Padding(
          padding: EdgeInsets.only(left: 18.w, right: 18.w, bottom: 77.w),
          child: Text('请确定设备已开启，且未被其他手机连接',
              style: TextStyles.display14, textAlign: TextAlign.left),
        )
      ],
    );
  }

  _getWidgetShowDevive() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 20.w),
        Image.asset(
          "assets/images/ptz_device.png",
          width: 72.w,
          height: 222.w,
        ),
        SizedBox(height: 30.w)
      ],
    );
  }

  _getWidgetConnectingPtz() {
    return Stack(
      alignment: Alignment.center,
      children: [
        SizedBox(height: 20.w),
        SizedBox(
          width: 160.w,
          height: 160.w,
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children:
                  List.generate(logic.connectingImageUrls.length, (index) {
                return AnimatedBuilder(
                    animation: logic.animationController,
                    builder: (context, child) {
                      return Opacity(
                        opacity: logic.connectingAnimations[index].value,
                        child: Image.asset(logic.connectingImageUrls[index]),
                      );
                    });
              }),
            ),
          ),
        ),
        Center(
            child: Image.asset(
          "assets/images/ptz_device.png",
          width: 72.w,
          height: 222.w,
        )),
      ],
    );
  }

  _getWidgetPermission(BuildContext context) {
    return Container(
        margin: EdgeInsets.only(left: 40.w, right: 40.w),
        child: Column(
          children: [
            _getWidgetPermisionCellSync(context, 'permision_camera.png', '相机权限',
                '使用相机进行拍摄', logic.cameraOpen, Permission.camera),
            SizedBox(
              height: 20.w,
            ),
            _getWidgetPermisionCellSync(context, 'permision_photo.png',
                '相册完全权限', '读写相册', logic.photoOpen, Permission.photos),
            SizedBox(
              height: 20.w,
            ),
            _getWidgetPermisionCellSync(context, 'permision_micro.png', '麦克风权限',
                '使用麦克风进行声音录制', logic.microOpen, Permission.microphone),
            SizedBox(
              height: 20.w,
            ),
            _getWidgetPermisionCellSync(
                context,
                'permision_location.png',
                '位置权限',
                '记录拍摄的位置信息',
                logic.locationOpen,
                Permission.locationWhenInUse),
            SizedBox(
              height: 10.w,
            ),
          ],
        ));
  }

// 同步版本的权限单元格，避免闪烁
  Widget _getWidgetPermisionCellSync(BuildContext context, String imagePath,
      String title, String desc, RxBool isOpen, Permission permission) {
    // 只在第一次构建时检查权限状态，避免重复调用
    _checkPermissionStatusOnce(permission, isOpen);

    // 直接返回UI，不使用FutureBuilder
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Image.asset(
              'assets/images/$imagePath',
              width: 42.w,
              height: 42.w,
            ),
            SizedBox(
              width: 10.w,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title,
                    style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.bold),
                    textAlign: TextAlign.left),
                Text(desc,
                    style:
                        TextStyle(fontSize: 12.sp, color: Colours.color5C5C6E),
                    textAlign: TextAlign.left)
              ],
            )
          ],
        ),
        Obx(() {
          return InkWell(
            onTap: () {
              if (isOpen.value) {
                openAppSettings();
              } else {
                requestPermission(permission, isOpen);
              }
              if (logic.coursStep.value == CoursStep.permissionNotEnabled) {
                logic.checkPermissionAllOpen();
              }
            },
            child: MyImage(
              isOpen.value ? "switch_on.png" : "switch_off.png",
              fit: BoxFit.fitWidth,
              bgColor: Colors.transparent,
              isAssetImage: true,
              width: 48.w,
            ),
          );
        })
      ],
    );
  }

// 只检查一次权限状态，避免重复调用
  void _checkPermissionStatusOnce(Permission permission, RxBool isOpen) {
    // 使用一个简单的标记来避免重复检查
    if (!logic.permissionChecked.value) {
      _checkAllPermissions();
      logic.permissionChecked.value = true;
    }
  }

// 检查蓝牙权限状态
  void _checkBluetoothPermissions() async {
    // 检查蓝牙权限
    final bluetoothStatus = await Permission.bluetooth.status;
    logic.bluetoothOpen.value = permissionIsOpen(bluetoothStatus);
  }

// 检查所有权限状态
  void _checkAllPermissions() async {
    // 检查相机权限
    final cameraStatus = await Permission.camera.status;
    logic.cameraOpen.value = permissionIsOpen(cameraStatus);

    // 检查相册权限
    final photoStatus = await Permission.photos.status;
    logic.photoOpen.value = permissionIsOpen(photoStatus);

    // 检查麦克风权限
    final microStatus = await Permission.microphone.status;
    logic.microOpen.value = permissionIsOpen(microStatus);

    // 检查位置权限
    final locationStatus = await Permission.locationWhenInUse.status;
    logic.locationOpen.value = permissionIsOpen(locationStatus);

    // 检查是否所有权限都已开启
    logic.checkPermissionAllOpen();

    log('!!!!!!!!!!权限检查完成: camera=${logic.cameraOpen.value}, photo=${logic.photoOpen.value}, micro=${logic.microOpen.value}, location=${logic.locationOpen.value}');
  }

  bool permissionIsOpen(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.denied:
        return false;
      case PermissionStatus.granted:
        return true;
      case PermissionStatus.limited:
        return true;
      default:
        return false;
    }
  }

  Future<void> requestPermission(Permission permission, RxBool isOpen) async {
    final status = await permission.request();
    if (status == PermissionStatus.permanentlyDenied) {
      openAppSettings();
    }
    isOpen.value = permissionIsOpen(status);
    logic.checkPermissionAllOpen();
  }

// 跳转到应用设置页面
  void openAppSettingsPage() async {
    await openAppSettings();
  }
}
