import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shoot_z/pages/tab3Create/creation_way_logic.dart';
import 'package:shoot_z/pages/tab3Create/ptz/model/gimbal_device.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/BluetoothService.dart';

/// PTZ 绑定面板的控制器（独立出来，复用 MyTripodHeadLogic 的状态）
class PtzBindingLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  /// 直接复用页面的逻辑控制器
  final CreationWayLogic head = Get.find<CreationWayLogic>();
  late AnimationController animationController;
  late List<Animation<double>> animations;
  late List<Animation<double>> connectingAnimations;
  var isSelectMatch = false;
  final List<String> imageUrls = [
    'assets/images/blueTooth_gif_01.png',
    'assets/images/blueTooth_gif_02.png',
    'assets/images/blueTooth_gif_03.png',
    'assets/images/blueTooth_gif_04.png',
  ];
  final List<String> linkImageUrls = [
    'assets/images/link_gif_01.png',
    'assets/images/blueTooth_gif_02.png',
    'assets/images/blueTooth_gif_03.png',
    'assets/images/blueTooth_gif_04.png',
  ];
  final List<String> connectingImageUrls = [
    'assets/images/blueTooth_gif_02.png',
    'assets/images/blueTooth_gif_03.png',
    'assets/images/blueTooth_gif_04.png',
  ];
  var checked = false.obs; //选中下次不再弹出
  var cameraOpen = false.obs; //相机权限是否打开
  var photoOpen = false.obs; //相册权限是否打开
  var microOpen = false.obs; //麦克风权限是否打开
  var locationOpen = false.obs; //位置权限是否打开
  var permissionChecked = false.obs; //权限是否已检查过，避免重复检查
  /// 便捷访问器
  Rx<CoursStep> get coursStep => head.coursStep;
  Rxn<GimbalDevice> get discoveredDevices => head.discoveredDevices;
  var bluetoothOpen = false.obs;
  @override
  void onInit() {
    super.onInit();
    _checkBluetoothPermissions();
    animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    // 为每张图片创建间隔动画
    animations = List.generate(4, (index) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: animationController,
          curve: Interval(
            index * 0.1, // 每张图片开始时间
            (index + 1) * 0.1, // 每张图片结束时间
            curve: Curves.easeInOut,
          ),
        ),
      );
    });
    connectingAnimations = List.generate(3, (index) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: animationController,
          curve: Interval(
            index * 0.1, // 每张图片开始时间
            (index + 1) * 0.2, // 每张图片结束时间
            curve: Curves.easeInOut,
          ),
        ),
      );
    });
  }

  /// 检查所有权限是否都已开启
  void checkPermissionAllOpen() {
    if (locationOpen.value &&
        cameraOpen.value &&
        photoOpen.value &&
        microOpen.value) {
      coursStep.value = CoursStep.permissionGranted;
    } else {
      coursStep.value = CoursStep.permissionNotEnabled;
    }
  }

  /// 主按钮点击
  Future<void> onMainButtonPressed() async {
    switch (coursStep.value) {
      case CoursStep.placePtz:
        coursStep.value = bluetoothOpen.value
            ? CoursStep.connectPtz
            : CoursStep.openBluetooth;
        break;
      case CoursStep.connectPtz:
        coursStep.value = CoursStep.showDevice;
        if (discoveredDevices.value == null) {
          head.startScanning();
        }
        break;
      case CoursStep.openBluetooth:
        final status = await Permission.bluetooth.request();
        if (status == PermissionStatus.permanentlyDenied) {
          openAppSettings();
        }
        if (permissionIsOpen(status)) {
          head.coursStep.value = CoursStep.connectPtz;
          if (head.discoveredDevices.value != null) {
            head.startScanning();
          }
        }
        SimpleBluetoothService.enableBluetooth();
        break;
      case CoursStep.showDevice:
        coursStep.value = CoursStep.connectingPtz;
        break;
      case CoursStep.connectingPtz:
        // 忙碌态，不处理
        break;
      case CoursStep.permissionNotEnabled:
        // 打开系统设置
        openAppSettings();
        break;
      case CoursStep.permissionGranted:
        // 进入原生相机页
        AppPage.back();
        if (isSelectMatch) {
          AppPage.to(Routes.nativeCameraScreen);
        } else {
          AppPage.to(Routes.myTripodHead);
        }

        break;
    }
  }

// 检查蓝牙权限状态
  void _checkBluetoothPermissions() async {
    // 检查蓝牙权限
    final bluetoothStatus = await Permission.bluetooth.status;
    bluetoothOpen.value = permissionIsOpen(bluetoothStatus);
  }

  /// 左侧按钮（返回“开始连接”步骤）
  void onLeftButtonPressed() {
    coursStep.value = CoursStep.connectPtz;
  }

  /// 右侧按钮（尝试连接当前发现的设备）
  Future<void> onRightButtonPressed() async {
    coursStep.value = CoursStep.connectingPtz;
    final device = discoveredDevices.value;
    if (device != null) {
      await head.gimbalManager.connectToDevice(device);
    } else {
      WxLoading.showToast('没搜索到设备');
    }
  }

  /// 标题
  String panelTitle() {
    switch (coursStep.value) {
      case CoursStep.placePtz:
        return '教程：放置云台';
      case CoursStep.connectPtz:
        return '连接球秀云台';
      case CoursStep.openBluetooth:
        return '请先开启手机蓝牙';
      case CoursStep.showDevice:
        return discoveredDevices.value?.name ?? '';
      case CoursStep.connectingPtz:
        return discoveredDevices.value?.name ?? '';
      case CoursStep.permissionNotEnabled:
        return '开启云台拍摄前，需以下权限';
      case CoursStep.permissionGranted:
        return '开启云台拍摄前，需以下权限';
    }
  }

  /// 主按钮文案
  String mainButtonText() {
    switch (coursStep.value) {
      case CoursStep.placePtz:
        return '知道了';
      case CoursStep.connectPtz:
        return '开始连接';
      case CoursStep.openBluetooth:
        return '去开启';
      case CoursStep.showDevice:
        return discoveredDevices.value?.name ?? '';
      case CoursStep.connectingPtz:
        return '云台正在连接中...';
      case CoursStep.permissionNotEnabled:
        return '去开启权限';
      case CoursStep.permissionGranted:
        return '开启拍摄';
    }
  }

  bool permissionIsOpen(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.denied:
        return false;
      case PermissionStatus.granted:
        return true;
      case PermissionStatus.limited:
        return true;
      default:
        return false;
    }
  }

  Future<void> requestPermission(Permission permission, RxBool isOpen) async {
    final status = await permission.request();
    if (status == PermissionStatus.permanentlyDenied) {
      openAppSettings();
    }
    isOpen.value = permissionIsOpen(status);
    checkPermissionAllOpen();
  }

  @override
  dispose() {
    log("!!!!!!");
    animationController.stop();
    animationController.dispose();
    super.dispose();
  }
}
