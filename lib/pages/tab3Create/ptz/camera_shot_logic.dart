import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter/widgets.dart' as widgets;
import 'package:path_provider/path_provider.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/gimbal_service.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/gimbal_manager.dart';

/// 屏幕方向模式
enum OrientationMode {
  auto, // 自动
  portrait, // 竖屏锁定
  landscape // 横屏锁定
}

/// 闪光灯模式
enum FlashModeType {
  off, // 关闭
  auto, // 自动
  on, // 常亮
  torch // 手电筒
}

final resolutions = [
  ResolutionPreset.low,
  ResolutionPreset.medium,
  ResolutionPreset.high,
  ResolutionPreset.veryHigh,
  ResolutionPreset.ultraHigh,
  ResolutionPreset.max,
];

final resolutionNames = [
  '360p',
  '480p',
  '720p',
  '1080p',
  '1440p',
  '最高质量',
];

/// 相机拍摄逻辑控制器
class CameraShotLogic extends GetxController
    with widgets.WidgetsBindingObserver {
  static CameraShotLogic get to => Get.find();
  final selectedResolution = ResolutionPreset.high.obs;
  final saveDirectory = ''.obs;
  final isSettingsVisible = false.obs;
  // 相机控制器
  late CameraController _cameraController;
  final recordingDuration = Duration.zero.obs;
  Timer? _recordingTimer;
  // 响应式状态变量
  var isCameraInitialized = false.obs;
  var isRecording = false.obs;
  var isRecordingPaused = false.obs;
  var recordedChunks = <String>[].obs;
  var currentTempPath = ''.obs;
  var isFrontCamera = false.obs;
  var currentFlashMode = FlashModeType.off.obs;
  var zoomLevel = 1.0.obs;

  // 屏幕方向状态
  var orientationMode = OrientationMode.auto.obs;
  var currentOrientation = widgets.Orientation.portrait.obs;

  // 可用摄像头列表
  List<CameraDescription> cameras = [];
  var currentCameraIndex = 0.obs;

  // 最后拍摄的照片路径
  var lastPicturePath = ''.obs;

  // 动画值
  var shutterAnimationValue = 0.0.obs;
  var orientationAnimationValue = 0.0.obs;

  // 错误状态
  var errorMessage = ''.obs;
  var hasError = false.obs;

  // 云台管理器 - 使用共享实例
  GimbalManager get gimbalManager => GimbalService.gimbal;
  @override
  void onInit() async {
    super.onInit();
    widgets.WidgetsBinding.instance.addObserver(this);
    _initializeCamera();
    _printDebugInfo();
    await _initSaveDirectory();
  }

  Future<void> _initSaveDirectory() async {
    final directory = await getApplicationDocumentsDirectory();
    saveDirectory.value = '${directory.path}/videos';
    final dir = Directory(saveDirectory.value);
    if (!await dir.exists()) {
      await dir.create(recursive: true);
    }
  }

  Future<void> changeResolution(ResolutionPreset newResolution) async {
    try {
      await cameraController.dispose();
      selectedResolution.value = newResolution;
      await _initializeCamera();
    } catch (e) {
      log('切换分辨率错误: $e');
      Get.snackbar('错误', '无法切换分辨率: ${e.toString()}');
    }
  }

  Future<void> toggleRecording() async {
    try {
      if (isRecording.value) {
        // 停止录制
        final file = await cameraController.stopVideoRecording();
        _recordingTimer?.cancel();
        isRecording.value = false;
        recordingDuration.value = Duration.zero;

        // 移动文件到指定目录
        final fileName = 'video_${DateTime.now().millisecondsSinceEpoch}.mp4';
        final newPath = '${saveDirectory.value}/$fileName';
        await file.saveTo(newPath);

        Get.snackbar('成功', '视频已保存到: $newPath');
      } else {
        // 开始录制
        final fileName = 'temp_${DateTime.now().millisecondsSinceEpoch}.mp4';
        final filePath = '${saveDirectory.value}/$fileName';
        await cameraController.startVideoRecording();
        isRecording.value = true;
        _startRecordingTimer();
      }
    } catch (e) {
      log('录制错误: $e');
      Get.snackbar('错误', '录制失败: ${e.toString()}');
    }
  }

// 暂停录制
  Future<void> pauseRecording() async {
    if (!isRecording.value || isRecordingPaused.value) return;

    try {
      final file = await cameraController.stopVideoRecording();
      recordedChunks.add(file.path);
      isRecordingPaused.value = true;

      Get.log('录制已暂停');
    } catch (e) {
      Get.snackbar('错误', '暂停录制失败: ${e.toString()}');
    }
  }

// 继续录制
  Future<void> resumeRecording() async {
    if (!isRecording.value || !isRecordingPaused.value) return;

    try {
      final fileName = 'temp_${DateTime.now().millisecondsSinceEpoch}.mp4';
      currentTempPath.value = '${saveDirectory.value}/$fileName';

      await cameraController.startVideoRecording();
      isRecordingPaused.value = false;

      Get.log('录制已继续');
    } catch (e) {
      Get.snackbar('错误', '继续录制失败: ${e.toString()}');
    }
  }

  void _startRecordingTimer() {
    _recordingTimer?.cancel();
    recordingDuration.value = Duration.zero;
    _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      recordingDuration.value += const Duration(seconds: 1);
    });
  }

  @override
  void onReady() {
    super.onReady();
    Get.log('CameraShotLogic is ready');
  }

  @override
  void onClose() {
    widgets.WidgetsBinding.instance.removeObserver(this);
    _disposeCamera();
    _resetOrientation();
    _recordingTimer?.cancel();
    super.onClose();
  }

  /// 打印调试信息
  void _printDebugInfo() {
    Get.log('CameraShotLogic initialized');
    Get.log('Available cameras: ${cameras.length}');
  }

  /// 初始化相机
  Future<void> _initializeCamera() async {
    try {
      hasError.value = false;
      errorMessage.value = '';

      cameras = await availableCameras();
      if (cameras.isEmpty) {
        throw Exception('未找到可用摄像头');
      }

      _cameraController = CameraController(
        cameras.first,
        ResolutionPreset.high,
        enableAudio: true,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      // 监听相机错误
      _cameraController.addListener(() {
        if (_cameraController.value.hasError) {
          _handleError('相机错误: ${_cameraController.value.errorDescription}');
        }
      });

      await _cameraController.initialize();
      isCameraInitialized.value = true;

      Get.log('相机初始化成功');
    } catch (e) {
      _handleError('相机初始化失败: $e');
    }
  }

  /// 处理错误
  void _handleError(String message) {
    hasError.value = true;
    errorMessage.value = message;
    Get.log('Camera Error: $message', isError: true);

    // 显示错误提示
    Get.snackbar(
      '相机错误',
      message,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: Duration(seconds: 3),
    );
  }

  /// 释放相机资源
  void _disposeCamera() {
    if (isCameraInitialized.value) {
      _cameraController.dispose();
      isCameraInitialized.value = false;
    }
  }

  /// 重置屏幕方向
  void _resetOrientation() {
    SystemChrome.setPreferredOrientations(DeviceOrientation.values);
  }

  @override
  void didChangeMetrics() {
    _updateOrientation();
  }

  /// 更新屏幕方向
  void _updateOrientation() {
    final view = widgets.WidgetsBinding.instance.platformDispatcher.views.first;
    final newOrientation = widgets.MediaQueryData.fromView(view).orientation;

    if (orientationMode.value == OrientationMode.auto &&
        newOrientation != currentOrientation.value) {
      currentOrientation.value = newOrientation;
      Get.log(
          '屏幕方向更新: ${newOrientation == widgets.Orientation.portrait ? '竖屏' : '横屏'}');
    }
  }

  // ========== 拍照功能 ==========

  /// 拍摄照片
  Future<void> takePicture() async {
    if (!isCameraInitialized.value || isRecording.value) {
      Get.log('相机未就绪，无法拍照');
      return;
    }

    try {
      _playShutterAnimation();

      // 延迟一下让动画可见
      await Future.delayed(Duration(milliseconds: 100));

      final XFile picture = await _cameraController.takePicture();
      lastPicturePath.value = picture.path;

      Get.log('照片拍摄成功: ${picture.path}');

      // 导航到预览页面
      Get.toNamed('/preview', arguments: picture.path);
    } catch (e) {
      _handleError('拍照失败: $e');
    }
  }

  /// 播放快门动画
  void _playShutterAnimation() {
    shutterAnimationValue.value = 0.0;
    final timer = Timer.periodic(Duration(milliseconds: 16), (timer) {
      shutterAnimationValue.value += 0.1;
      if (shutterAnimationValue.value >= 1.0) {
        timer.cancel();
        Future.delayed(Duration(milliseconds: 100), () {
          shutterAnimationValue.value = 0.0;
        });
      }
    });
  }

  // ========== 相机控制功能 ==========

  /// 切换摄像头
  Future<void> switchCamera() async {
    if (!isCameraInitialized.value || cameras.length < 2) {
      Get.log('无法切换摄像头: ${cameras.length < 2 ? "只有一个摄像头" : "相机未初始化"}');
      return;
    }

    try {
      isCameraInitialized.value = false;
      await _cameraController.dispose();

      currentCameraIndex.value =
          (currentCameraIndex.value + 1) % cameras.length;
      isFrontCamera.value = cameras[currentCameraIndex.value].lensDirection ==
          CameraLensDirection.front;

      _cameraController = CameraController(
        cameras[currentCameraIndex.value],
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _cameraController.initialize();
      isCameraInitialized.value = true;

      // 恢复之前的闪光灯设置
      _applyFlashMode();

      Get.log('摄像头切换成功: ${isFrontCamera.value ? "前置" : "后置"}');
    } catch (e) {
      _handleError('切换摄像头失败: $e');
      // 尝试恢复之前的摄像头
      await _recoverCamera();
    }
  }

  /// 恢复相机
  Future<void> _recoverCamera() async {
    try {
      currentCameraIndex.value = 0;
      isFrontCamera.value = false;

      _cameraController = CameraController(
        cameras.first,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _cameraController.initialize();
      isCameraInitialized.value = true;
    } catch (e) {
      _handleError('相机恢复失败: $e');
    }
  }

  /// 切换闪光灯模式
  void toggleFlash() {
    if (!isCameraInitialized.value) return;

    switch (currentFlashMode.value) {
      case FlashModeType.off:
        currentFlashMode.value = FlashModeType.auto;
        break;
      case FlashModeType.auto:
        currentFlashMode.value = FlashModeType.on;
        break;
      case FlashModeType.on:
        currentFlashMode.value = FlashModeType.torch;
        break;
      case FlashModeType.torch:
        currentFlashMode.value = FlashModeType.off;
        break;
    }

    _applyFlashMode();
    Get.log('闪光灯模式切换: ${currentFlashMode.value}');
  }

  /// 应用闪光灯模式
  void _applyFlashMode() {
    if (!isCameraInitialized.value) return;

    FlashMode mode;
    switch (currentFlashMode.value) {
      case FlashModeType.off:
        mode = FlashMode.off;
        break;
      case FlashModeType.auto:
        mode = FlashMode.auto;
        break;
      case FlashModeType.on:
        mode = FlashMode.always;
        break;
      case FlashModeType.torch:
        mode = FlashMode.torch;
        break;
    }

    _cameraController.setFlashMode(mode);
  }

  // ========== 屏幕方向控制 ==========

  /// 切换屏幕方向模式
  Future<void> toggleOrientationMode() async {
    // _playOrientationAnimation();
    log("!!!!!!!${orientationMode.value}");
    switch (orientationMode.value) {
      case OrientationMode.auto:
        await _setPortraitLock();
        break;
      case OrientationMode.portrait:
        await _setLandscapeLock();
        break;
      case OrientationMode.landscape:
        await _setAutoOrientation();
        break;
    }

    Get.log('屏幕方向模式切换: ${orientationMode.value}');
  }

  /// 设置竖屏锁定
  Future<void> _setPortraitLock() async {
    orientationMode.value = OrientationMode.portrait;
    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    currentOrientation.value = widgets.Orientation.portrait;
  }

  /// 设置横屏锁定
  Future<void> _setLandscapeLock() async {
    orientationMode.value = OrientationMode.landscape;
    await SystemChrome.setPreferredOrientations(
        [DeviceOrientation.landscapeLeft]);
    currentOrientation.value = widgets.Orientation.landscape;
  }

  /// 设置自动方向
  Future<void> _setAutoOrientation() async {
    orientationMode.value = OrientationMode.auto;
    await SystemChrome.setPreferredOrientations(DeviceOrientation.values);
  }

  /// 播放方向切换动画
  void _playOrientationAnimation() {
    orientationAnimationValue.value = 0.0;
    final timer = Timer.periodic(Duration(milliseconds: 16), (timer) {
      orientationAnimationValue.value += 0.1;
      if (orientationAnimationValue.value >= 1.0) {
        timer.cancel();
        orientationAnimationValue.value = 0.0;
      }
    });
  }

  // ========== 工具方法 ==========

  /// 获取闪光灯图标
  String get flashIcon {
    switch (currentFlashMode.value) {
      case FlashModeType.off:
        return '🔅';
      case FlashModeType.auto:
        return '⚡';
      case FlashModeType.on:
        return '🔦';
      case FlashModeType.torch:
        return '💡';
    }
  }

  /// 获取方向锁定图标
  String get orientationIcon {
    switch (orientationMode.value) {
      case OrientationMode.auto:
        return '🔄';
      case OrientationMode.portrait:
        return '📱';
      case OrientationMode.landscape:
        return '🖥️';
    }
  }

  /// 获取方向锁定提示文本
  String get orientationTooltip {
    switch (orientationMode.value) {
      case OrientationMode.auto:
        return '自动旋转';
      case OrientationMode.portrait:
        return '竖屏锁定';
      case OrientationMode.landscape:
        return '横屏锁定';
    }
  }

  /// 获取相机控制器（供UI使用）
  CameraController get cameraController => _cameraController;

  /// 检查是否横屏
  bool get isLandscape =>
      currentOrientation.value == widgets.Orientation.landscape;

  /// 检查是否有多个摄像头
  bool get hasMultipleCameras => cameras.length > 1;

  /// 重新加载相机
  Future<void> reloadCamera() async {
    _disposeCamera();
    await _initializeCamera();
  }
  // ========== 云台控制功能 ==========

  /// 云台居中
  Future<void> gimbalCentering() async {
    await gimbalManager.gimbalCentering();
    Get.log('云台居中');
  }

  /// 云台左转
  Future<void> gimbalTurnLeft() async {
    await gimbalManager.gimbalTurnLeft();
    Get.log('云台左转');
  }

  /// 云台右转
  Future<void> gimbalTurnRight() async {
    await gimbalManager.gimbalTurnRight();
    Get.log('云台右转');
  }

  /// 设置左限位
  Future<void> setLeftLimit() async {
    await gimbalManager.setLeftLimit();
    Get.log('设置左限位');
  }

  /// 设置右限位
  Future<void> setRightLimit() async {
    await gimbalManager.setRightLimit();
    Get.log('设置右限位');
  }

  /// 设置云台方向（横屏/竖屏）
  Future<void> setGimbalDirection(bool isPortrait) async {
    await gimbalManager.setDirection(isPortrait);
  }

  /// 获取云台连接状态
  bool get isGimbalConnected =>
      gimbalManager.connectionStatus.name == 'connected';

  /// 获取云台连接状态流
  Stream get gimbalConnectionStatusStream =>
      gimbalManager.connectionStatusStream;
}
