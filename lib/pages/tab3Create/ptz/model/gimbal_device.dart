/// 云台设备模型
class GimbalDevice {
  final String name;
  final String macAddress;
  final String serial;
  final int rssi;
  final bool isConnected;

  const GimbalDevice({
    required this.name,
    required this.macAdd<PERSON>,
    required this.serial,
    required this.rssi,
    this.isConnected = false,
  });

  factory GimbalDevice.fromMap(Map<String, dynamic> map) {
    return GimbalDevice(
      name: map['name'] ?? '',
      macAddress: map['macAddress'] ?? '',
      serial: map['serial'] ?? '',
      rssi: map['rssi'] ?? 0,
      isConnected: map['isConnected'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'macAddress': macAddress,
      'serial': serial,
      'rssi': rssi,
      'isConnected': isConnected,
    };
  }

  GimbalDevice copyWith({
    String? name,
    String? macAddress,
    String? serial,
    int? rssi,
    bool? isConnected,
  }) {
    return GimbalDevice(
      name: name ?? this.name,
      macAddress: macAddress ?? this.macAddress,
      serial: serial ?? this.serial,
      rssi: rssi ?? this.rssi,
      isConnected: isConnected ?? this.isConnected,
    );
  }

  @override
  String toString() {
    return 'GimbalDevice(name: $name, macAddress: $macAddress, serial: $serial, rssi: $rssi, isConnected: $isConnected)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GimbalDevice &&
        other.name == name &&
        other.macAddress == macAddress &&
        other.serial == serial &&
        other.rssi == rssi &&
        other.isConnected == isConnected;
  }

  @override
  int get hashCode {
    return name.hashCode ^
        macAddress.hashCode ^
        serial.hashCode ^
        rssi.hashCode ^
        isConnected.hashCode;
  }
}
