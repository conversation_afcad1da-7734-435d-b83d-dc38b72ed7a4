/// 篮球追踪统计信息
class TrackingStats {
  final int shotCount;
  final int madeCount;
  final int missedCount;
  final bool isTracking;
  final String trackingMessage;

  const TrackingStats({
    this.shotCount = 0,
    this.madeCount = 0,
    this.missedCount = 0,
    this.isTracking = false,
    this.trackingMessage = '',
  });

  double get accuracy {
    if (shotCount == 0) return 0.0;
    return (madeCount / shotCount) * 100.0;
  }

  String get statsText {
    return '投篮: $shotCount | 命中: $madeCount | 未中: $missedCount | 命中率: ${accuracy.toStringAsFixed(1)}%';
  }

  TrackingStats copyWith({
    int? shotCount,
    int? madeCount,
    int? missedCount,
    bool? isTracking,
    String? trackingMessage,
  }) {
    return TrackingStats(
      shotCount: shotCount ?? this.shotCount,
      madeCount: madeCount ?? this.madeCount,
      missedCount: missedCount ?? this.missedCount,
      isTracking: isTracking ?? this.isTracking,
      trackingMessage: trackingMessage ?? this.trackingMessage,
    );
  }

  @override
  String toString() {
    return 'TrackingStats(shotCount: $shotCount, madeCount: $madeCount, missedCount: $missedCount, isTracking: $isTracking, trackingMessage: $trackingMessage)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrackingStats &&
        other.shotCount == shotCount &&
        other.madeCount == madeCount &&
        other.missedCount == missedCount &&
        other.isTracking == isTracking &&
        other.trackingMessage == trackingMessage;
  }

  @override
  int get hashCode {
    return shotCount.hashCode ^
        madeCount.hashCode ^
        missedCount.hashCode ^
        isTracking.hashCode ^
        trackingMessage.hashCode;
  }
}
