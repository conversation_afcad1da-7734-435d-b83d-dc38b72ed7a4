import 'package:flutter/material.dart';

/// 云台连接状态枚举
enum ConnectionStatus {
  disconnected,
  connecting,
  connected,
  error,
}

extension ConnectionStatusExtension on ConnectionStatus {
  String get description {
    switch (this) {
      case ConnectionStatus.disconnected:
        return '未连接';
      case ConnectionStatus.connecting:
        return '连接中...';
      case ConnectionStatus.connected:
        return '已连接';
      case ConnectionStatus.error:
        return '连接错误';
    }
  }

  String get statusText {
    switch (this) {
      case ConnectionStatus.disconnected:
        return 'disconnected';
      case ConnectionStatus.connecting:
        return 'connecting';
      case ConnectionStatus.connected:
        return 'connected';
      case ConnectionStatus.error:
        return 'error';
    }
  }

  Color get color {
    switch (this) {
      case ConnectionStatus.disconnected:
        return Colors.grey;
      case ConnectionStatus.connecting:
        return Colors.orange;
      case ConnectionStatus.connected:
        return Colors.green;
      case ConnectionStatus.error:
        return Colors.red;
    }
  }
}

/// 权限状态枚举
enum PermissionStatus {
  notDetermined,
  denied,
  granted,
  restricted,
}

extension PermissionStatusExtension on PermissionStatus {
  String get description {
    switch (this) {
      case PermissionStatus.notDetermined:
        return '未询问';
      case PermissionStatus.denied:
        return '已拒绝';
      case PermissionStatus.granted:
        return '已允许';
      case PermissionStatus.restricted:
        return '受限制';
    }
  }

  String get statusText {
    switch (this) {
      case PermissionStatus.notDetermined:
        return 'notDetermined';
      case PermissionStatus.denied:
        return 'denied';
      case PermissionStatus.granted:
        return 'granted';
      case PermissionStatus.restricted:
        return 'restricted';
    }
  }
}
