import 'dart:developer';

import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/ptz/config/ptz_dependencies.dart';
import 'package:shoot_z/pages/tab3Create/ptz/my_tripod_head_logic.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/gimbal_service.dart';

/// MyTripodHead 页面的依赖绑定
class MyTripodHeadBinding extends Bindings {
  @override
  void dependencies() async {
    log('!~!!!!!!MyTripodHeadBinding');
    // 确保 PTZ 依赖已初始化
    if (!Get.isRegistered<GimbalService>()) {
      log('GimbalService not registered, initializing...');
      // 直接注册 GimbalService，因为 main.dart 中的异步初始化可能还没完成
      Get.put<GimbalService>(GimbalService(), permanent: true);
    }

    // 注册控制器
    Get.lazyPut(() => MyTripodHeadLogic());
  }
}
