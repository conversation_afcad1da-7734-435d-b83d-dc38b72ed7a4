import 'dart:async';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shoot_z/pages/tab3Create/ptz/model/connection_status.dart';
import 'package:shoot_z/pages/tab3Create/ptz/model/gimbal_device.dart';

/// 云台管理器类
/// 负责蓝牙连接、设备搜索、云台控制等功能
class GimbalManager extends ChangeNotifier {
  static const MethodChannel _channel = MethodChannel('gimbal_manager');

  // 状态流控制器
  final StreamController<ConnectionStatus> _connectionStatusController =
      StreamController<ConnectionStatus>.broadcast();
  final StreamController<List<GimbalDevice>> _discoveredDevicesController =
      StreamController<List<GimbalDevice>>.broadcast();
  final StreamController<String> _errorMessageController =
      StreamController<String>.broadcast();

  // 当前状态
  ConnectionStatus _connectionStatus = ConnectionStatus.disconnected;
  List<GimbalDevice> _discoveredDevices = [];
  GimbalDevice? _connectedDevice;
  String _errorMessage = '';
  bool _isScanning = false;

  // Getters
  ConnectionStatus get connectionStatus => _connectionStatus;
  List<GimbalDevice> get discoveredDevices => _discoveredDevices;
  GimbalDevice? get connectedDevice => _connectedDevice;
  String get errorMessage => _errorMessage;
  bool get isScanning => _isScanning;

  // 流
  Stream<ConnectionStatus> get connectionStatusStream =>
      _connectionStatusController.stream;
  Stream<List<GimbalDevice>> get discoveredDevicesStream =>
      _discoveredDevicesController.stream;
  Stream<String> get errorMessageStream => _errorMessageController.stream;

  GimbalManager() {
    _setupMethodChannel();
  }

  void _setupMethodChannel() {
    _channel.setMethodCallHandler((call) async {
      log("!!!!!!!!!!222=====${call.method}${call.arguments}");
      switch (call.method) {
        case 'onConnectionStatusChanged':
          _handleConnectionStatusChanged(call.arguments);
          break;
        case 'onDeviceFound':
          log("!!!!!!!!!!${call.arguments}");
          _handleDeviceFound(call.arguments);
          break;
        case 'onError':
          _handleError(call.arguments);
          break;
        case 'onDeviceStateChanged':
          _handleDeviceStateChanged(call.arguments);
          break;
        default:
          log('Unknown method call: ${call.method}');
      }
    });
  }

  void _handleConnectionStatusChanged(dynamic arguments) {
    final statusString = arguments['status'] as String? ?? 'disconnected';
    final message = arguments['message'] as String? ?? '';
    log("!!!!_handleConnectionStatusChanged$statusString===$message");
    ConnectionStatus newStatus;
    switch (statusString) {
      case 'connected':
        newStatus = ConnectionStatus.connected;
        break;
      case 'connecting':
        newStatus = ConnectionStatus.connecting;
        break;
      case 'error':
        newStatus = ConnectionStatus.error;
        break;
      default:
        newStatus = ConnectionStatus.disconnected;
    }

    _connectionStatus = newStatus;
    _errorMessage = message;

    _connectionStatusController.add(newStatus);
    if (message.isNotEmpty) {
      _errorMessageController.add(message);
    }
    notifyListeners();

    log('Connection status changed: $newStatus - $message');
  }

  void _handleDeviceFound(dynamic arguments) {
    try {
      final List<dynamic> deviceList =
          arguments['devices'] as List<dynamic>? ?? [];
      final List<GimbalDevice> devices = deviceList
          .map((device) =>
              GimbalDevice.fromMap(Map<String, dynamic>.from(device)))
          .toList();

      _discoveredDevices = devices;
      _discoveredDevicesController.add(devices);
      notifyListeners();

      log('Found ${devices.length} devices');
    } catch (e) {
      log('Error parsing device list: $e');
    }
  }

  void _handleError(dynamic arguments) {
    final message = arguments['message'] as String? ?? 'Unknown error';
    _errorMessage = message;
    _errorMessageController.add(message);

    log('Gimbal error: $message');
  }

  void _handleDeviceStateChanged(dynamic arguments) {
    final state = arguments['state'] as String? ?? '';
    final message = arguments['message'] as String? ?? '';

    log('Device state changed: $state - $message');

    // 处理设备状态变化，如低电量、过载等
    if (message.isNotEmpty) {
      _errorMessage = message;
      _errorMessageController.add(message);
    }
  }

  /// 开始扫描云台设备
  Future<void> startScanning() async {
    if (_isScanning) return;

    try {
      _isScanning = true;
      _discoveredDevices.clear();
      _errorMessage = '';
      _discoveredDevicesController.add(_discoveredDevices);
      notifyListeners();

      log('Started scanning for gimbal devices');

      // 使用iOS原生蓝牙扫描
      await _channel.invokeMethod('startScanning');

      // 30秒后自动停止扫描
      Timer(const Duration(seconds: 30), () {
        if (_isScanning) {
          stopScanning();
        }
      });
    } catch (e) {
      _isScanning = false;
      _errorMessage = '扫描失败: $e';
      _errorMessageController.add(_errorMessage);
      notifyListeners();
      log('Error starting scan: $e');
    }
  }

  /// 判断是否为云台设备
  bool _isGimbalDevice(String name, String address) {
    final lowerName = name.toLowerCase();
    final lowerAddress = address.toLowerCase();

    // 根据设备名称或MAC地址特征判断是否为云台设备
    return lowerName.contains('gimbal') ||
        lowerName.contains('云台') ||
        lowerName.contains('camera') ||
        lowerName.contains('stabilizer') ||
        lowerAddress.startsWith('aa:bb:cc') || // 云台设备的MAC地址前缀
        lowerAddress.startsWith('00:11:22'); // 另一个云台设备MAC地址前缀
  }

  /// 停止扫描云台设备
  Future<void> stopScanning() async {
    if (!_isScanning) return;

    try {
      await _channel.invokeMethod('stopScanning');
      _isScanning = false;
      notifyListeners();
      log('Stopped scanning for gimbal devices');
    } catch (e) {
      log('Error stopping scan: $e');
    }
  }

  /// 连接到指定设备
  Future<void> connectToDevice(GimbalDevice device) async {
    log("!!!!!connectToDevice$_connectionStatus");
    if (_connectionStatus == ConnectionStatus.connecting) return;
    log("!!!!!1111");
    try {
      await stopScanning();
      _connectionStatus = ConnectionStatus.connecting;
      _connectedDevice = device;
      _connectionStatusController.add(_connectionStatus);
      notifyListeners();

      log('Connecting to device: ${device.name}');

      // 使用iOS原生蓝牙连接
      await _channel.invokeMethod('connectToDevice', {
        'name': device.name,
        'macAddress': device.macAddress,
        'serial': device.serial,
      });
    } catch (e) {
      _connectionStatus = ConnectionStatus.error;
      _errorMessage = '连接失败: $e';
      _connectionStatusController.add(_connectionStatus);
      _errorMessageController.add(_errorMessage);
      notifyListeners();
      log('Error connecting to device: $e');
    }
  }

  /// 断开连接
  Future<void> disconnect() async {
    try {
      await _channel.invokeMethod('disconnect');
      _connectionStatus = ConnectionStatus.disconnected;
      _connectedDevice = null;
      _errorMessage = '';
      _connectionStatusController.add(_connectionStatus);
      _errorMessageController.add('');
      notifyListeners();

      log('Disconnected from gimbal');
    } catch (e) {
      log('Error disconnecting: $e');
    }
  }

  // MARK: - 云台控制方法

  /// 云台居中
  Future<void> gimbalCentering() async {
    if (_connectionStatus != ConnectionStatus.connected) return;

    try {
      await _channel.invokeMethod('gimbalCentering');
      log('Gimbal centering');
    } catch (e) {
      log('Error centering gimbal: $e');
    }
  }

  /// 云台左转
  Future<void> gimbalTurnLeft() async {
    if (_connectionStatus != ConnectionStatus.connected) return;

    try {
      await _channel.invokeMethod('gimbalTurnLeft');
      log('Gimbal turn left');
    } catch (e) {
      log('Error turning gimbal left: $e');
    }
  }

  /// 云台右转
  Future<void> gimbalTurnRight() async {
    if (_connectionStatus != ConnectionStatus.connected) return;

    try {
      await _channel.invokeMethod('gimbalTurnRight');
      log('Gimbal turn right');
    } catch (e) {
      log('Error turning gimbal right: $e');
    }
  }

  /// 设置云台方向（横屏/竖屏）
  Future<void> setDirection(bool isPortrait) async {
    if (_connectionStatus != ConnectionStatus.connected) return;

    try {
      await _channel.invokeMethod('setDirection', {'isPortrait': isPortrait});
      log('Set gimbal direction: ${isPortrait ? 'portrait' : 'landscape'}');
    } catch (e) {
      log('Error setting gimbal direction: $e');
    }
  }

  // MARK: - 限位设置方法

  /// 设置左限位
  Future<void> setLeftLimit() async {
    if (_connectionStatus != ConnectionStatus.connected) return;

    try {
      await _channel.invokeMethod('setLeftLimit');
      log('Set left limit');
    } catch (e) {
      log('Error setting left limit: $e');
    }
  }

  /// 设置右限位
  Future<void> setRightLimit() async {
    if (_connectionStatus != ConnectionStatus.connected) return;

    try {
      await _channel.invokeMethod('setRightLimit');
      log('Set right limit');
    } catch (e) {
      log('Error setting right limit: $e');
    }
  }

  /// 移动到左限位
  Future<void> goToLeftLimit() async {
    if (_connectionStatus != ConnectionStatus.connected) return;

    try {
      await _channel.invokeMethod('goToLeftLimit');
      log('Go to left limit');
    } catch (e) {
      log('Error going to left limit: $e');
    }
  }

  /// 移动到右限位
  Future<void> goToRightLimit() async {
    if (_connectionStatus != ConnectionStatus.connected) return;

    try {
      await _channel.invokeMethod('goToRightLimit');
      log('Go to right limit');
    } catch (e) {
      log('Error going to right limit: $e');
    }
  }

  /// 启用/停用限位
  Future<void> enableLimit(bool enable) async {
    if (_connectionStatus != ConnectionStatus.connected) return;

    try {
      await _channel.invokeMethod('enableLimit', {'enable': enable});
      log('${enable ? 'Enable' : 'Disable'} limit');
    } catch (e) {
      log('Error ${enable ? 'enabling' : 'disabling'} limit: $e');
    }
  }

  /// 清理资源
  @override
  void dispose() {
    super.dispose();
    _connectionStatusController.close();
    _discoveredDevicesController.close();
    _errorMessageController.close();
  }
}
