import 'dart:developer';

import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/gimbal_manager.dart';

/// GimbalManager 的 GetX 服务包装器
/// 确保在整个应用中只有一个 GimbalManager 实例
class GimbalService extends GetxService {
  late final GimbalManager _gimbalManager;

  /// 获取 GimbalManager 实例
  GimbalManager get gimbalManager => _gimbalManager;

  @override
  void onInit() {
    super.onInit();
    log("!!!!!!!GimbalServiceonInit");
    _gimbalManager = GimbalManager();
  }

  @override
  void onClose() {
    _gimbalManager.dispose();
    super.onClose();
  }

  /// 静态方法，方便获取实例
  static GimbalService get to => Get.find<GimbalService>();

  /// 静态方法，直接获取 GimbalManager
  static GimbalManager get gimbal => Get.find<GimbalService>().gimbalManager;
}
