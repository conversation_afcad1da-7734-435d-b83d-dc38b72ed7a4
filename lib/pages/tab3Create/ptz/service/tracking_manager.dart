import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shoot_z/pages/tab3Create/ptz/model/tracking_stats.dart';

/// 篮球追踪管理器类
/// 负责篮球追踪、投篮统计等功能
class TrackingManager extends ChangeNotifier {
  static const MethodChannel _channel = MethodChannel('tracking_manager');

  // 状态流控制器
  final StreamController<TrackingStats> _trackingStatsController =
      StreamController<TrackingStats>.broadcast();

  // 当前追踪状态
  TrackingStats _trackingStats = const TrackingStats();
  bool _isSDKAvailable = false;

  // Getters
  TrackingStats get trackingStats => _trackingStats;
  bool get isTracking => _trackingStats.isTracking;
  int get shotCount => _trackingStats.shotCount;
  int get madeCount => _trackingStats.madeCount;
  int get missedCount => _trackingStats.missedCount;
  String get trackingMessage => _trackingStats.trackingMessage;
  bool get isSDKAvailable => _isSDKAvailable;

  // 流
  Stream<TrackingStats> get trackingStatsStream =>
      _trackingStatsController.stream;

  TrackingManager() {
    _setupMethodChannel();
  }

  void _setupMethodChannel() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onTrackingEvent':
          _handleTrackingEvent(call.arguments);
          break;
        case 'onSDKStatusChanged':
          _handleSDKStatusChanged(call.arguments);
          break;
        default:
          print('Unknown tracking method call: ${call.method}');
      }
    });
  }

  void _handleTrackingEvent(dynamic arguments) {
    final String eventType = arguments['eventType'] as String? ?? '';
    final int score = arguments['score'] as int? ?? 0;
    // final String message = arguments['message'] as String? ?? '';

    print('Tracking event: $arguments $eventType, score: $score,');

    // 根据事件类型更新统计信息
    switch (eventType) {
      case 'shot':
        _trackingStats = _trackingStats.copyWith(
          shotCount: score,
          trackingMessage: '投篮数: $score',
        );
        break;
      case 'made':
        _trackingStats = _trackingStats.copyWith(
          madeCount: score,
          trackingMessage: '命中数: $score',
        );
        break;
      case 'missed':
        _trackingStats = _trackingStats.copyWith(
          missedCount: score,
          trackingMessage: '未中数: $score',
        );
        break;
      default:
        _trackingStats = _trackingStats.copyWith(
          trackingMessage: "",
        );
    }

    _trackingStatsController.add(_trackingStats);
    notifyListeners();
  }

  void _handleSDKStatusChanged(dynamic arguments) {
    final bool available = arguments['available'] as bool? ?? false;
    _isSDKAvailable = available;
    print('SDK status changed: $available');
  }

  /// 初始化SDK
  Future<void> initializeSDK() async {
    try {
      await _channel.invokeMethod('initializeSDK');
      _isSDKAvailable = true;
      print('Tracking SDK initialized');
    } catch (e) {
      _isSDKAvailable = false;
      print('Failed to initialize tracking SDK: $e');
    }
  }

  // /// 开始篮球追踪
  // Future<void> startTracking() async {
  //   if (_trackingStats.isTracking) return;

  //   try {
  //     _trackingStats = _trackingStats.copyWith(
  //       isTracking: true,
  //       trackingMessage: '正在启动篮球追踪...',
  //     );
  //     _trackingStatsController.add(_trackingStats);

  //     await _channel.invokeMethod('startTracking');

  //     // 延迟更新状态，模拟SDK初始化
  //     Timer(const Duration(milliseconds: 200), () {
  //       _trackingStats = _trackingStats.copyWith(
  //         trackingMessage: _isSDKAvailable ? '篮球追踪已开启' : '追踪模式（SDK 不可用）',
  //       );
  //       _trackingStatsController.add(_trackingStats);
  //     });

  //     print('Started basketball tracking');
  //   } catch (e) {
  //     _trackingStats = _trackingStats.copyWith(
  //       isTracking: false,
  //       trackingMessage: '追踪启动失败',
  //     );
  //     _trackingStatsController.add(_trackingStats);
  //     print('Error starting tracking: $e');
  //   }
  // }

  // /// 停止篮球追踪
  // Future<void> stopTracking() async {
  //   if (!_trackingStats.isTracking) return;

  //   try {
  //     await _channel.invokeMethod('stopTracking');

  //     _trackingStats = _trackingStats.copyWith(
  //       isTracking: false,
  //       trackingMessage: '篮球追踪已关闭',
  //     );
  //     _trackingStatsController.add(_trackingStats);

  //     print('Stopped basketball tracking');
  //   } catch (e) {
  //     print('Error stopping tracking: $e');
  //   }
  // }

  /// 清零得分
  Future<void> clearScore() async {
    try {
      await _channel.invokeMethod('clearScore');

      _trackingStats = TrackingStats(
        isTracking: _trackingStats.isTracking,
        trackingMessage: '得分已清零',
      );
      _trackingStatsController.add(_trackingStats);

      print('Cleared tracking score');
    } catch (e) {
      print('Error clearing score: $e');
    }
  }

  /// 清除所有分数（别名方法）
  Future<void> clearScores() async {
    await clearScore();
  }

  /// 处理视频帧（用于篮球追踪）
  Future<void> handleFrame(dynamic pixelBuffer) async {
    if (!_trackingStats.isTracking || !_isSDKAvailable) return;

    try {
      await _channel.invokeMethod('handleFrame', {'pixelBuffer': pixelBuffer});
    } catch (e) {
      print('Error handling frame: $e');
    }
  }

  /// 设置摄像头类型
  Future<void> setCameraType({required bool isFrontCamera}) async {
    try {
      await _channel
          .invokeMethod('setCameraType', {'isFrontCamera': isFrontCamera});
      print('Set camera type: ${isFrontCamera ? 'front' : 'back'}');
    } catch (e) {
      print('Error setting camera type: $e');
    }
  }

  /// 获取统计信息文本
  String getStatsText() {
    return _trackingStats.statsText;
  }

  /// 清理资源
  void dispose() {
    _trackingStatsController.close();
  }
}
