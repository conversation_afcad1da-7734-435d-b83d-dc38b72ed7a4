import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

/// 原生相机视图服务
class CameraViewService extends ChangeNotifier {
  static const MethodChannel _channel = MethodChannel('camera_view');

  // 返回回调
  VoidCallback? _onBackPressed;

  bool _isInitialized = false;
  bool _isRecording = false;
  bool _isTracking = false;
  bool _isPaused = false;
  String _currentResolution = 'hd1080';
  String _currentFolder = "";

  int _frameRate = 30;
  bool _isPortraitMode = true;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isRecording => _isRecording;
  bool get isTracking => _isTracking;
  String get currentResolution => _currentResolution;
  int get frameRate => _frameRate;
  bool get isPortraitMode => _isPortraitMode;

  /// 初始化相机
  Future<void> initialize() async {
    try {
      await _channel.invokeMethod('initialize');
      _isInitialized = true;
      notifyListeners();
      _currentFolder = getCurrentFolderName();
      print('Camera initialized successfully');
    } catch (e) {
      print('Error initializing camera: $e');
    }
  }

  String getCurrentFolderName() {
    final formatter = DateFormat('yyyyMMdd_HHmmss');
    final timestamp = formatter.format(DateTime.now());
    final fileName = 'video_$timestamp';
    return fileName;
  }

  /// 开始录制
  Future<void> startRecording() async {
    if (!_isInitialized) return;

    try {
      await _channel
          .invokeMethod('startRecording', {'folderName': _currentFolder});
      _isRecording = true;
      notifyListeners();
      print('Recording started');
    } catch (e) {
      print('Error starting recording: $e');
    }
  }

  /// 停止录制
  Future<void> stopRecording() async {
    if (!_isInitialized) return;

    try {
      await _channel.invokeMethod('stopRecording');
      _isRecording = false;
      notifyListeners();
      print('Recording stopped');
    } catch (e) {
      print('Error stopping recording: $e');
    }
  }

  /// 暂停录制
  Future<void> pauseRecording() async {
    if (!_isInitialized) return;

    try {
      await _channel.invokeMethod('pauseRecording');
      _isPaused = true;
      notifyListeners();
      print('Recording paused');
    } catch (e) {
      print('Error paused recording: $e');
    }
  }

  /// 继续录制
  Future<void> continueRecording() async {
    if (!_isInitialized) return;

    try {
      await _channel.invokeMethod('continueRecording');
      _isPaused = false;
      notifyListeners();
      print('Recording continue');
    } catch (e) {
      print('Error continue recording: $e');
    }
  }

  /// 设置竖屏模式
  Future<void> setPortraitMode(bool isPortrait) async {
    if (!_isInitialized) return;

    try {
      await _channel
          .invokeMethod('setPortraitMode', {'isPortrait': isPortrait});
      _isPortraitMode = isPortrait;
      notifyListeners();
      print('Portrait mode set to: $isPortrait');
    } catch (e) {
      print('Error setting portrait mode: $e');
    }
  }

  /// 拍照
  Future<void> takePhoto() async {
    if (!_isInitialized) return;

    try {
      await _channel.invokeMethod('takePhoto');
      print('Photo taken');
    } catch (e) {
      print('Error taking photo: $e');
    }
  }

  /// 设置分辨率
  Future<void> setResolution(String resolution) async {
    if (!_isInitialized) return;

    try {
      await _channel.invokeMethod('setResolution', {'resolution': resolution});
      _currentResolution = resolution;
      notifyListeners();
      print('Resolution set to: $resolution');
    } catch (e) {
      print('Error setting resolution: $e');
    }
  }

  /// 设置帧率
  Future<void> setFrameRate(int frameRate) async {
    if (!_isInitialized) return;

    try {
      await _channel.invokeMethod('setFrameRate', {'frameRate': frameRate});
      _frameRate = frameRate;
      notifyListeners();
      print('Frame rate set to: $frameRate');
    } catch (e) {
      print('Error setting frame rate: $e');
    }
  }

  /// 设置竖屏模式
  Future<void> setPortraitMode(bool isPortrait) async {
    if (!_isInitialized) return;

    try {
      await _channel
          .invokeMethod('setPortraitMode', {'isPortrait': isPortrait});
      _isPortraitMode = isPortrait;
      notifyListeners();
      print('Portrait mode set to: $isPortrait');
    } catch (e) {
      print('Error setting portrait mode: $e');
    }
  }

  /// 开始追踪
  Future<void> startTracking() async {
    if (!_isInitialized) return;

    try {
      await _channel.invokeMethod('startTracking');
      _isTracking = true;
      notifyListeners();
      print('Tracking started');
    } catch (e) {
      print('Error starting tracking: $e');
    }
  }

  /// 停止追踪
  Future<void> stopTracking() async {
    if (!_isInitialized) return;

    try {
      await _channel.invokeMethod('stopTracking');
      _isTracking = false;
      notifyListeners();
      print('Tracking stopped');
    } catch (e) {
      print('Error stopping tracking: $e');
    }
  }

  /// 清除分数
  Future<void> clearScores() async {
    if (!_isInitialized) return;

    try {
      await _channel.invokeMethod('clearScores');
      print('Scores cleared');
    } catch (e) {
      print('Error clearing scores: $e');
    }
  }

  /// 设置返回回调
  void setOnBackPressed(VoidCallback? callback) {
    _onBackPressed = callback;
  }

  /// 处理返回事件
  void _handleBackPressed() {
    print('Dart: _handleBackPressed called');
    print(
        'Dart: _onBackPressed is ${_onBackPressed != null ? "not null" : "null"}');
    try {
      _onBackPressed?.call();
      print('Dart: onBackPressed callback executed successfully');
    } catch (e) {
      print('Dart: Error executing onBackPressed callback: $e');
    }
  }

  /// 初始化方法通道监听
  void initializeMethodChannel() {
    // 先清除之前的监听器，避免重复设置
    _channel.setMethodCallHandler(null);

    // 重新设置监听器
    _channel.setMethodCallHandler((call) async {
      print('Received method call: ${call.method}');
      try {
        switch (call.method) {
          case 'onBackPressed':
            print('Handling back pressed event');
            _handleBackPressed();
            return null; // 成功处理，返回null
          default:
            print('Unknown method: ${call.method}');
            throw PlatformException(
              code: 'UNKNOWN_METHOD',
              message: 'Unknown method: ${call.method}',
            );
        }
      } catch (e) {
        print('Error handling method call: $e');
        throw PlatformException(
          code: 'HANDLER_ERROR',
          message: 'Error handling method call: $e',
        );
      }
    });
  }

  /// 清理方法通道监听
  @override
  void dispose() {
    _channel.setMethodCallHandler(null);
    _onBackPressed = null;
    super.dispose();
  }
}
