import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/ptz/config/ptz_dependencies.dart';
import 'package:shoot_z/pages/tab3Create/ptz/native_camera_screen_logic.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/gimbal_service.dart';

class NativeCameraScreenBinding extends Bindings {
  @override
  void dependencies() async {
    // 确保 PTZ 依赖已初始化
    if (!Get.isRegistered<GimbalService>()) {
      await PtzDependencies.init();
    }

    // 注册控制器
    Get.lazyPut(() => NativeCameraScreenLogic());
  }
}
