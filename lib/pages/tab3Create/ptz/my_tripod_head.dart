import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3Create/ptz/my_tripod_head_logic.dart';
import 'package:shoot_z/pages/tab3Create/ptz/ptz_binding/ptz_binding_view.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/BluetoothService.dart';
import 'package:ui_packages/ui_packages.dart';
// import 'package:permission_handler/permission_handler.dart';

class MyTripodHead extends StatelessWidget {
  MyTripodHead({super.key});
  final logic = Get.put(MyTripodHeadLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            child: WxAssets.images.pageTopBg.image(
                width: ScreenUtil().screenWidth,
                height: 260.w,
                fit: BoxFit.fitWidth),
          ),
          Column(
            children: [
              _topBar(context),
              SizedBox(
                height: 159.w,
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () => AppPage.to(Routes.nativeCameraScreen),
                child: Container(
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: WxAssets.images.aiBg.provider(),
                          fit: BoxFit.fill)),
                  width: double.infinity,
                  margin: EdgeInsets.symmetric(horizontal: 15.w),
                  padding:
                      EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.w),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ShaderMask(
                              shaderCallback: (bounds) => const LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [
                                  Colours.colorFFECC1,
                                  Colours.colorE7CEFF,
                                  Colours.colorD1EAFF,
                                ],
                              ).createShader(bounds),
                              child: Text(
                                '普通拍摄',
                                style: TextStyles.display12.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16.sp),
                              ),
                            ),
                            SizedBox(
                              height: 10.w,
                            ),
                            Text(
                              '半场竖屏拍摄和全场横屏拍摄',
                              style: TextStyles.display12.copyWith(
                                  color: Colours.colorA8A8BC, fontSize: 12.sp),
                            ),
                          ],
                        ),
                      ),
                      WxAssets.images.nomalShoot
                          .image(width: 78.w, height: 60.w),
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 15.w,
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () => AppPage.to(Routes.competitionListPage),
                child: Container(
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: WxAssets.images.aiBg.provider(),
                          fit: BoxFit.fill)),
                  width: double.infinity,
                  margin: EdgeInsets.symmetric(horizontal: 15.w),
                  padding:
                      EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.w),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ShaderMask(
                              shaderCallback: (bounds) => const LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [
                                  Colours.colorFFECC1,
                                  Colours.colorE7CEFF,
                                  Colours.colorD1EAFF,
                                ],
                              ).createShader(bounds),
                              child: Text(
                                '赛事管理',
                                style: TextStyles.display12.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16.sp),
                              ),
                            ),
                            SizedBox(
                              height: 10.w,
                            ),
                            Text(
                              '可创建赛事，智能生成赛事报告',
                              style: TextStyles.display12.copyWith(
                                  color: Colours.colorA8A8BC, fontSize: 12.sp),
                            ),
                          ],
                        ),
                      ),
                      WxAssets.images.ai6.image(width: 78.w, height: 60.w),
                    ],
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _topBar(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(top: 4.w),
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 60.w,
            padding: EdgeInsets.only(
              left: 8.w,
              right: 0.w,
            ),
            child: IconButton(
              icon: Icon(
                Icons.arrow_back_ios,
                size: 20.w,
                color: Colors.white,
              ),
              onPressed: () {
                AppPage.back();
              },
            ),
          ),
          Text(
            S.current.my_tripod_head,
            style: TextStyles.titleSemiBold16,
          ),
          Container(
            width: 60.w,
            padding: EdgeInsets.only(left: 0.w, right: 8.w),
          ),
        ],
      ),
    );
  }
}
