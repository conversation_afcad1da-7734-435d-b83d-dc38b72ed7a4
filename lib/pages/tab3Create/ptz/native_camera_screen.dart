import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3Create/ptz/native_camera_screen_logic.dart';
import 'package:shoot_z/pages/tab3Create/ptz/model/connection_status.dart';
import 'package:shoot_z/pages/tab3Create/ptz/widget/native_camera_view.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

/// 原生相机界面 - 使用iOS原生CameraView
class NativeCameraScreen extends StatelessWidget {
  final VoidCallback? onBack;
  final logic = Get.put(NativeCameraScreenLogic());
  NativeCameraScreen({
    super.key,
    this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: Colors.black,
      body: Obx(() {
        return Stack(
          children: [
            // 原生相机视图
            NativeCameraView(
              width: double.infinity,
              height: double.infinity,
              onCameraReady: () {
                log('Native camera ready');
              },
            ),

            // Flutter UI覆盖层
            // _buildFlutterOverlay(logic),
            Positioned(
                top: 0, left: 0, right: 0, child: _buildTopControls(false)),
            Positioned(
                bottom: 0, left: 0, right: 0, child: _buildBottomControls()),
            Center(
              child:
                  logic.operationStep.value == InstructionsSteps.effectPreview
                      ? _buildFirstView()
                      : logic.operationStep.value ==
                              InstructionsSteps.recommendedPosition
                          ? _buildRecommandPosition()
                          : logic.operationStep.value ==
                                  InstructionsSteps.limitPositionsTips
                              ? _buildLimitPositionsTips()
                              : logic.operationStep.value ==
                                      InstructionsSteps.leftLimitSetting
                                  ? _buildLeftLimit()
                                  : logic.operationStep.value ==
                                          InstructionsSteps.rightLimitSetting
                                      ? _buildRightLimit()
                                      : const SizedBox(),
            ),
            if (logic.showSetting.value) _buildSettingView(),
            if (logic.isPaused)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    alignment: Alignment.center,
                    width: 80.w,
                    height: 30.w,
                    margin: EdgeInsets.only(top: 110.w),
                    decoration: BoxDecoration(
                        color: Colours.colorFF3F3F,
                        borderRadius: BorderRadius.all(Radius.circular(15.r))),
                    child: Text(
                      '已暂停',
                      style: TextStyles.display12.copyWith(color: Colors.white),
                    ),
                  ),
                ],
              ),
            if (logic.showLeftLimitSetting.value)
              Center(
                child: _buildLeftLimit(),
              ),
            if (logic.showRightLimitSetting.value)
              Center(
                child: _buildRightLimit(),
              )
          ],
        );
      }),
    );
  }

  Widget _buildSettingView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          margin: EdgeInsets.only(left: 20.w, right: 20.w, top: 100.w),
          width: ScreenUtil().screenWidth - 30.w,
          padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 20.w),
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Column(
            children: [
              Text(
                "云台设置",
                style: TextStyles.titleSemiBold16,
              ),
              SizedBox(
                height: 24.w,
              ),
              // SizedBox(
              //     height: 50.w,
              //     child: Row(
              //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //       children: [
              //         Text(
              //           "云台提示音与振动",
              //           style: TextStyles.semiBold14,
              //         ),
              //         WxAssets.images.switchOff.image()
              //       ],
              //     )),
              // const Divider(
              //   color: Colours.color1AFFFFFF,
              //   height: 0,
              //   thickness: 0.5,
              // ),
              InkWell(
                onTap: () {
                  logic.showLeftLimitSetting.value = true;
                  logic.showSetting.value = false;
                },
                child: SizedBox(
                  height: 50.w,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "云台限位设置",
                        style: TextStyles.semiBold14,
                      ),
                      WxAssets.images.icArrowRight.image(color: Colors.white)
                    ],
                  ),
                ),
              ),
              // const Divider(
              //   color: Colours.color1AFFFFFF,
              //   height: 0,
              //   thickness: 0.5,
              // ),
              // SizedBox(
              //   height: 50.w,
              //   child: Row(
              //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //     children: [
              //       Text(
              //         "恢复出厂设置",
              //         style: TextStyles.semiBold14,
              //       ),
              //       WxAssets.images.icArrowRight.image(color: Colors.white)
              //     ],
              //   ),
              // ),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildFirstView() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: ScreenUtil().screenWidth - 80.w,
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Column(
            children: [
              WxAssets.images.portraitExampleImage.image(),
              SizedBox(
                height: 24.w,
              ),
              Text(
                "半场竖屏模式效果预览",
                style: TextStyles.display12,
              )
            ],
          ),
        ),
        SizedBox(
          height: 20.w,
        ),
        TextButton(
          onPressed: () async {
            logic.nextTimeNoPop.value = !logic.nextTimeNoPop.value;
            await WxStorage.instance
                .setBool("rememberCameraShotOption", logic.nextTimeNoPop.value);
          },
          style: TextButton.styleFrom(
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            padding: EdgeInsets.zero,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              logic.nextTimeNoPop.value
                  ? WxAssets.images.selectIcon.image()
                  : WxAssets.images.unselectIcon.image(),
              SizedBox(width: 6.w),
              Text("下次不再弹出",
                  style: TextStyles.regular.copyWith(fontSize: 12.sp)),
            ],
          ),
        ),
        SizedBox(
          height: 20.w,
        ),
        InkWell(
            onTap: () => logic.operationStep.value =
                InstructionsSteps.recommendedPosition,
            child: Container(
              width: 200.w,
              height: 50.w,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Colours.color7732ED, Colours.colorA555EF],
                    begin: Alignment.bottomLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(25.r)),
              child: Text(
                '开始拍摄',
                style: TextStyles.semiBold14,
              ),
            ))
      ],
    );
  }

  Widget _buildRecommandPosition() {
    return Column(mainAxisAlignment: MainAxisAlignment.center, children: [
      Container(
        width: ScreenUtil().screenWidth - 30.w,
        padding: EdgeInsets.all(15.w),
        decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          children: [
            Text(
              textAlign: TextAlign.center,
              "半场拍摄前请尽量将云台置于推荐位置以保证最佳拍摄效果",
              style: TextStyles.semiBold14.copyWith(height: 1.71),
            ),
            SizedBox(
              height: 30.w,
            ),
            WxAssets.images.recommandPositionImage.image(),
            SizedBox(
              height: 17.w,
            ),
          ],
        ),
      ),
      SizedBox(
        height: 42.w,
      ),
      InkWell(
          onTap: () =>
              logic.operationStep.value = InstructionsSteps.limitPositionsTips,
          child: Container(
            width: 200.w,
            height: 50.w,
            alignment: Alignment.center,
            decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colours.color7732ED, Colours.colorA555EF],
                  begin: Alignment.bottomLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25.r)),
            child: Text(
              '知道了',
              style: TextStyles.semiBold14,
            ),
          ))
    ]);
  }

  Widget _buildLimitPositionsTips() {
    return Column(mainAxisAlignment: MainAxisAlignment.center, children: [
      Container(
        width: ScreenUtil().screenWidth - 30.w,
        padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 20.w),
        decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Text(
          textAlign: TextAlign.center,
          "在拍摄前，请预先设置云台的左右限位。限位点定义了云台的最大旋转角度，以确保拍摄采集的最佳效果",
          style: TextStyles.semiBold14.copyWith(height: 1.71),
        ),
      ),
      SizedBox(
        height: 28.w,
      ),
      InkWell(
          onTap: () =>
              logic.operationStep.value = InstructionsSteps.leftLimitSetting,
          child: Container(
            width: 200.w,
            height: 50.w,
            alignment: Alignment.center,
            decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colours.color7732ED, Colours.colorA555EF],
                  begin: Alignment.bottomLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25.r)),
            child: Text(
              '开始设置',
              style: TextStyles.semiBold14,
            ),
          ))
    ]);
  }

  Widget _buildLeftLimit() {
    return Column(mainAxisAlignment: MainAxisAlignment.center, children: [
      Container(
        width: ScreenUtil().screenWidth - 30.w,
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.w),
        decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          children: [
            Text(
              textAlign: TextAlign.center,
              "请通过云台遥控器向左移动云台，选择起始位置 后点击“下一步",
              style: TextStyles.semiBold14.copyWith(height: 1.71),
            ),
            SizedBox(
              height: 20.w,
            ),
            WxAssets.images.leftLimit.image(),
          ],
        ),
      ),
      SizedBox(
        height: 28.w,
      ),
      InkWell(
          onTap: () {
            logic.setLeftLimit();
            if (logic.showLeftLimitSetting.value) {
              logic.showLeftLimitSetting.value = false;
              logic.showRightLimitSetting.value = true;
              return;
            }
            logic.operationStep.value = InstructionsSteps.rightLimitSetting;
            logic.takePhoto();
          },
          child: Container(
            width: 200.w,
            height: 50.w,
            alignment: Alignment.center,
            decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colours.color7732ED, Colours.colorA555EF],
                  begin: Alignment.bottomLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25.r)),
            child: Text(
              '下一步',
              style: TextStyles.semiBold14,
            ),
          ))
    ]);
  }

  Widget _buildRightLimit() {
    return Column(mainAxisAlignment: MainAxisAlignment.center, children: [
      Container(
        width: ScreenUtil().screenWidth - 30.w,
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.w),
        decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          children: [
            Text(
              textAlign: TextAlign.center,
              "继续用遥控器向右移动云台，选择目标位置后点击“确认”完成设置",
              style: TextStyles.semiBold14.copyWith(height: 1.71),
            ),
            SizedBox(
              height: 20.w,
            ),
            WxAssets.images.rightLimit.image(),
          ],
        ),
      ),
      SizedBox(
        height: 28.w,
      ),
      InkWell(
          onTap: () {
            logic.setRightLimit();
            if (logic.showRightLimitSetting.value) {
              logic.showRightLimitSetting.value = false;
              return;
            }
            logic.operationStep.value = InstructionsSteps.recordingStart;
            logic.takePhoto();
          },
          child: Container(
            width: 200.w,
            height: 50.w,
            alignment: Alignment.center,
            decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colours.color7732ED, Colours.colorA555EF],
                  begin: Alignment.bottomLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25.r)),
            child: Text(
              '确定',
              style: TextStyles.semiBold14,
            ),
          ))
    ]);
  }

  Widget _buildTopControls(bool isLandscape) {
    return Container(
      height: ScreenUtil().statusBarHeight + 40.w,
      width: double.infinity,
      alignment: Alignment.center,
      color: Colours.colorCC0F0F16,
      padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          WxAssets.images.cameraSetting1080.image(),
          // InkWell(
          //     onTap: () {
          //       //直播
          //     },
          //     child: WxAssets.images.cameraSettingRecord.image()),
          InkWell(
              onTap: () {
                if (logic.isRecording) {
                  getMyDialog(
                    '结束拍摄',
                    S.current.sure,
                    content: '确定要结束拍摄吗？结束后视频不会保存',
                    () {
                      AppPage.back();
                      AppPage.back();
                    },
                    isShowClose: false,
                    btnIsHorizontal: true,
                    btnText2: S.current.cancel,
                    onPressed2: () {
                      AppPage.back();
                    },
                  );
                } else {
                  AppPage.back();
                }
              },
              child: WxAssets.images.cameraSettingHome.image()),
          InkWell(
              onTap: () {
                logic.showSetting.value = !logic.showSetting.value;
              },
              child: logic.showSetting.value
                  ? WxAssets.images.cameraSettingSet
                      .image(color: Colours.color7732ED)
                  : WxAssets.images.cameraSettingSet.image())
        ],
      ),
    );
  }

  Widget _buildBottomControls() {
    return Column(
      children: [
        if (!logic.isRecording &&
            logic.operationStep.value == InstructionsSteps.recordingStart)
          InkWell(
              onTap: () {
                log('开始录制');
                logic.toggleRecording();
              },
              child: WxAssets.images.recordingIcon.image()),
        if (logic.isRecording)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Opacity(
                opacity: 0,
                child: WxAssets.images.finishRecordingIcon.image(),
              ),
              InkWell(
                  onTap: () {
                    // print('暂停录制');
                    logic.togglePaused();
                  },
                  child: logic.isPaused
                      ? WxAssets.images.recordingIcon.image()
                      : WxAssets.images.recordingStopIcon.image()),
              InkWell(
                  onTap: () {
                    getMyDialog(
                      '结束拍摄',
                      S.current.sure,
                      content: '确定要结束拍摄吗？',
                      () {
                        AppPage.back();
                        logic.toggleRecording();
                      },
                      isShowClose: false,
                      btnIsHorizontal: true,
                      btnText2: S.current.cancel,
                      onPressed2: () {
                        AppPage.back();
                      },
                    );
                  },
                  child: WxAssets.images.finishRecordingIcon.image()),
            ],
          ).marginSymmetric(horizontal: 20.w),
        SizedBox(
          height: 20.w,
        ),
        Container(
          height: 72.w,
          width: double.infinity,
          alignment: Alignment.center,
          color: Colours.colorCC0F0F16,
          padding: EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight),
          child: logic.isRecording
              ? Text(
                  logic.recordingTime,
                  style: TextStyles.numberDin16,
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        // logic.setGimbalDirection(true);
                        log("!!!!!!!!${logic.gimbalManager.connectionStatus}");
                        logic.setGimbalDirection(true);
                      },
                      child: Text(
                        '半场竖屏',
                        style: TextStyles.titleSemiBold16,
                      ),
                    ),
                    SizedBox(
                      width: 20.w,
                    ),
                    InkWell(
                        onTap: () {
                          logic.setGimbalDirection(false);
                        },
                        child: Text(
                          '全场横屏',
                          style: TextStyles.regular,
                        )),
                  ],
                ),
        ),
      ],
    );
  }
}
