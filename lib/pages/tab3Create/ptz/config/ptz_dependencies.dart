import 'dart:developer';

import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/ptz/service/gimbal_service.dart';

/// PTZ 相关依赖注入配置
class PtzDependencies {
  /// 初始化所有 PTZ 相关的依赖
  static Future<void> init() async {
    // 检查是否已经注册
    if (Get.isRegistered<GimbalService>()) {
      log('PTZ Dependencies already initialized');
      return;
    }
    // 注册 GimbalService 为单例服务
    // 使用 Get.putAsync 确保服务在需要时才初始化
    await Get.putAsync<GimbalService>(() async {
      final service = GimbalService();
      service.onInit();
      return service;
    }, permanent: true);

    log('PTZ Dependencies initialized');
  }

  /// 清理所有依赖
  static void dispose() {
    if (Get.isRegistered<GimbalService>()) {
      Get.delete<GimbalService>();
    }
    log('PTZ Dependencies disposed');
  }
}
