import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class NativeCameraView extends StatefulWidget {
  final double? width;
  final double? height;
  final VoidCallback? onCameraReady;

  const NativeCameraView({
    super.key,
    this.width,
    this.height,
    this.onCameraReady,
  });

  @override
  State<NativeCameraView> createState() => _NativeCameraViewState();
}

class _NativeCameraViewState extends State<NativeCameraView> {
  static const platform = MethodChannel('camera_view');

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    try {
      await platform.invokeMethod('initialize');
      widget.onCameraReady?.call();
    } catch (e) {
      print('Error initializing camera: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width ?? double.infinity,
      height: widget.height ?? double.infinity,
      child: Ui<PERSON>itView(
        viewType: 'camera_view',
        creationParams: <String, dynamic>{},
        creationParamsCodec: const StandardMessageCodec(),
      ),
    );
  }
}
